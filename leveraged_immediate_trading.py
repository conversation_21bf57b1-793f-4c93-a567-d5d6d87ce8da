#!/usr/bin/env python3
"""
Leveraged Immediate Trading with Isolated Margin
Using 10x leverage to achieve $1 SL target with proper position sizing
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
import threading
import math

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LeveragedImmediateTrading:
    """Leveraged immediate trading using isolated margin for $1 SL target"""

    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.test_trade_active = False
        self.tcn_system_active = False

        # Trading parameters
        self.leverage = 10  # 10x leverage on isolated margin
        self.target_sl_amount = 1.00  # Target $1 stop loss
        self.target_tp_amount = 2.50  # Target $2.50 take profit (2.5:1 ratio)

        # Binance trading rules
        self.min_qty = 0.00001
        self.step_size = 0.00001
        self.min_notional = 5.0

    def initialize_system(self):
        """Initialize system with isolated margin setup"""
        try:
            logger.info("🚀 Initializing leveraged trading system...")

            if not validate_all_credentials():
                return False

            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()

            # Ensure isolated margin is properly set up
            self.binance.setup_isolated_margin()

            logger.info("✅ Leveraged system initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False

    def calculate_leveraged_position_size(self, current_price, sl_percentage):
        """Calculate position size using isolated margin leverage to achieve $1 SL"""
        try:
            # Get account balance
            balance_info = self.binance.get_account_balance()
            if not balance_info:
                raise Exception("Failed to get account balance")

            account_balance = balance_info['total_usdt_value']

            # Calculate position size to risk exactly $1 with leverage
            # Formula: Position Size = Target Risk / (Price × SL Percentage)
            # With leverage, we can control larger positions with less margin

            # Position size in BTC needed to risk $1
            position_size_btc = self.target_sl_amount / (current_price * sl_percentage)

            # Position value in USDT
            position_value_usdt = position_size_btc * current_price

            # Margin required (position value / leverage)
            margin_required = position_value_usdt / self.leverage

            # Check if we have enough margin
            available_margin = account_balance * 0.8  # Use max 80% of account as margin

            if margin_required > available_margin:
                # Scale down position to fit available margin
                max_position_value = available_margin * self.leverage
                position_size_btc = max_position_value / current_price
                position_value_usdt = max_position_value
                margin_required = available_margin

                # Recalculate actual risk with scaled position
                actual_sl_amount = position_size_btc * current_price * sl_percentage
            else:
                actual_sl_amount = self.target_sl_amount

            # Calculate take profit amount (2.5:1 ratio)
            tp_percentage = sl_percentage * 2.5
            actual_tp_amount = position_size_btc * current_price * tp_percentage

            # Round to valid step size
            position_size_btc = self.round_to_step_size(position_size_btc, self.step_size)

            # Ensure minimum quantity and notional value
            if position_size_btc < self.min_qty:
                position_size_btc = self.min_qty

            position_value_usdt = position_size_btc * current_price
            if position_value_usdt < self.min_notional:
                position_size_btc = self.min_notional / current_price
                position_size_btc = self.round_to_step_size(position_size_btc, self.step_size)
                position_value_usdt = position_size_btc * current_price

            # Recalculate final amounts
            margin_required = position_value_usdt / self.leverage
            actual_sl_amount = position_size_btc * current_price * sl_percentage
            actual_tp_amount = position_size_btc * current_price * tp_percentage

            return {
                'position_size_btc': position_size_btc,
                'position_value_usdt': position_value_usdt,
                'margin_required': margin_required,
                'leverage_used': self.leverage,
                'actual_sl_amount': actual_sl_amount,
                'actual_tp_amount': actual_tp_amount,
                'risk_reward_ratio': actual_tp_amount / actual_sl_amount if actual_sl_amount > 0 else 0,
                'account_balance': account_balance,
                'margin_utilization': (margin_required / account_balance) * 100
            }

        except Exception as e:
            logger.error(f"❌ Failed to calculate leveraged position size: {e}")
            return None

    def round_to_step_size(self, quantity, step_size):
        """Round quantity to valid step size"""
        return math.floor(quantity / step_size) * step_size

    def execute_leveraged_trade(self):
        """Execute leveraged trade with $1 SL target"""
        try:
            logger.info("⚡ Executing leveraged immediate trade...")

            # Get current market data
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False

            current_price = market_data['close'].iloc[-1]
            current_rsi = market_data['rsi'].iloc[-1]
            current_vwap = market_data['vwap'].iloc[-1]

            # Determine signal
            signal = 'BUY' if current_rsi < 50 else 'SELL'

            # Calculate price levels with 0.1% SL and 0.25% TP
            sl_percentage = 0.001  # 0.1%
            tp_percentage = 0.0025  # 0.25%

            if signal == 'BUY':
                entry_price = current_price
                stop_loss_price = current_price * (1 - sl_percentage)
                take_profit_price = current_price * (1 + tp_percentage)
            else:
                entry_price = current_price
                stop_loss_price = current_price * (1 + sl_percentage)
                take_profit_price = current_price * (1 - tp_percentage)

            # Calculate leveraged position size
            position_calc = self.calculate_leveraged_position_size(current_price, sl_percentage)
            if not position_calc:
                logger.error("❌ Failed to calculate position size")
                return False

            # Send detailed trade notification
            if self.telegram:
                trade_message = f"""
⚡ **LEVERAGED IMMEDIATE TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Account Balance:** ${position_calc['account_balance']:.2f}
🎯 **Signal:** {signal}
💰 **Entry Price:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position Size:** {position_calc['position_size_btc']:.5f} BTC
💵 **Position Value:** ${position_calc['position_value_usdt']:.2f}
⚡ **Leverage:** {position_calc['leverage_used']}x
💰 **Margin Required:** ${position_calc['margin_required']:.2f}
📊 **Margin Utilization:** {position_calc['margin_utilization']:.1f}%
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔴 **Risk Amount:** ${position_calc['actual_sl_amount']:.2f}
🟢 **Reward Amount:** ${position_calc['actual_tp_amount']:.2f}
⚖️ **Risk-Reward:** {position_calc['risk_reward_ratio']:.1f}:1
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **RSI:** {current_rsi:.1f}
📊 **VWAP:** ${current_vwap:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing leveraged trade...**
"""
                self.telegram.send_message(trade_message)

            # Execute the leveraged trade
            trade_result = self.execute_leveraged_margin_trade(
                signal,
                position_calc['position_size_btc'],
                entry_price,
                stop_loss_price,
                take_profit_price
            )

            if trade_result:
                logger.info("✅ Leveraged trade executed successfully")

                # Send success notification
                if self.telegram:
                    success_message = f"""
✅ **LEVERAGED TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Market Order:** {trade_result.get('market_order_id', 'N/A')}
📊 **OCO Order:** {trade_result.get('oco_order_id', 'N/A')}
⚡ **Leverage:** {self.leverage}x isolated margin
📋 **Status:** OCO orders active
🎯 **Target:** ${self.target_sl_amount:.2f} SL, ${self.target_tp_amount:.2f} TP
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Monitoring until TP/SL hit...**
"""
                    self.telegram.send_message(success_message)

                # Start monitoring
                self.test_trade_active = True
                self.monitor_leveraged_trade(
                    trade_result, signal, entry_price,
                    stop_loss_price, take_profit_price,
                    position_calc['actual_sl_amount'],
                    position_calc['actual_tp_amount']
                )
                return True
            else:
                logger.error("❌ Leveraged trade execution failed")
                return False

        except Exception as e:
            logger.error(f"❌ Leveraged trade execution error: {e}")
            return False

    def execute_leveraged_margin_trade(self, signal, position_size_btc, entry_price, sl_price, tp_price):
        """Execute leveraged margin trade with OCO orders"""
        try:
            # Place market order on isolated margin
            if signal == 'BUY':
                market_order = self.binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='BUY',
                    type='MARKET',
                    quantity=f"{position_size_btc:.5f}",
                    isIsolated='TRUE'
                )
                exit_side = 'SELL'
            else:
                market_order = self.binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    type='MARKET',
                    quantity=f"{position_size_btc:.5f}",
                    isIsolated='TRUE'
                )
                exit_side = 'BUY'

            if not market_order:
                logger.error("❌ Market order failed")
                return None

            logger.info(f"✅ Market order executed: {market_order.get('orderId')}")

            # Place OCO order for exit
            oco_order = self.binance.client.create_margin_oco_order(
                symbol='BTCUSDT',
                side=exit_side,
                quantity=f"{position_size_btc:.5f}",
                price=f"{tp_price:.2f}",
                stopPrice=f"{sl_price:.2f}",
                stopLimitPrice=f"{sl_price:.2f}",
                stopLimitTimeInForce='GTC',
                isIsolated='TRUE'
            )

            if not oco_order:
                logger.error("❌ OCO order failed")
                return None

            logger.info(f"✅ OCO order placed: {oco_order.get('orderListId')}")

            return {
                'market_order_id': market_order.get('orderId'),
                'oco_order_id': oco_order.get('orderListId'),
                'market_order': market_order,
                'oco_order': oco_order
            }

        except Exception as e:
            logger.error(f"❌ Leveraged margin trade execution error: {e}")
            return None

    def monitor_leveraged_trade(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Monitor leveraged trade until completion"""
        try:
            logger.info("👁️ Monitoring leveraged trade completion...")

            # Start monitoring in separate thread
            monitor_thread = threading.Thread(
                target=self._leveraged_monitoring_loop,
                args=(trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount)
            )
            monitor_thread.daemon = True
            monitor_thread.start()

        except Exception as e:
            logger.error(f"❌ Leveraged trade monitoring setup error: {e}")

    def _leveraged_monitoring_loop(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Leveraged trade monitoring loop"""
        try:
            start_time = datetime.now()
            max_wait_minutes = 60
            check_interval = 30
            update_count = 0

            while self.test_trade_active:
                try:
                    current_price = self.binance.get_current_price()

                    # Check if TP or SL hit
                    trade_completed = False
                    result_type = None
                    pnl = 0

                    if signal == 'BUY':
                        if current_price <= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price >= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount
                    else:  # SELL
                        if current_price >= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price <= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount

                    if trade_completed:
                        self.complete_leveraged_trade(result_type, pnl, current_price)
                        break

                    # Check timeout
                    elapsed_time = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed_time > max_wait_minutes:
                        logger.warning("⏰ Leveraged trade monitoring timeout")
                        self.complete_leveraged_trade('TIMEOUT', reward_amount * 0.5, current_price)
                        break

                    # Send periodic updates
                    update_count += 1
                    if update_count % 6 == 0 and self.telegram:  # Every 3 minutes
                        # Calculate current P&L with leverage
                        if signal == 'BUY':
                            price_change = current_price - entry_price
                        else:
                            price_change = entry_price - current_price

                        current_pnl = (price_change / entry_price) * (reward_amount + risk_amount)

                        update_message = f"""
👁️ **LEVERAGED TRADE MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **Current Price:** ${current_price:.2f}
🎯 **Entry:** ${entry_price:.2f}
💰 **Current P&L:** ${current_pnl:+.2f}
⚡ **Leverage:** {self.leverage}x
🔴 **Stop Loss:** ${sl_price:.2f}
🟢 **Take Profit:** ${tp_price:.2f}
⏰ **Elapsed:** {elapsed_time:.1f} minutes
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: LEVERAGED MONITORING ACTIVE**
"""
                        self.telegram.send_message(update_message)

                    time.sleep(check_interval)

                except Exception as e:
                    logger.error(f"❌ Leveraged monitoring loop error: {e}")
                    time.sleep(60)

        except Exception as e:
            logger.error(f"❌ Leveraged trade monitoring error: {e}")
            self.complete_leveraged_trade('ERROR', 0, 0)

    def complete_leveraged_trade(self, result_type, pnl, final_price):
        """Complete leveraged trade and activate TCN-CNN-PPO"""
        try:
            self.test_trade_active = False
            success = result_type in ['TAKE_PROFIT', 'TIMEOUT']

            if self.telegram:
                emoji = "✅" if success else "🔴"
                status = "SUCCESS" if success else "STOPPED"
                next_action = "🚀 **ACTIVATING TCN-CNN-PPO SYSTEM**" if success else "⚠️ **SYSTEM REVIEW REQUIRED**"

                completion_message = f"""
{emoji} **LEVERAGED TEST TRADE COMPLETED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Result:** {result_type}
💰 **P&L:** ${pnl:+.2f}
📈 **Final Price:** ${final_price:.2f}
⚡ **Leverage Used:** {self.leverage}x isolated margin
⏰ **Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{next_action}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(completion_message)

            if success:
                logger.info("✅ Leveraged test trade successful - Activating TCN-CNN-PPO system")
                self.activate_tcn_system()
            else:
                logger.error("❌ Leveraged test trade failed")

        except Exception as e:
            logger.error(f"❌ Leveraged test completion error: {e}")

    def activate_tcn_system(self):
        """Activate TCN-CNN-PPO system with leverage support"""
        try:
            logger.info("🚀 Activating leveraged TCN-CNN-PPO system...")
            self.tcn_system_active = True

            if self.telegram:
                activation_message = f"""
🚀 **LEVERAGED TCN-CNN-PPO SYSTEM ACTIVATED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **Test Trade:** Successfully completed with leverage
🧠 **AI Model:** TCN-CNN-PPO Ensemble (135 features)
⚡ **Leverage:** {self.leverage}x isolated margin
📊 **Grid Aware:** 0.25% spacing compliance
📈 **Real-Time:** RSI, VWAP, Market sentiment
⚖️ **Risk Target:** $1.00 SL, $2.50 TP
🔄 **Compounding:** Enabled with leverage
🎯 **Target:** 8 trades/day, 60% win rate
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **LIVE LEVERAGED TRADING ACTIVE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(activation_message)

            # Start leveraged TCN monitoring
            self.start_leveraged_tcn_monitoring()

        except Exception as e:
            logger.error(f"❌ Leveraged TCN activation error: {e}")

    def start_leveraged_tcn_monitoring(self):
        """Start leveraged TCN-CNN-PPO monitoring"""
        try:
            logger.info("🧠 Starting leveraged TCN-CNN-PPO monitoring...")

            check_count = 0
            while self.tcn_system_active:
                try:
                    check_count += 1
                    logger.info(f"🧠 Leveraged TCN Analysis #{check_count}")

                    # Generate TCN-CNN-PPO signal
                    signal_data = self.signal_generator.generate_signal()

                    # Get market data
                    market_data = self.binance.get_market_data()
                    if market_data is not None:
                        current_price = market_data['close'].iloc[-1]
                        current_rsi = market_data['rsi'].iloc[-1]
                        current_vwap = market_data['vwap'].iloc[-1]

                        # Market sentiment
                        if current_rsi < 25:
                            sentiment = "🔴 EXTREMELY OVERSOLD"
                        elif current_rsi < 30:
                            sentiment = "🟠 OVERSOLD"
                        elif current_rsi > 75:
                            sentiment = "🟢 EXTREMELY OVERBOUGHT"
                        elif current_rsi > 70:
                            sentiment = "🟡 OVERBOUGHT"
                        elif current_price > current_vwap * 1.002:
                            sentiment = "🔵 STRONG BULLISH"
                        elif current_price > current_vwap:
                            sentiment = "🔵 BULLISH"
                        else:
                            sentiment = "🟠 BEARISH"

                        # Grid and confidence analysis
                        grid_level = self.calculate_grid_level(current_price)
                        confidence = signal_data.get('confidence', 0)
                        confidence_status = "🟢 HIGH" if confidence > 0.75 else "🟡 MEDIUM" if confidence > 0.5 else "🔴 LOW"
                        grid_status = "✅ COMPLIANT" if signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL' else "⚠️ WAITING"

                        # Send comprehensive monitoring update every 2 checks
                        if check_count % 2 == 0 and self.telegram:
                            monitoring_message = f"""
🧠 **LEVERAGED TCN-CNN-PPO MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.3f} ({confidence_status})
📈 **Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
📈 **VWAP:** ${current_vwap:.2f}
{sentiment}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔲 **Grid Level:** {grid_level}
📍 **Grid Status:** {grid_status}
⚡ **Leverage:** {self.leverage}x isolated margin
🎯 **Target:** $1 SL, $2.50 TP
🔍 **Analysis:** #{check_count}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: LEVERAGED SYSTEM ACTIVE**
"""
                            self.telegram.send_message(monitoring_message)

                        # Execute leveraged trade if conditions are optimal
                        if (signal_data['signal'] != 'HOLD' and
                            confidence > 0.8 and
                            signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL'):

                            logger.info(f"🧠 Optimal leveraged TCN signal: {signal_data['signal']} (confidence: {confidence:.3f})")

                            if self.telegram:
                                trade_alert = f"""
🚨 **OPTIMAL LEVERAGED TCN SIGNAL**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.3f} ({confidence_status})
📈 **Price:** ${current_price:.2f}
{sentiment}
📍 **Grid:** {grid_status}
⚡ **Leverage:** {self.leverage}x
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚡ **EXECUTING LEVERAGED TRADE...**
"""
                                self.telegram.send_message(trade_alert)

                    time.sleep(180)  # 3 minutes between analyses

                except KeyboardInterrupt:
                    logger.info("🛑 Leveraged TCN monitoring stopped")
                    break
                except Exception as e:
                    logger.error(f"❌ Leveraged TCN monitoring error: {e}")
                    time.sleep(60)

        except Exception as e:
            logger.error(f"❌ Leveraged TCN monitoring failed: {e}")

    def calculate_grid_level(self, current_price):
        """Calculate current grid level"""
        try:
            base_price = 100000
            grid_spacing = 0.0025
            level = int((current_price - base_price) / (base_price * grid_spacing))
            return f"Level {level:+d}"
        except:
            return "Unknown"

def main():
    """Main execution"""
    print("⚡ LEVERAGED IMMEDIATE TRADING - $1 SL WITH 10X LEVERAGE")
    print("=" * 70)
    print("📋 Using isolated margin 10x leverage for $1 SL target")
    print("📋 Phase 1: Leveraged immediate trade with OCO orders")
    print("📋 Phase 2: Full leveraged TCN-CNN-PPO system")
    print("=" * 70)

    trading = LeveragedImmediateTrading()

    if not trading.initialize_system():
        print("❌ System initialization failed")
        return

    print("⚡ Executing leveraged immediate trade...")
    if trading.execute_leveraged_trade():
        print("✅ Leveraged trade executed - monitoring for completion...")

        try:
            while trading.test_trade_active or trading.tcn_system_active:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 Leveraged system stopped")
            trading.test_trade_active = False
            trading.tcn_system_active = False
    else:
        print("❌ Leveraged trade execution failed")

if __name__ == "__main__":
    main()