#!/usr/bin/env python3
"""
Check actual model architecture in saved file
"""

import torch

def check_model_architecture():
    """Check the actual architecture in the saved model"""
    try:
        print("🔍 Checking win_rate_optimized_model.pth architecture...")
        
        checkpoint = torch.load('win_rate_optimized_model.pth', map_location='cpu', weights_only=False)
        state_dict = checkpoint['model_state_dict']
        
        print(f"📊 Model keys: {list(checkpoint.keys())}")
        print(f"📊 Validation accuracy: {checkpoint.get('val_accuracy', 'N/A')}")
        
        print("\n🏗️ Model Architecture Analysis:")
        
        # Group layers by component
        tcn_layers = [k for k in state_dict.keys() if k.startswith('tcn.')]
        cnn_layers = [k for k in state_dict.keys() if k.startswith('cnn.')]
        ppo_layers = [k for k in state_dict.keys() if k.startswith('ppo')]
        
        print(f"\n📊 TCN Layers ({len(tcn_layers)}):")
        for layer in tcn_layers:
            shape = state_dict[layer].shape if hasattr(state_dict[layer], 'shape') else 'scalar'
            print(f"   {layer}: {shape}")
        
        print(f"\n📊 CNN Layers ({len(cnn_layers)}):")
        for layer in cnn_layers:
            shape = state_dict[layer].shape if hasattr(state_dict[layer], 'shape') else 'scalar'
            print(f"   {layer}: {shape}")
        
        print(f"\n📊 PPO Layers ({len(ppo_layers)}):")
        for layer in ppo_layers:
            shape = state_dict[layer].shape if hasattr(state_dict[layer], 'shape') else 'scalar'
            print(f"   {layer}: {shape}")
        
        # Check ensemble weights
        if 'ensemble_weights' in state_dict:
            print(f"\n📊 Ensemble Weights: {state_dict['ensemble_weights']}")
        
        # Check classifier layers
        classifier_layers = [k for k in state_dict.keys() if 'classifier' in k]
        print(f"\n📊 Classifier Layers ({len(classifier_layers)}):")
        for layer in classifier_layers:
            shape = state_dict[layer].shape if hasattr(state_dict[layer], 'shape') else 'scalar'
            print(f"   {layer}: {shape}")
        
        return state_dict
        
    except Exception as e:
        print(f"❌ Architecture check failed: {e}")
        return None

def analyze_cnn_issue():
    """Analyze the specific CNN architecture issue"""
    try:
        checkpoint = torch.load('win_rate_optimized_model.pth', map_location='cpu', weights_only=False)
        state_dict = checkpoint['model_state_dict']
        
        print("\n🔍 CNN Architecture Analysis:")
        
        # Check CNN conv layers specifically
        cnn_conv_layers = [k for k in state_dict.keys() if k.startswith('cnn.') and '.weight' in k and 'conv' in k.lower()]
        
        for layer in cnn_conv_layers:
            weight_shape = state_dict[layer].shape
            print(f"   {layer}: {weight_shape}")
            
            # Analyze kernel size from weight shape
            if len(weight_shape) == 3:  # Conv1d: [out_channels, in_channels, kernel_size]
                out_ch, in_ch, kernel_size = weight_shape
                print(f"     -> Out: {out_ch}, In: {in_ch}, Kernel: {kernel_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ CNN analysis failed: {e}")
        return False

def main():
    print("🔍 MODEL ARCHITECTURE CHECKER")
    print("="*50)
    
    state_dict = check_model_architecture()
    if state_dict:
        analyze_cnn_issue()
        print("\n✅ Architecture analysis completed")
    else:
        print("\n❌ Architecture analysis failed")

if __name__ == "__main__":
    main()
