{"telegram_bot": {"bot_token": "YOUR_BOT_TOKEN_HERE", "bot_username": "YourTradingBot", "description": "Enhanced Grid-Aware TCN-CNN-PPO Live Trading Bot", "version": "1.0.0"}, "chat_configuration": {"chat_id": "YOUR_CHAT_ID_HERE", "group_chat_enabled": false, "private_chat_only": true, "chat_backup_id": "BACKUP_CHAT_ID"}, "user_management": {"admin_users": ["YOUR_USER_ID_HERE"], "authorized_users": ["YOUR_USER_ID_HERE"], "user_verification": true, "access_control": true}, "command_configuration": {"commands_enabled": true, "admin_commands_enabled": true, "command_rate_limit": 10, "command_timeout": 30, "help_command": true, "status_command": true}, "notification_settings": {"notifications_enabled": true, "trade_notifications": {"enabled": true, "format": "detailed", "include_charts": false, "immediate_delivery": true}, "performance_notifications": {"enabled": true, "frequency": "hourly", "threshold_alerts": true, "daily_summary": true}, "system_notifications": {"enabled": true, "startup_message": true, "shutdown_message": true, "error_alerts": true, "health_updates": true}, "alert_notifications": {"enabled": true, "critical_alerts": true, "warning_alerts": true, "info_alerts": false, "emergency_alerts": true}}, "message_formatting": {"parse_mode": "HTML", "disable_web_page_preview": true, "disable_notification": false, "message_threading": false, "emoji_enabled": true, "formatting_enabled": true}, "response_settings": {"response_timeout": 10, "retry_attempts": 3, "error_handling": true, "fallback_responses": true, "response_caching": false}, "security_settings": {"webhook_enabled": false, "polling_enabled": true, "ssl_verification": true, "token_encryption": true, "message_encryption": false, "audit_logging": true}, "dashboard_configuration": {"real_time_dashboard": true, "update_frequency": 60, "performance_charts": false, "trade_history_limit": 20, "status_indicators": true, "color_coding": true}, "alert_thresholds": {"win_rate_warning": 0.55, "win_rate_critical": 0.5, "drawdown_warning": 0.05, "drawdown_critical": 0.08, "balance_warning": 0.9, "balance_critical": 0.85, "trades_per_day_warning": 6.0, "trades_per_day_critical": 4.0}, "command_permissions": {"public_commands": ["/start", "/status", "/balance", "/trades", "/performance", "/signals", "/health", "/help"], "admin_commands": ["/pause", "/resume", "/emergency", "/config", "/logs", "/alerts", "/backup", "/restore"], "restricted_commands": ["/emergency", "/shutdown", "/restart"]}, "message_templates": {"startup_message": "🚀 Live Trading Bot Started\n💰 Balance: ${balance}\n🎯 Target: 61.4% win rate, 8.1 trades/day\n⚡ Status: ACTIVE", "shutdown_message": "🛑 Live Trading Bot Stopped\n💰 Final Balance: ${balance}\n📊 Total Trades: ${trades}\n🎯 Final Win Rate: ${win_rate}", "trade_notification": "🚀 TRADE EXECUTED\nSignal: ${signal}\nPrice: $${price}\nSize: ${size} BTC\nConfidence: ${confidence}%\nTime: ${time}", "emergency_alert": "🚨 EMERGENCY ALERT\nType: ${type}\nAction: ${action}\nTime: ${time}\nStatus: ${status}", "performance_update": "📊 PERFORMANCE UPDATE\n💰 Balance: $${balance}\n📈 P&L: $${pnl}\n🎯 Win Rate: ${win_rate}%\n⚡ Trades: ${trades}"}, "polling_configuration": {"polling_interval": 1, "timeout": 10, "allowed_updates": ["message", "callback_query"], "drop_pending_updates": false, "max_connections": 40}, "error_handling": {"retry_on_error": true, "max_retries": 3, "retry_delay": 5, "error_notifications": true, "fallback_mode": true, "graceful_degradation": true}, "logging": {"telegram_logging": true, "log_level": "INFO", "log_file": "telegram_bot.log", "message_logging": true, "command_logging": true, "error_logging": true}, "rate_limiting": {"enabled": true, "messages_per_second": 30, "messages_per_minute": 20, "burst_limit": 5, "cooldown_period": 60}, "backup_configuration": {"backup_chat_enabled": false, "backup_chat_id": "BACKUP_CHAT_ID", "failover_enabled": true, "redundancy_enabled": false}, "integration_settings": {"trading_system_integration": true, "real_time_sync": true, "data_sharing": true, "command_forwarding": true, "status_synchronization": true}, "customization": {"custom_commands": [], "custom_responses": {}, "custom_keyboards": false, "custom_formatting": false, "branding_enabled": false}, "maintenance": {"maintenance_mode": false, "maintenance_message": "🔧 System under maintenance. Please try again later.", "scheduled_maintenance": false, "auto_restart": false}}