#!/usr/bin/env python3
"""
FINAL TEST TRADE EXECUTION
Realistic position sizing based on actual account balance

CRITICAL FIXES:
- Use actual account balance for position sizing
- Maintain 1% risk with available funds
- Proper LOT_SIZE compliance
- Real money test trade execution
"""

import os
import sys
import time
import json
import logging
import decimal
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_test_trade.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalTestTrade:
    """Final test trade with realistic position sizing"""
    
    def __init__(self):
        self.test_status = "INITIALIZING"
        
        # PROVEN MODEL TEST PARAMETERS
        self.stop_loss_percent = 0.001      # 0.1% SL
        self.take_profit_percent = 0.0025   # 0.25% TP
        self.risk_reward_ratio = 2.5        # 2.5:1 RR
        self.risk_percent = 0.01            # 1% risk of account balance
        
        # Binance requirements
        self.lot_size_info = {}
        self.price_filter_info = {}
        self.min_notional = 0.0
        
        logger.info("FINAL TEST TRADE EXECUTOR INITIALIZED")
        logger.info("Using realistic position sizing based on actual balance")
    
    def initialize_connections(self):
        """Initialize connections and get symbol requirements"""
        try:
            logger.info("Initializing connections and symbol requirements...")
            
            # Initialize Binance connector
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test connection
            account_info = self.binance.get_account_info()
            if not account_info:
                raise Exception("Binance connection failed")
            
            # Get isolated margin balance
            balance_info = self.binance.get_isolated_margin_balance()
            if not balance_info:
                raise Exception("Isolated margin access failed")
            
            self.current_balance = balance_info['total_usdt_value']
            
            # Calculate realistic risk amount (1% of actual balance)
            self.risk_amount = self.current_balance * self.risk_percent
            
            # Get symbol requirements
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    self.lot_size_info = {
                        'min_qty': float(filter_item['minQty']),
                        'max_qty': float(filter_item['maxQty']),
                        'step_size': float(filter_item['stepSize'])
                    }
                elif filter_item['filterType'] == 'PRICE_FILTER':
                    self.price_filter_info = {
                        'min_price': float(filter_item['minPrice']),
                        'max_price': float(filter_item['maxPrice']),
                        'tick_size': float(filter_item['tickSize'])
                    }
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    self.min_notional = float(filter_item['minNotional'])
            
            logger.info("ACCOUNT AND SYMBOL REQUIREMENTS:")
            logger.info(f"  Current Balance: ${self.current_balance:.2f}")
            logger.info(f"  Risk Amount (1%): ${self.risk_amount:.2f}")
            logger.info(f"  LOT_SIZE - Min: {self.lot_size_info['min_qty']}, Step: {self.lot_size_info['step_size']}")
            logger.info(f"  PRICE_FILTER - Tick Size: {self.price_filter_info['tick_size']}")
            logger.info(f"  MIN_NOTIONAL: ${self.min_notional}")
            
            # Initialize Telegram bot
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                logger.info("Telegram bot: SUCCESS")
            except Exception as e:
                logger.warning(f"Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            logger.error(f"Connection initialization failed: {e}")
            return False
    
    def round_to_step_size(self, quantity, step_size):
        """Round quantity to proper step size"""
        try:
            decimal.getcontext().rounding = decimal.ROUND_DOWN
            qty_decimal = decimal.Decimal(str(quantity))
            step_decimal = decimal.Decimal(str(step_size))
            rounded = qty_decimal.quantize(step_decimal)
            return float(rounded)
        except Exception as e:
            logger.error(f"Step size rounding failed: {e}")
            return quantity
    
    def round_to_tick_size(self, price, tick_size):
        """Round price to proper tick size"""
        try:
            decimal.getcontext().rounding = decimal.ROUND_HALF_UP
            price_decimal = decimal.Decimal(str(price))
            tick_decimal = decimal.Decimal(str(tick_size))
            rounded = price_decimal.quantize(tick_decimal)
            return float(rounded)
        except Exception as e:
            logger.error(f"Tick size rounding failed: {e}")
            return price
    
    def calculate_realistic_position(self):
        """Calculate realistic position based on actual balance"""
        try:
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Round price to tick size
            entry_price = self.round_to_tick_size(current_price, self.price_filter_info['tick_size'])
            
            # Calculate position size for 1% risk with 0.1% stop loss
            # Risk = Position Size * Stop Loss %
            # Position Size = Risk / Stop Loss %
            position_size_usdt = self.risk_amount / self.stop_loss_percent
            
            # Ensure we don't exceed account balance (safety margin)
            max_position = self.current_balance * 0.8  # Use max 80% of balance
            if position_size_usdt > max_position:
                position_size_usdt = max_position
                logger.info(f"Position size limited to 80% of balance: ${position_size_usdt:.2f}")
            
            # Calculate BTC quantity
            btc_quantity = position_size_usdt / entry_price
            
            # Round quantity to step size
            btc_quantity = self.round_to_step_size(btc_quantity, self.lot_size_info['step_size'])
            
            # Ensure minimum quantity
            if btc_quantity < self.lot_size_info['min_qty']:
                btc_quantity = self.lot_size_info['min_qty']
                logger.info(f"Adjusted to minimum quantity: {btc_quantity:.8f}")
            
            # Recalculate position size based on rounded quantity
            actual_position_size = btc_quantity * entry_price
            
            # Check minimum notional
            if actual_position_size < self.min_notional:
                btc_quantity = self.min_notional / entry_price
                btc_quantity = self.round_to_step_size(btc_quantity, self.lot_size_info['step_size'])
                actual_position_size = btc_quantity * entry_price
                logger.info(f"Adjusted to meet minimum notional: {btc_quantity:.8f} BTC")
            
            # Calculate SL and TP prices with proper rounding
            stop_loss_price = self.round_to_tick_size(
                entry_price * (1 - self.stop_loss_percent), 
                self.price_filter_info['tick_size']
            )
            take_profit_price = self.round_to_tick_size(
                entry_price * (1 + self.take_profit_percent), 
                self.price_filter_info['tick_size']
            )
            
            # Calculate actual risk
            actual_risk = actual_position_size * self.stop_loss_percent
            
            # Store calculated values
            self.test_trade_data = {
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'btc_quantity': btc_quantity,
                'position_size_usdt': actual_position_size,
                'actual_risk': actual_risk,
                'risk_percent': (actual_risk / self.current_balance) * 100,
                'step_size': self.lot_size_info['step_size'],
                'tick_size': self.price_filter_info['tick_size'],
                'min_notional': self.min_notional
            }
            
            logger.info("REALISTIC POSITION CALCULATION:")
            logger.info(f"  Account Balance: ${self.current_balance:.2f}")
            logger.info(f"  Entry Price: ${entry_price:.2f}")
            logger.info(f"  Stop Loss: ${stop_loss_price:.2f} (-0.1%)")
            logger.info(f"  Take Profit: ${take_profit_price:.2f} (+0.25%)")
            logger.info(f"  BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"  Position Size: ${actual_position_size:.2f}")
            logger.info(f"  Actual Risk: ${actual_risk:.2f} ({self.test_trade_data['risk_percent']:.2f}%)")
            logger.info(f"  Available Balance After: ${self.current_balance - actual_position_size:.2f}")
            
            # Validate we have sufficient balance
            if actual_position_size <= self.current_balance:
                logger.info("BALANCE CHECK: PASSED")
                return True
            else:
                logger.error(f"BALANCE CHECK: FAILED - Need ${actual_position_size:.2f}, Have ${self.current_balance:.2f}")
                return False
            
        except Exception as e:
            logger.error(f"Position calculation failed: {e}")
            return False
    
    def execute_realistic_test_order(self):
        """Execute test order with realistic parameters"""
        try:
            logger.info("EXECUTING REALISTIC TEST ORDER...")
            
            # Send Telegram notification
            self.send_telegram_notification("TEST_STARTING")
            
            # Format quantity with proper precision
            quantity_str = f"{self.test_trade_data['btc_quantity']:.8f}".rstrip('0').rstrip('.')
            
            logger.info(f"Placing BUY order:")
            logger.info(f"  Symbol: BTCUSDT")
            logger.info(f"  Side: BUY")
            logger.info(f"  Type: MARKET")
            logger.info(f"  Quantity: {quantity_str}")
            logger.info(f"  Estimated Cost: ${self.test_trade_data['position_size_usdt']:.2f}")
            
            # Execute BUY market order
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=quantity_str
            )
            
            logger.info(f"BUY order executed successfully!")
            logger.info(f"  Order ID: {buy_order['orderId']}")
            logger.info(f"  Status: {buy_order.get('status', 'Unknown')}")
            
            # Get actual execution details
            order_details = self.binance.client.get_order(
                symbol='BTCUSDT',
                orderId=buy_order['orderId']
            )
            
            actual_quantity = float(order_details['executedQty'])
            
            # Calculate average price from fills
            if 'fills' in buy_order and buy_order['fills']:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_price = total_cost / actual_quantity if actual_quantity > 0 else self.test_trade_data['entry_price']
            else:
                actual_price = self.test_trade_data['entry_price']
            
            # Update trade data
            self.test_trade_data.update({
                'entry_order_id': buy_order['orderId'],
                'actual_entry_price': actual_price,
                'actual_quantity': actual_quantity,
                'actual_cost': actual_quantity * actual_price,
                'entry_time': datetime.now(),
                'order_status': order_details['status']
            })
            
            # Recalculate SL and TP based on actual execution
            actual_stop_loss = self.round_to_tick_size(
                actual_price * (1 - self.stop_loss_percent),
                self.price_filter_info['tick_size']
            )
            actual_take_profit = self.round_to_tick_size(
                actual_price * (1 + self.take_profit_percent),
                self.price_filter_info['tick_size']
            )
            
            self.test_trade_data.update({
                'actual_stop_loss': actual_stop_loss,
                'actual_take_profit': actual_take_profit
            })
            
            logger.info("ACTUAL EXECUTION DETAILS:")
            logger.info(f"  Actual Entry Price: ${actual_price:.2f}")
            logger.info(f"  Actual Quantity: {actual_quantity:.8f} BTC")
            logger.info(f"  Actual Cost: ${self.test_trade_data['actual_cost']:.2f}")
            logger.info(f"  Actual Stop Loss: ${actual_stop_loss:.2f}")
            logger.info(f"  Actual Take Profit: ${actual_take_profit:.2f}")
            
            # Send execution notification
            self.send_telegram_notification("TEST_EXECUTED")
            
            return True
            
        except Exception as e:
            logger.error(f"Realistic test order execution failed: {e}")
            self.send_telegram_notification("TEST_FAILED")
            return False

    def place_realistic_exit_orders(self):
        """Place OCO exit orders with realistic parameters"""
        try:
            logger.info("Placing realistic OCO exit orders...")

            # Format quantity and prices with proper precision
            quantity_str = f"{self.test_trade_data['actual_quantity']:.8f}".rstrip('0').rstrip('.')
            tp_price_str = f"{self.test_trade_data['actual_take_profit']:.2f}"
            sl_price_str = f"{self.test_trade_data['actual_stop_loss']:.2f}"
            sl_limit_price = self.test_trade_data['actual_stop_loss'] * 0.999
            sl_limit_str = f"{sl_limit_price:.2f}"

            logger.info(f"OCO Parameters:")
            logger.info(f"  Quantity: {quantity_str}")
            logger.info(f"  TP Price: {tp_price_str}")
            logger.info(f"  SL Price: {sl_price_str}")
            logger.info(f"  SL Limit: {sl_limit_str}")

            # Place OCO order
            oco_order = self.binance.client.create_oco_order(
                symbol='BTCUSDT',
                side='SELL',
                quantity=quantity_str,
                price=tp_price_str,
                stopPrice=sl_price_str,
                stopLimitPrice=sl_limit_str,
                stopLimitTimeInForce='GTC'
            )

            self.test_trade_data['oco_order_id'] = oco_order['orderListId']

            logger.info(f"OCO order placed successfully: {oco_order['orderListId']}")
            logger.info("REAL TEST TRADE ACTIVE - Monitoring for TP or SL...")

            return True

        except Exception as e:
            logger.error(f"OCO order placement failed: {e}")
            return False

    def monitor_test_trade(self, max_wait_hours=2):
        """Monitor test trade until completion"""
        try:
            logger.info(f"Monitoring real test trade (max {max_wait_hours} hours)...")
            start_time = datetime.now()
            check_count = 0

            while (datetime.now() - start_time).total_seconds() < (max_wait_hours * 3600):
                try:
                    check_count += 1

                    # Check OCO order status
                    oco_status = self.binance.client.get_order_list(
                        orderListId=self.test_trade_data['oco_order_id']
                    )

                    logger.info(f"Check #{check_count}: OCO Status = {oco_status['listStatusType']}")

                    if oco_status['listStatusType'] == 'ALL_DONE':
                        # Trade completed
                        for order in oco_status['orders']:
                            if order['status'] == 'FILLED':
                                exit_price = float(order['price'])

                                # Determine result
                                tp_price = self.test_trade_data['actual_take_profit']
                                sl_price = self.test_trade_data['actual_stop_loss']

                                if abs(exit_price - tp_price) < abs(exit_price - sl_price):
                                    result = 'WIN'
                                    pnl_percent = self.take_profit_percent * 100  # 0.25%
                                    pnl_amount = self.test_trade_data['actual_cost'] * self.take_profit_percent
                                    logger.info("🎉 REAL TEST TRADE WON - TAKE PROFIT HIT!")
                                else:
                                    result = 'LOSS'
                                    pnl_percent = -self.stop_loss_percent * 100  # -0.1%
                                    pnl_amount = -self.test_trade_data['actual_cost'] * self.stop_loss_percent
                                    logger.info("📉 REAL TEST TRADE LOST - STOP LOSS HIT")

                                # Update test data
                                self.test_trade_data.update({
                                    'exit_time': datetime.now(),
                                    'exit_price': exit_price,
                                    'result': result,
                                    'pnl_percent': pnl_percent,
                                    'pnl_amount': pnl_amount,
                                    'duration_minutes': (datetime.now() - self.test_trade_data['entry_time']).total_seconds() / 60
                                })

                                # Send completion notification
                                self.send_telegram_notification("TEST_COMPLETED")

                                logger.info("REAL TEST TRADE MONITORING COMPLETED")
                                self.test_status = "COMPLETED"
                                return True

                except Exception as e:
                    logger.warning(f"Error checking OCO status: {e}")

                # Wait 30 seconds before next check
                time.sleep(30)

                # Log progress every 5 minutes
                elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                if elapsed_minutes % 5 == 0:
                    logger.info(f"Monitoring progress: {elapsed_minutes:.0f} minutes elapsed")

            logger.warning(f"Test trade monitoring timeout after {max_wait_hours} hours")
            return False

        except Exception as e:
            logger.error(f"Test trade monitoring failed: {e}")
            return False

    def send_telegram_notification(self, event_type):
        """Send Telegram notification"""
        try:
            if not self.telegram_bot:
                return

            if event_type == "TEST_STARTING":
                message = f"""
🧪 REAL TEST TRADE STARTING

Account Balance: ${self.current_balance:.2f}
Risk Amount: ${self.risk_amount:.2f} (1%)
Position Size: ${self.test_trade_data['position_size_usdt']:.2f}
Parameters: SL=0.1%, TP=0.25%, RR=2.5:1

Executing REAL money test trade...
"""

            elif event_type == "TEST_EXECUTED":
                data = self.test_trade_data
                message = f"""
✅ REAL TEST TRADE EXECUTED

Entry Details:
- Price: ${data['actual_entry_price']:.2f}
- Quantity: {data['actual_quantity']:.8f} BTC
- Cost: ${data['actual_cost']:.2f}
- Stop Loss: ${data['actual_stop_loss']:.2f} (-0.1%)
- Take Profit: ${data['actual_take_profit']:.2f} (+0.25%)

OCO orders placed. Monitoring until TP or SL hit...
This is REAL money trading!
"""

            elif event_type == "TEST_COMPLETED":
                data = self.test_trade_data
                result_emoji = "🎉" if data['result'] == 'WIN' else "📉"
                message = f"""
{result_emoji} REAL TEST TRADE COMPLETED

Result: {data['result']}
- Entry: ${data['actual_entry_price']:.2f}
- Exit: ${data['exit_price']:.2f}
- P&L: ${data['pnl_amount']:.2f} ({data['pnl_percent']:.2f}%)
- Duration: {data['duration_minutes']:.0f} minutes

SYSTEM VALIDATION COMPLETE ✅
All systems verified with REAL money
READY FOR LIVE TRADING DEPLOYMENT! 🚀
"""

            elif event_type == "TEST_FAILED":
                message = """
❌ REAL TEST TRADE FAILED

Investigating issue...
Will retry with corrected parameters...
"""

            self.telegram_bot.send_message(message)
            logger.info(f"Telegram notification sent: {event_type}")

        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")

    def run_final_test(self):
        """Run complete final test trade"""
        try:
            logger.info("STARTING FINAL REAL MONEY TEST TRADE")
            logger.info("Using realistic position sizing based on actual balance")
            logger.info("This will execute REAL trades with REAL money")
            logger.info("="*80)

            # Step 1: Initialize connections and get requirements
            if not self.initialize_connections():
                logger.error("Connection initialization failed")
                return False

            # Step 2: Calculate realistic position
            if not self.calculate_realistic_position():
                logger.error("Realistic position calculation failed")
                return False

            # Step 3: Execute realistic test order
            if not self.execute_realistic_test_order():
                logger.error("Realistic test order execution failed")
                return False

            # Step 4: Place realistic exit orders
            if not self.place_realistic_exit_orders():
                logger.error("Realistic exit order placement failed")
                return False

            # Step 5: Monitor until completion
            if not self.monitor_test_trade():
                logger.error("Test trade monitoring failed or timeout")
                return False

            # Success!
            logger.info("FINAL REAL MONEY TEST TRADE COMPLETED SUCCESSFULLY!")
            self.test_status = "COMPLETED"

            # Save results
            results = {
                'test_type': 'Final Real Money Test Trade',
                'test_status': self.test_status,
                'timestamp': datetime.now().isoformat(),
                'account_balance': self.current_balance,
                'trade_data': self.test_trade_data,
                'compliance_info': {
                    'lot_size_info': self.lot_size_info,
                    'price_filter_info': self.price_filter_info,
                    'min_notional': self.min_notional
                },
                'ready_for_live_trading': True
            }

            with open('final_test_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)

            return True

        except Exception as e:
            logger.error(f"Final test trade failed: {e}")
            self.test_status = "FAILED"
            return False

def main():
    """Main execution"""
    print("FINAL REAL MONEY TEST TRADE EXECUTION")
    print("Realistic position sizing based on actual account balance")
    print("Using proven 1-year performance model parameters")
    print("SL: 0.1%, TP: 0.25%, RR: 2.5:1, Risk: 1% of balance")
    print("="*80)
    print("⚠️  WARNING: This will execute REAL trades with REAL money!")
    print("⚠️  This is the final validation before live deployment!")
    print("="*80)

    try:
        # Initialize final test executor
        test_executor = FinalTestTrade()

        # Run final test
        if test_executor.run_final_test():
            print("\n🎉 SUCCESS: FINAL REAL MONEY TEST TRADE COMPLETED!")
            print("All systems verified and operational with real money")
            print("System is ready for live deployment")

            # Display results
            if hasattr(test_executor, 'test_trade_data') and test_executor.test_trade_data:
                data = test_executor.test_trade_data
                print(f"\nREAL MONEY TEST RESULTS:")
                print(f"  Result: {data.get('result', 'Unknown')}")
                print(f"  Entry: ${data.get('actual_entry_price', 0):.2f}")
                print(f"  Exit: ${data.get('exit_price', 0):.2f}")
                print(f"  P&L: ${data.get('pnl_amount', 0):.2f} ({data.get('pnl_percent', 0):.2f}%)")
                print(f"  Duration: {data.get('duration_minutes', 0):.0f} minutes")
                print(f"  Position: {data.get('actual_quantity', 0):.8f} BTC (${data.get('actual_cost', 0):.2f})")
                print(f"  Risk: {data.get('risk_percent', 0):.2f}% of account")

                if data.get('result') == 'WIN':
                    print("🎉 Test trade WON - Take Profit hit!")
                else:
                    print("📉 Test trade LOST - Stop Loss hit (normal)")

                print("\n✅ SYSTEM VALIDATION COMPLETE:")
                print("✅ Real money execution: CONFIRMED")
                print("✅ Risk management: VALIDATED")
                print("✅ TP/SL functionality: VERIFIED")
                print("✅ Position sizing: ACCURATE")
                print("✅ Compliance: PASSED")

                print("\n🚀 SYSTEM IS READY FOR LIVE TRADING DEPLOYMENT!")

        else:
            print("\n❌ FAILED: Final test trade could not be completed")
            print("Check final_test_trade.log for details")
            print("System is NOT ready for live deployment")

    except Exception as e:
        print(f"\nCRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
