#!/usr/bin/env python3
"""
Enhanced Performance Metrics System
Comprehensive performance evaluation targeting:
- 0.8+ composite score
- 60%+ win rate  
- 5+ trades per day
- 1:2.5 risk-reward ratio
- Highest composite score * net profit optimization

Includes advanced metrics:
- Sortino ratio, Calmar ratio, profit factor
- Maximum drawdown, trade frequency
- Risk-adjusted returns, volatility analysis
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

@dataclass
class TradeResult:
    """Individual trade result"""
    timestamp: datetime
    entry_price: float
    exit_price: float
    trade_type: str  # 'BUY' or 'SELL'
    pnl: float
    pnl_percentage: float
    duration_hours: float
    is_winner: bool
    risk_reward_ratio: float

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Core metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Profitability metrics
    total_pnl: float
    gross_profit: float
    gross_loss: float
    profit_factor: float
    average_win: float
    average_loss: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_duration: float
    volatility: float
    downside_deviation: float
    
    # Risk-adjusted metrics
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trading frequency
    trades_per_day: float
    average_trade_duration: float
    
    # Composite score
    composite_score: float
    composite_score_x_profit: float

class EnhancedPerformanceMetrics:
    """
    Enhanced Performance Metrics System
    Calculates comprehensive trading performance metrics
    """
    
    def __init__(self):
        """Initialize the performance metrics system"""
        self.logger = logging.getLogger(__name__)
        
        # Composite score weights (optimized for trading performance)
        self.composite_weights = {
            'sortino_ratio': 0.28,      # 28% - Risk-adjusted returns
            'calmar_ratio': 0.22,       # 22% - Return/max drawdown ratio  
            'profit_factor': 0.20,      # 20% - Gross profit/gross loss
            'win_rate': 0.15,           # 15% - Win percentage
            'max_drawdown_inverse': 0.10,  # 10% - Drawdown minimization
            'trade_frequency': 0.05     # 5% - Trading activity
        }
        
        # Performance targets
        self.targets = {
            'composite_score': 0.8,
            'win_rate': 0.60,
            'trades_per_day': 5.0,
            'risk_reward_ratio': 2.5,
            'max_drawdown': 0.10,  # 10%
            'sortino_ratio': 2.0,
            'calmar_ratio': 3.0,
            'profit_factor': 1.5
        }
        
        self.logger.info("📊 Enhanced Performance Metrics System Initialized")
        self.logger.info(f"🎯 Targets: {self.targets['composite_score']} composite, {self.targets['win_rate']:.0%} win rate, {self.targets['trades_per_day']} trades/day")
    
    def calculate_comprehensive_metrics(self, trades: List[TradeResult], 
                                      initial_balance: float = 100.0,
                                      period_days: float = 365.0) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics from trade results
        
        Args:
            trades: List of TradeResult objects
            initial_balance: Starting balance for calculations
            period_days: Period length in days for annualization
            
        Returns:
            PerformanceMetrics object with all calculated metrics
        """
        try:
            if not trades:
                return self._create_empty_metrics()
            
            # Basic trade statistics
            total_trades = len(trades)
            winning_trades = sum(1 for trade in trades if trade.is_winner)
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
            
            # PnL calculations
            total_pnl = sum(trade.pnl for trade in trades)
            gross_profit = sum(trade.pnl for trade in trades if trade.pnl > 0)
            gross_loss = abs(sum(trade.pnl for trade in trades if trade.pnl < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Average win/loss
            wins = [trade.pnl for trade in trades if trade.is_winner]
            losses = [abs(trade.pnl) for trade in trades if not trade.is_winner]
            average_win = np.mean(wins) if wins else 0.0
            average_loss = np.mean(losses) if losses else 0.0
            
            # Calculate equity curve for drawdown analysis
            equity_curve = self._calculate_equity_curve(trades, initial_balance)
            
            # Drawdown metrics
            max_drawdown, max_drawdown_duration = self._calculate_drawdown_metrics(equity_curve)
            
            # Volatility metrics
            returns = [trade.pnl_percentage for trade in trades]
            volatility = np.std(returns) * np.sqrt(252) if returns else 0.0  # Annualized
            downside_returns = [r for r in returns if r < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(252) if downside_returns else 0.0
            
            # Risk-adjusted metrics
            mean_return = np.mean(returns) if returns else 0.0
            annualized_return = mean_return * 252  # Assuming daily returns
            
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0.0
            sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0.0
            calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0.0
            
            # Trading frequency
            trades_per_day = total_trades / period_days if period_days > 0 else 0.0
            average_trade_duration = np.mean([trade.duration_hours for trade in trades]) if trades else 0.0
            
            # Calculate composite score
            composite_score = self._calculate_composite_score(
                sortino_ratio, calmar_ratio, profit_factor, win_rate, 
                max_drawdown, trades_per_day
            )
            
            # Composite score * net profit (optimization target)
            composite_score_x_profit = composite_score * max(0, total_pnl)
            
            return PerformanceMetrics(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                gross_profit=gross_profit,
                gross_loss=gross_loss,
                profit_factor=profit_factor,
                average_win=average_win,
                average_loss=average_loss,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_drawdown_duration,
                volatility=volatility,
                downside_deviation=downside_deviation,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                trades_per_day=trades_per_day,
                average_trade_duration=average_trade_duration,
                composite_score=composite_score,
                composite_score_x_profit=composite_score_x_profit
            )
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating comprehensive metrics: {e}")
            return self._create_empty_metrics()
    
    def _calculate_equity_curve(self, trades: List[TradeResult], initial_balance: float) -> List[float]:
        """Calculate equity curve from trade results"""
        equity_curve = [initial_balance]
        current_balance = initial_balance
        
        for trade in trades:
            current_balance += trade.pnl
            equity_curve.append(current_balance)
        
        return equity_curve
    
    def _calculate_drawdown_metrics(self, equity_curve: List[float]) -> Tuple[float, float]:
        """Calculate maximum drawdown and duration"""
        if len(equity_curve) < 2:
            return 0.0, 0.0
        
        peak = equity_curve[0]
        max_drawdown = 0.0
        max_drawdown_duration = 0.0
        current_drawdown_duration = 0.0
        
        for value in equity_curve[1:]:
            if value > peak:
                peak = value
                current_drawdown_duration = 0.0
            else:
                drawdown = (peak - value) / peak
                max_drawdown = max(max_drawdown, drawdown)
                current_drawdown_duration += 1
                max_drawdown_duration = max(max_drawdown_duration, current_drawdown_duration)
        
        return max_drawdown, max_drawdown_duration
    
    def _calculate_composite_score(self, sortino_ratio: float, calmar_ratio: float, 
                                 profit_factor: float, win_rate: float, 
                                 max_drawdown: float, trades_per_day: float) -> float:
        """Calculate composite score using weighted metrics"""
        try:
            # Normalize metrics to 0-1 scale
            sortino_normalized = min(1.0, max(0.0, sortino_ratio / 3.0))  # Cap at 3.0
            calmar_normalized = min(1.0, max(0.0, calmar_ratio / 5.0))    # Cap at 5.0
            profit_factor_normalized = min(1.0, max(0.0, (profit_factor - 1.0) / 2.0))  # 1.0-3.0 -> 0-1
            win_rate_normalized = win_rate  # Already 0-1
            max_drawdown_inverse = max(0.0, 1.0 - (max_drawdown / 0.2))  # Inverse, cap at 20%
            trade_frequency_normalized = min(1.0, trades_per_day / 10.0)  # Cap at 10 trades/day
            
            # Calculate weighted composite score
            composite_score = (
                self.composite_weights['sortino_ratio'] * sortino_normalized +
                self.composite_weights['calmar_ratio'] * calmar_normalized +
                self.composite_weights['profit_factor'] * profit_factor_normalized +
                self.composite_weights['win_rate'] * win_rate_normalized +
                self.composite_weights['max_drawdown_inverse'] * max_drawdown_inverse +
                self.composite_weights['trade_frequency'] * trade_frequency_normalized
            )
            
            return composite_score
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating composite score: {e}")
            return 0.0
    
    def _create_empty_metrics(self) -> PerformanceMetrics:
        """Create empty performance metrics object"""
        return PerformanceMetrics(
            total_trades=0, winning_trades=0, losing_trades=0, win_rate=0.0,
            total_pnl=0.0, gross_profit=0.0, gross_loss=0.0, profit_factor=0.0,
            average_win=0.0, average_loss=0.0, max_drawdown=0.0, max_drawdown_duration=0.0,
            volatility=0.0, downside_deviation=0.0, sharpe_ratio=0.0, sortino_ratio=0.0,
            calmar_ratio=0.0, trades_per_day=0.0, average_trade_duration=0.0,
            composite_score=0.0, composite_score_x_profit=0.0
        )
    
    def evaluate_target_achievement(self, metrics: PerformanceMetrics) -> Dict[str, bool]:
        """Evaluate whether performance targets are achieved"""
        return {
            'composite_score': metrics.composite_score >= self.targets['composite_score'],
            'win_rate': metrics.win_rate >= self.targets['win_rate'],
            'trades_per_day': metrics.trades_per_day >= self.targets['trades_per_day'],
            'max_drawdown': metrics.max_drawdown <= self.targets['max_drawdown'],
            'sortino_ratio': metrics.sortino_ratio >= self.targets['sortino_ratio'],
            'calmar_ratio': metrics.calmar_ratio >= self.targets['calmar_ratio'],
            'profit_factor': metrics.profit_factor >= self.targets['profit_factor']
        }
    
    def generate_performance_report(self, metrics: PerformanceMetrics) -> str:
        """Generate comprehensive performance report"""
        targets_achieved = self.evaluate_target_achievement(metrics)
        
        report = f"""
📊 ENHANCED PERFORMANCE METRICS REPORT
{'='*50}
📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 CORE PERFORMANCE METRICS:
   Total Trades: {metrics.total_trades:,}
   Win Rate: {metrics.win_rate:.1%} {'✅' if targets_achieved['win_rate'] else '❌'} (Target: {self.targets['win_rate']:.0%})
   Trades/Day: {metrics.trades_per_day:.1f} {'✅' if targets_achieved['trades_per_day'] else '❌'} (Target: {self.targets['trades_per_day']})
   
💰 PROFITABILITY METRICS:
   Total PnL: ${metrics.total_pnl:.2f}
   Profit Factor: {metrics.profit_factor:.2f} {'✅' if targets_achieved['profit_factor'] else '❌'} (Target: {self.targets['profit_factor']})
   Average Win: ${metrics.average_win:.2f}
   Average Loss: ${metrics.average_loss:.2f}
   
🛡️ RISK METRICS:
   Max Drawdown: {metrics.max_drawdown:.1%} {'✅' if targets_achieved['max_drawdown'] else '❌'} (Target: ≤{self.targets['max_drawdown']:.0%})
   Volatility: {metrics.volatility:.1%}
   Sharpe Ratio: {metrics.sharpe_ratio:.2f}
   
📈 RISK-ADJUSTED METRICS:
   Sortino Ratio: {metrics.sortino_ratio:.2f} {'✅' if targets_achieved['sortino_ratio'] else '❌'} (Target: {self.targets['sortino_ratio']})
   Calmar Ratio: {metrics.calmar_ratio:.2f} {'✅' if targets_achieved['calmar_ratio'] else '❌'} (Target: {self.targets['calmar_ratio']})
   
🏆 COMPOSITE METRICS:
   Composite Score: {metrics.composite_score:.3f} {'✅' if targets_achieved['composite_score'] else '❌'} (Target: {self.targets['composite_score']})
   Composite × Profit: {metrics.composite_score_x_profit:.2f}
   
📋 TARGET ACHIEVEMENT: {sum(targets_achieved.values())}/{len(targets_achieved)} targets met
"""
        return report

if __name__ == "__main__":
    # Test the performance metrics system
    metrics_system = EnhancedPerformanceMetrics()
    print("✅ Enhanced Performance Metrics System initialized successfully")
