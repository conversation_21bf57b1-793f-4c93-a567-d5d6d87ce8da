#!/usr/bin/env python3
"""
Check the status of our test trade
OCO Order ID: 11684644933
Entry Order ID: 46194558984
"""

import sys
sys.path.append('01_binance_connector')

def check_test_trade_status():
    try:
        from binance_real_money_connector import BinanceRealMoneyConnector
        
        connector = BinanceRealMoneyConnector()
        
        print("CHECKING TEST TRADE STATUS")
        print("="*50)
        
        # Check entry order
        try:
            entry_order = connector.client.get_order(symbol='BTCUSDT', orderId=46194558984)
            print("ENTRY ORDER STATUS:")
            print(f"  Order ID: {entry_order['orderId']}")
            print(f"  Status: {entry_order['status']}")
            print(f"  Side: {entry_order['side']}")
            print(f"  Quantity: {entry_order['executedQty']}")
            print(f"  Price: {entry_order.get('price', 'Market')}")
            if 'fills' in entry_order:
                avg_price = sum(float(fill['price']) * float(fill['qty']) for fill in entry_order['fills']) / float(entry_order['executedQty'])
                print(f"  Average Fill Price: ${avg_price:.2f}")
        except Exception as e:
            print(f"Error checking entry order: {e}")
        
        print()
        
        # Check OCO order using correct method
        try:
            oco_order = connector.client.get_oco_order(orderListId=11684644933)
            print("OCO ORDER STATUS:")
            print(f"  OCO ID: {oco_order['orderListId']}")
            print(f"  Status: {oco_order['listStatusType']}")
            print(f"  Order Count: {len(oco_order['orders'])}")
            
            for i, order in enumerate(oco_order['orders']):
                print(f"  Order {i+1}:")
                print(f"    Order ID: {order['orderId']}")
                print(f"    Side: {order['side']}")
                print(f"    Type: {order['type']}")
                print(f"    Price: ${float(order['price']):.2f}")
                print(f"    Status: {order['status']}")
                
                if order['status'] == 'FILLED':
                    print(f"    *** FILLED! ***")
                    print(f"    Fill Price: ${float(order['price']):.2f}")
                    
                    # Determine if TP or SL
                    if float(order['price']) > 120200:  # Approximate entry price
                        print(f"    Result: TAKE PROFIT HIT! 🎉")
                        pnl = (float(order['price']) - 120143.11) * 0.00005
                        print(f"    P&L: ${pnl:.4f}")
                    else:
                        print(f"    Result: STOP LOSS HIT 📉")
                        pnl = (float(order['price']) - 120143.11) * 0.00005
                        print(f"    P&L: ${pnl:.4f}")
                        
        except Exception as e:
            print(f"Error checking OCO order: {e}")
            
            # Try checking open orders
            try:
                open_orders = connector.client.get_open_orders(symbol='BTCUSDT')
                print(f"\nOPEN ORDERS: {len(open_orders)}")
                for order in open_orders:
                    print(f"  Order ID: {order['orderId']}")
                    print(f"  Side: {order['side']}")
                    print(f"  Type: {order['type']}")
                    print(f"  Price: {order['price']}")
                    print(f"  Status: {order['status']}")
                    print()
            except Exception as e2:
                print(f"Error checking open orders: {e2}")
        
        # Check current balance
        try:
            balance_info = connector.get_isolated_margin_balance()
            print("\nCURRENT BALANCE:")
            print(f"  USDT: ${balance_info['usdt_balance']:.2f}")
            print(f"  BTC: {balance_info['btc_balance']:.8f}")
            print(f"  Total USD Value: ${balance_info['total_usdt_value']:.2f}")
        except Exception as e:
            print(f"Error checking balance: {e}")
            
        # Get current BTC price
        try:
            ticker = connector.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            print(f"\nCURRENT BTC PRICE: ${current_price:.2f}")
            
            # Compare to our levels
            entry_price = 120143.11
            tp_price = 120443.47
            sl_price = 120022.97
            
            print(f"Entry Price: ${entry_price:.2f}")
            print(f"Take Profit: ${tp_price:.2f} (${tp_price - entry_price:.2f} away)")
            print(f"Stop Loss: ${sl_price:.2f} (${entry_price - sl_price:.2f} away)")
            
            if current_price >= tp_price:
                print("🎉 CURRENT PRICE IS AT/ABOVE TAKE PROFIT!")
            elif current_price <= sl_price:
                print("📉 CURRENT PRICE IS AT/BELOW STOP LOSS!")
            else:
                print(f"📊 CURRENT PRICE IS BETWEEN SL AND TP")
                print(f"Distance to TP: ${tp_price - current_price:.2f}")
                print(f"Distance to SL: ${current_price - sl_price:.2f}")
                
        except Exception as e:
            print(f"Error checking current price: {e}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_test_trade_status()
