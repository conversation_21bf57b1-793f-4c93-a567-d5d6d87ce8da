#!/usr/bin/env python3
"""
Enhanced Money Management System
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Supports $100 equivalent, 1-2% risk, 2.5% reward, compounding
"""

import json
import logging
from datetime import datetime
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class EnhancedMoneyManager:
    """Enhanced money management with compounding and adjustable risk"""
    
    def __init__(self, config_file: str = 'money_management_config.json'):
        self.config_file = config_file
        self.load_configuration()
        self.initialize_state()
        
    def load_configuration(self):
        """Load money management configuration"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
                
            # Master document compliance parameters
            self.starting_balance = config.get('starting_balance', 100.0)
            self.risk_per_trade = config.get('risk_per_trade', 0.01)  # 1% default
            self.reward_per_trade = config.get('reward_per_trade', 0.025)  # 2.5% fixed
            self.risk_reward_ratio = config.get('risk_reward_ratio', 2.5)  # Fixed 2.5:1
            
            # Compounding settings
            self.compounding_enabled = config.get('compounding_enabled', True)
            self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)  # 2% max
            self.min_risk_per_trade = config.get('min_risk_per_trade', 0.01)  # 1% min
            
            # Position sizing
            self.min_position_size = config.get('min_position_size', 0.001)  # BTC
            self.max_position_size = config.get('max_position_size', 1.0)  # BTC
            
            # Safety limits
            self.max_daily_loss = config.get('max_daily_loss', 0.05)  # 5% daily loss limit
            self.max_drawdown = config.get('max_drawdown', 0.10)  # 10% max drawdown
            
            logger.info('✅ Money management configuration loaded')
            
        except FileNotFoundError:
            logger.warning('⚠️ Config file not found, creating default configuration')
            self.create_default_config()
            self.load_configuration()
        except Exception as e:
            logger.error(f'❌ Error loading configuration: {e}')
            raise
    
    def create_default_config(self):
        """Create default money management configuration"""
        default_config = {
            "starting_balance": 100.0,
            "risk_per_trade": 0.01,
            "reward_per_trade": 0.025,
            "risk_reward_ratio": 2.5,
            "compounding_enabled": True,
            "max_risk_per_trade": 0.02,
            "min_risk_per_trade": 0.01,
            "min_position_size": 0.001,
            "max_position_size": 1.0,
            "max_daily_loss": 0.05,
            "max_drawdown": 0.10,
            "master_document_compliance": {
                "description": "100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md",
                "starting_capital": "$100 equivalent",
                "risk_management": "1-2% risk per trade",
                "reward_target": "2.5% reward per trade",
                "risk_reward_ratio": "Fixed 2.5:1",
                "compounding": "Enabled for balance growth"
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        logger.info('✅ Default money management configuration created')
    
    def initialize_state(self):
        """Initialize money management state"""
        self.current_balance = self.starting_balance
        self.peak_balance = self.starting_balance
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.trades_today = 0
        self.session_start = datetime.now()
        
        logger.info(f'💰 Money management initialized: ${self.current_balance:.2f} starting balance')
        logger.info(f'⚖️ Risk per trade: {self.risk_per_trade:.1%}, Reward target: {self.reward_per_trade:.1%}')
    
    def calculate_position_size(self, current_price: float, signal_confidence: float = 1.0) -> Tuple[float, Dict]:
        """
        Calculate position size for EXACT dollar amounts: $1 SL, $2.50 TP with compounding

        CALCULATION LOGIC:
        1. Target SL = 1% of current balance (starts at $1, grows with compounding)
        2. Target TP = 2.5% of current balance (starts at $2.50, grows with compounding)
        3. Position size calculated to achieve exact dollar amounts
        4. Automatic rebalancing as account grows
        """
        try:
            # Check if trading is allowed
            if not self.can_trade():
                return 0.0, {'error': 'Trading not allowed due to risk limits'}

            # STEP 1: Calculate target dollar amounts with compounding
            if self.compounding_enabled:
                # Compounding: SL/TP amounts grow with account
                target_stop_loss_amount = self.current_balance * 0.01    # 1% of current balance
                target_take_profit_amount = self.current_balance * 0.025  # 2.5% of current balance
            else:
                # Fixed: Always $1 SL, $2.50 TP regardless of balance
                target_stop_loss_amount = 1.00
                target_take_profit_amount = 2.50

            # STEP 2: Calculate price distances for SL/TP levels
            # For grid-aware trading, we need to determine actual price levels
            # Assuming we're using percentage-based SL/TP distances
            stop_loss_percentage = target_stop_loss_amount / (current_price * 0.01)  # Convert $ to %
            take_profit_percentage = target_take_profit_amount / (current_price * 0.01)  # Convert $ to %

            # Ensure minimum distances for grid compliance
            stop_loss_percentage = max(stop_loss_percentage, 0.001)  # Minimum 0.1%
            take_profit_percentage = max(take_profit_percentage, 0.0025)  # Minimum 0.25%

            # STEP 3: Calculate position size to achieve exact SL dollar amount
            # Position Size = Target SL Amount / (Current Price × SL Percentage)
            position_size = target_stop_loss_amount / (current_price * stop_loss_percentage)

            # STEP 4: Apply position size limits
            position_size = max(self.min_position_size, min(position_size, self.max_position_size))

            # STEP 5: Calculate actual dollar amounts based on final position size
            actual_stop_loss_amount = position_size * current_price * stop_loss_percentage
            actual_take_profit_amount = position_size * current_price * take_profit_percentage

            # STEP 6: Calculate actual risk-reward ratio
            actual_risk_reward_ratio = actual_take_profit_amount / actual_stop_loss_amount if actual_stop_loss_amount > 0 else 0

            # STEP 7: Calculate price levels for orders
            stop_loss_price = current_price * (1 - stop_loss_percentage)
            take_profit_price = current_price * (1 + take_profit_percentage)

            calculation_details = {
                'current_balance': self.current_balance,
                'compounding_enabled': self.compounding_enabled,
                'target_stop_loss_amount': target_stop_loss_amount,
                'target_take_profit_amount': target_take_profit_amount,
                'actual_stop_loss_amount': actual_stop_loss_amount,
                'actual_take_profit_amount': actual_take_profit_amount,
                'position_size': position_size,
                'current_price': current_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'stop_loss_percentage': stop_loss_percentage,
                'take_profit_percentage': take_profit_percentage,
                'actual_risk_reward_ratio': actual_risk_reward_ratio,
                'signal_confidence': signal_confidence,
                'calculation_method': 'EXACT_DOLLAR_AMOUNTS_WITH_COMPOUNDING'
            }

            logger.info(f'💰 COMPOUNDING MONEY MANAGEMENT CALCULATION:')
            logger.info(f'   Current Balance: ${self.current_balance:.2f}')
            logger.info(f'   Target SL Amount: ${target_stop_loss_amount:.2f} (1% of balance)')
            logger.info(f'   Target TP Amount: ${target_take_profit_amount:.2f} (2.5% of balance)')
            logger.info(f'   Position Size: {position_size:.6f} BTC')
            logger.info(f'   Entry Price: ${current_price:.2f}')
            logger.info(f'   Stop Loss Price: ${stop_loss_price:.2f}')
            logger.info(f'   Take Profit Price: ${take_profit_price:.2f}')
            logger.info(f'   Actual SL: ${actual_stop_loss_amount:.2f}')
            logger.info(f'   Actual TP: ${actual_take_profit_amount:.2f}')
            logger.info(f'   Risk-Reward Ratio: {actual_risk_reward_ratio:.2f}:1')

            return position_size, calculation_details

        except Exception as e:
            logger.error(f'❌ Error calculating position size: {e}')
            return 0.0, {'error': str(e)}
    
    def update_balance(self, pnl: float, trade_type: str) -> Dict:
        """Update balance with P&L and compounding"""
        try:
            # Update balances
            self.current_balance += pnl
            self.daily_pnl += pnl
            self.total_pnl += pnl
            self.trades_today += 1
            
            # Update peak balance for drawdown calculation
            if self.current_balance > self.peak_balance:
                self.peak_balance = self.current_balance
            
            # Calculate current drawdown
            current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            
            # Calculate returns
            total_return = (self.current_balance - self.starting_balance) / self.starting_balance
            daily_return = self.daily_pnl / self.starting_balance
            
            update_info = {
                'pnl': pnl,
                'current_balance': self.current_balance,
                'daily_pnl': self.daily_pnl,
                'total_pnl': self.total_pnl,
                'total_return': total_return,
                'daily_return': daily_return,
                'current_drawdown': current_drawdown,
                'peak_balance': self.peak_balance,
                'trades_today': self.trades_today,
                'compounding_effect': self.current_balance / self.starting_balance
            }
            
            logger.info(f'💰 Balance updated: ${self.current_balance:.2f} (P&L: ${pnl:.2f}, Return: {total_return:.1%})')
            
            return update_info
            
        except Exception as e:
            logger.error(f'❌ Error updating balance: {e}')
            return {'error': str(e)}
    
    def can_trade(self) -> bool:
        """Check if trading is allowed based on risk limits"""
        try:
            # Check daily loss limit
            daily_loss_pct = abs(self.daily_pnl) / self.starting_balance if self.daily_pnl < 0 else 0
            if daily_loss_pct >= self.max_daily_loss:
                logger.warning(f'⚠️ Daily loss limit reached: {daily_loss_pct:.1%}')
                return False
            
            # Check maximum drawdown
            current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            if current_drawdown >= self.max_drawdown:
                logger.warning(f'⚠️ Maximum drawdown reached: {current_drawdown:.1%}')
                return False
            
            # Check minimum balance
            if self.current_balance <= 0:
                logger.warning('⚠️ Insufficient balance for trading')
                return False
            
            return True
            
        except Exception as e:
            logger.error(f'❌ Error checking trading permission: {e}')
            return False
    
    def set_risk_percentage(self, new_risk: float) -> bool:
        """Change risk percentage (1% to 2%)"""
        try:
            if not (self.min_risk_per_trade <= new_risk <= self.max_risk_per_trade):
                logger.error(f'❌ Risk {new_risk:.1%} outside allowed range ({self.min_risk_per_trade:.1%} - {self.max_risk_per_trade:.1%})')
                return False
            
            old_risk = self.risk_per_trade
            self.risk_per_trade = new_risk
            
            # Update configuration file
            self.save_configuration()
            
            logger.info(f'✅ Risk per trade changed: {old_risk:.1%} → {new_risk:.1%}')
            return True
            
        except Exception as e:
            logger.error(f'❌ Error setting risk percentage: {e}')
            return False
    
    def save_configuration(self):
        """Save current configuration to file"""
        try:
            config = {
                "starting_balance": self.starting_balance,
                "risk_per_trade": self.risk_per_trade,
                "reward_per_trade": self.reward_per_trade,
                "risk_reward_ratio": self.risk_reward_ratio,
                "compounding_enabled": self.compounding_enabled,
                "max_risk_per_trade": self.max_risk_per_trade,
                "min_risk_per_trade": self.min_risk_per_trade,
                "min_position_size": self.min_position_size,
                "max_position_size": self.max_position_size,
                "max_daily_loss": self.max_daily_loss,
                "max_drawdown": self.max_drawdown,
                "last_updated": datetime.now().isoformat(),
                "master_document_compliance": {
                    "description": "100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md",
                    "starting_capital": "$100 equivalent",
                    "risk_management": f"{self.risk_per_trade:.1%} risk per trade",
                    "reward_target": "2.5% reward per trade",
                    "risk_reward_ratio": "Fixed 2.5:1",
                    "compounding": "Enabled for balance growth"
                }
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info('✅ Configuration saved successfully')
            
        except Exception as e:
            logger.error(f'❌ Error saving configuration: {e}')
    
    def get_status(self) -> Dict:
        """Get current money management status"""
        session_duration = (datetime.now() - self.session_start).total_seconds() / 3600
        current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        
        return {
            'current_balance': self.current_balance,
            'starting_balance': self.starting_balance,
            'total_pnl': self.total_pnl,
            'daily_pnl': self.daily_pnl,
            'total_return': (self.current_balance - self.starting_balance) / self.starting_balance,
            'daily_return': self.daily_pnl / self.starting_balance,
            'current_drawdown': current_drawdown,
            'peak_balance': self.peak_balance,
            'trades_today': self.trades_today,
            'session_duration_hours': session_duration,
            'risk_per_trade': self.risk_per_trade,
            'reward_per_trade': self.reward_per_trade,
            'risk_reward_ratio': self.risk_reward_ratio,
            'compounding_enabled': self.compounding_enabled,
            'can_trade': self.can_trade(),
            'compounding_multiplier': self.current_balance / self.starting_balance
        }
    
    def reset_daily_stats(self):
        """Reset daily statistics (call at start of new day)"""
        self.daily_pnl = 0.0
        self.trades_today = 0
        logger.info('🔄 Daily statistics reset')

def create_money_management_config():
    """Create money management configuration file"""
    manager = EnhancedMoneyManager()
    return manager

if __name__ == '__main__':
    # Test the money management system
    print('💰 ENHANCED MONEY MANAGEMENT SYSTEM')
    print('=' * 40)
    
    manager = EnhancedMoneyManager()
    
    # Test position sizing
    btc_price = 45000.0
    position_size, details = manager.calculate_position_size(btc_price)
    
    print(f'📊 Position Size Test:')
    print(f'   BTC Price: ${btc_price:,.2f}')
    print(f'   Position Size: {position_size:.6f} BTC')
    print(f'   Risk Amount: ${details["actual_risk"]:.2f}')
    print(f'   Target Profit: ${details["target_profit"]:.2f}')
    
    # Test risk change
    print(f'\n⚖️ Risk Change Test:')
    print(f'   Current Risk: {manager.risk_per_trade:.1%}')
    manager.set_risk_percentage(0.02)  # Change to 2%
    print(f'   New Risk: {manager.risk_per_trade:.1%}')
    
    # Test balance update
    print(f'\n💰 Balance Update Test:')
    test_pnl = 5.0
    update_info = manager.update_balance(test_pnl, 'LONG_TAKE_PROFIT')
    print(f'   P&L: ${test_pnl:.2f}')
    print(f'   New Balance: ${update_info["current_balance"]:.2f}')
    print(f'   Total Return: {update_info["total_return"]:.1%}')
    
    print(f'\n✅ Money management system test completed!')
