#!/usr/bin/env python3
"""
Binance Connector Module - Independent Execution
Run this module independently for testing Binance connectivity
"""

import sys
import os

# Add shared config to path
sys.path.append('../shared_config')

from binance_real_money_connector import BinanceRealMoneyConnector
from shared_config.shared_utilities import setup_logging

def main():
    """Run Binance connector module independently"""
    logger = setup_logging('binance_connector_standalone')
    
    print("🔗 Binance Connector Module - Independent Execution")
    print("=" * 60)
    
    try:
        # Initialize connector
        connector = BinanceRealMoneyConnector()
        
        # Test connection
        balance = connector.get_account_balance()
        if balance:
            print(f"✅ Connection successful")
            print(f"💰 Total USDT Value: ${balance['total_usdt_value']:.2f}")
            print(f"📊 BTC Position: {balance['btc']['netAsset']:.6f}")
            print(f"💵 USDT Balance: {balance['usdt']['netAsset']:.2f}")
        
        # Test market data
        market_data = connector.get_market_data()
        if market_data is not None:
            current_price = market_data['close'].iloc[-1]
            current_rsi = market_data['rsi'].iloc[-1]
            current_vwap = market_data['vwap'].iloc[-1]
            
            print(f"📈 Current Price: ${current_price:.2f}")
            print(f"📊 RSI: {current_rsi:.2f}")
            print(f"📈 VWAP: ${current_vwap:.2f}")
        
        print("✅ Binance connector module ready for integration")
        
    except Exception as e:
        print(f"❌ Module execution failed: {e}")
        logger.error(f"Module execution failed: {e}")

if __name__ == "__main__":
    main()
