#!/usr/bin/env python3
"""
Minimal test to identify issues
"""

import pandas as pd
import torch
import json
from datetime import datetime

def main():
    print("🔍 MINIMAL TEST")
    print("="*40)
    
    try:
        # Test 1: Data loading
        print("📊 Testing data loading...")
        df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
        print(f"✅ Data loaded: {len(df):,} samples")
        
        # Test 2: Model file check
        print("🔍 Testing model file...")
        checkpoint = torch.load('quick_best_model.pth', map_location='cpu', weights_only=False)
        print(f"✅ Model loaded: {list(checkpoint.keys())}")
        
        # Test 3: Simple calculation
        print("📊 Testing calculations...")
        sample = df.tail(10)
        result = {
            'samples': len(sample),
            'price_range': [float(sample['close'].min()), float(sample['close'].max())],
            'test_completed': True,
            'timestamp': datetime.now().isoformat()
        }
        
        # Test 4: File writing
        print("💾 Testing file writing...")
        with open('minimal_test_results.json', 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        print("✅ All tests passed!")
        print("📊 Results saved to minimal_test_results.json")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
