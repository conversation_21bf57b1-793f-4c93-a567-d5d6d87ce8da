#!/usr/bin/env python3
"""
MARGIN MONEY MANAGER
Implements exact $1 SL / $2.5 TP using 10:1 leverage with 0.25% grid levels
Calculates profits on actual leverage for precise dollar amounts
"""

import sys
import json
import logging
import decimal
from datetime import datetime
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP

sys.path.append('01_binance_connector')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarginMoneyManager:
    """
    Margin Money Manager for Exact Dollar Amounts with Leverage
    
    SPECIFICATIONS:
    - Account Balance: $100 equivalent
    - Target SL: $1.00 (1% of account) at 0.1% price movement
    - Target TP: $2.50 (2.5% of account) at 0.25% price movement  
    - Leverage: 10:1 to achieve exact dollar amounts
    - Position Size: $1,000 (leveraged)
    - Margin Required: $100 (full account)
    
    MATHEMATICAL WORKINGS:
    Position Size = TP Amount ÷ TP Grid Level = $2.50 ÷ 0.0025 = $1,000
    Leverage = Position Size ÷ Account Balance = $1,000 ÷ $100 = 10:1
    SL Grid Level = SL Amount ÷ Position Size = $1.00 ÷ $1,000 = 0.1%
    """
    
    def __init__(self, account_balance=100.0):
        # Core Parameters
        self.account_balance = account_balance           # $100 equivalent
        self.target_sl_amount = 1.0                     # $1.00 stop loss
        self.target_tp_amount = 2.5                     # $2.50 take profit
        self.tp_grid_level = 0.0025                     # 0.25% TP grid level (specified)
        
        # Calculated Parameters
        self.position_size_usd = self.target_tp_amount / self.tp_grid_level  # $1,000
        self.leverage = self.position_size_usd / self.account_balance        # 10:1
        self.sl_grid_level = self.target_sl_amount / self.position_size_usd  # 0.1%
        self.margin_required = self.position_size_usd / self.leverage        # $100
        
        # Risk Management
        self.max_leverage = 20                          # Binance max for BTCUSDT
        self.liquidation_buffer = 0.05                  # 5% buffer from liquidation
        self.min_balance_threshold = 50.0               # Minimum $50 to trade
        
        # Validation
        self.risk_reward_ratio = self.target_tp_amount / self.target_sl_amount  # 2.5:1
        
        logger.info("MARGIN MONEY MANAGER INITIALIZED")
        logger.info(f"Account Balance: ${self.account_balance:.2f}")
        logger.info(f"Target SL: ${self.target_sl_amount:.2f} at {self.sl_grid_level:.3%} grid")
        logger.info(f"Target TP: ${self.target_tp_amount:.2f} at {self.tp_grid_level:.3%} grid")
        logger.info(f"Position Size: ${self.position_size_usd:.2f}")
        logger.info(f"Leverage: {self.leverage:.0f}:1")
        logger.info(f"Margin Required: ${self.margin_required:.2f}")
        logger.info(f"Risk-Reward Ratio: {self.risk_reward_ratio:.1f}:1")
    
    def initialize_margin_connection(self):
        """Initialize margin trading connection"""
        try:
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Get current balance
            balance_info = self.binance.get_isolated_margin_balance()
            current_balance = balance_info['total_usdt_value']
            
            logger.info("MARGIN CONNECTION INITIALIZED")
            logger.info(f"Current Account Balance: ${current_balance:.2f}")
            
            # Update account balance if different
            if abs(current_balance - self.account_balance) > 1.0:
                self.account_balance = current_balance
                self._recalculate_parameters()
            
            return True
            
        except Exception as e:
            logger.error(f"Margin connection initialization failed: {e}")
            return False
    
    def _recalculate_parameters(self):
        """Recalculate parameters based on current balance"""
        if self.account_balance >= 100.0:
            # Use full $100 equivalent parameters
            self.target_sl_amount = 1.0
            self.target_tp_amount = 2.5
        else:
            # Scale proportionally
            scale_factor = self.account_balance / 100.0
            self.target_sl_amount = 1.0 * scale_factor
            self.target_tp_amount = 2.5 * scale_factor
        
        # Recalculate derived parameters
        self.position_size_usd = self.target_tp_amount / self.tp_grid_level
        self.leverage = self.position_size_usd / self.account_balance
        self.sl_grid_level = self.target_sl_amount / self.position_size_usd
        self.margin_required = self.position_size_usd / self.leverage
        
        logger.info("PARAMETERS RECALCULATED")
        logger.info(f"Updated Position Size: ${self.position_size_usd:.2f}")
        logger.info(f"Updated Leverage: {self.leverage:.1f}:1")
    
    def calculate_margin_position(self, btc_price):
        """
        Calculate margin position with exact dollar amounts
        
        CALCULATION WORKINGS:
        1. Position Size = $2.50 ÷ 0.0025 = $1,000 (for TP at 0.25% grid)
        2. BTC Quantity = $1,000 ÷ BTC Price
        3. Leverage = $1,000 ÷ $100 = 10:1
        4. SL Level = $1.00 ÷ $1,000 = 0.1% (for SL at exact $1)
        5. Margin = $1,000 ÷ 10 = $100 (uses full account)
        """
        try:
            # Calculate BTC quantity needed
            btc_quantity = self.position_size_usd / btc_price
            
            # Get Binance symbol requirements
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            step_size = None
            min_qty = None
            min_notional = None
            tick_size = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                    min_qty = float(filter_item['minQty'])
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    min_notional = float(filter_item['minNotional'])
                elif filter_item['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_item['tickSize'])
            
            # Round BTC quantity to step size
            decimal.getcontext().rounding = ROUND_DOWN
            qty_decimal = Decimal(str(btc_quantity))
            step_decimal = Decimal(str(step_size))
            btc_quantity = float(qty_decimal.quantize(step_decimal))
            
            # Ensure minimum requirements
            btc_quantity = max(btc_quantity, min_qty)
            
            # Recalculate actual position size
            actual_position_size = btc_quantity * btc_price
            
            # Recalculate actual dollar amounts based on actual position
            actual_sl_amount = actual_position_size * self.sl_grid_level
            actual_tp_amount = actual_position_size * self.tp_grid_level
            actual_leverage = actual_position_size / self.account_balance
            actual_margin = actual_position_size / actual_leverage
            
            # Calculate prices with proper rounding
            decimal.getcontext().rounding = ROUND_HALF_UP
            price_decimal = Decimal(str(btc_price))
            tick_decimal = Decimal(str(tick_size))
            
            entry_price = float(price_decimal.quantize(tick_decimal))
            sl_price = float(Decimal(str(entry_price * (1 - self.sl_grid_level))).quantize(tick_decimal))
            tp_price = float(Decimal(str(entry_price * (1 + self.tp_grid_level))).quantize(tick_decimal))
            
            # Calculate liquidation price (approximate)
            liquidation_price = entry_price * (1 - (actual_margin / actual_position_size) + self.liquidation_buffer)
            
            position_data = {
                # Core Position Data
                'entry_price': entry_price,
                'btc_quantity': btc_quantity,
                'position_size_usd': actual_position_size,
                'leverage': actual_leverage,
                'margin_required': actual_margin,
                
                # Price Levels
                'sl_price': sl_price,
                'tp_price': tp_price,
                'liquidation_price': liquidation_price,
                
                # Grid Levels
                'sl_grid_level': self.sl_grid_level,
                'tp_grid_level': self.tp_grid_level,
                
                # Dollar Amounts (Exact)
                'sl_amount': actual_sl_amount,
                'tp_amount': actual_tp_amount,
                'risk_reward_ratio': actual_tp_amount / actual_sl_amount,
                
                # Account Impact
                'account_balance': self.account_balance,
                'margin_utilization': (actual_margin / self.account_balance) * 100,
                'sl_account_impact': (actual_sl_amount / self.account_balance) * 100,
                'tp_account_impact': (actual_tp_amount / self.account_balance) * 100,
                
                # Binance Requirements
                'step_size': step_size,
                'tick_size': tick_size,
                'min_notional': min_notional,
                'min_qty': min_qty,
                
                # Formatted Strings
                'quantity_str': f"{btc_quantity:.8f}".rstrip('0').rstrip('.'),
                'entry_price_str': f"{entry_price:.2f}",
                'sl_price_str': f"{sl_price:.2f}",
                'tp_price_str': f"{tp_price:.2f}",
                'sl_limit_str': f"{sl_price * 0.999:.2f}"
            }
            
            logger.info("MARGIN POSITION CALCULATED")
            logger.info(f"Position Size: ${actual_position_size:.2f}")
            logger.info(f"BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"Leverage: {actual_leverage:.1f}:1")
            logger.info(f"Margin Used: ${actual_margin:.2f} ({position_data['margin_utilization']:.1f}%)")
            logger.info(f"SL: ${sl_price:.2f} = ${actual_sl_amount:.2f} ({self.sl_grid_level:.3%} grid)")
            logger.info(f"TP: ${tp_price:.2f} = ${actual_tp_amount:.2f} ({self.tp_grid_level:.3%} grid)")
            logger.info(f"Risk-Reward: {actual_tp_amount/actual_sl_amount:.1f}:1")
            logger.info(f"Liquidation: ${liquidation_price:.2f}")
            
            return position_data
            
        except Exception as e:
            logger.error(f"Margin position calculation failed: {e}")
            return None
    
    def validate_margin_safety(self, position_data):
        """Validate margin position meets safety requirements"""
        try:
            safety_checks = {
                'sufficient_balance': self.account_balance >= self.min_balance_threshold,
                'leverage_within_limits': position_data['leverage'] <= self.max_leverage,
                'margin_available': position_data['margin_required'] <= self.account_balance,
                'liquidation_safe': position_data['sl_price'] > position_data['liquidation_price'],
                'dollar_amounts_correct': (
                    abs(position_data['sl_amount'] - self.target_sl_amount) < 0.01 and
                    abs(position_data['tp_amount'] - self.target_tp_amount) < 0.01
                ),
                'grid_levels_correct': (
                    abs(position_data['sl_grid_level'] - self.sl_grid_level) < 0.0001 and
                    abs(position_data['tp_grid_level'] - self.tp_grid_level) < 0.0001
                ),
                'risk_reward_ok': position_data['risk_reward_ratio'] >= 2.4,  # Allow small variance
                'min_notional_met': position_data['position_size_usd'] >= position_data['min_notional']
            }
            
            all_safe = all(safety_checks.values())
            
            logger.info("MARGIN SAFETY VALIDATION")
            for check, result in safety_checks.items():
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"  {check}: {status}")
            
            if all_safe:
                logger.info("✅ MARGIN SAFETY VALIDATION: PASSED")
            else:
                logger.warning("⚠️ MARGIN SAFETY VALIDATION: FAILED")
            
            return all_safe, safety_checks
            
        except Exception as e:
            logger.error(f"Margin safety validation failed: {e}")
            return False, {}
    
    def calculate_pnl_on_leverage(self, entry_price, exit_price, position_data):
        """
        Calculate P&L on actual leverage
        
        LEVERAGE P&L CALCULATION:
        Position Size = $1,000 (leveraged)
        Price Change % = (Exit Price - Entry Price) / Entry Price
        P&L = Position Size × Price Change %
        
        Example:
        Entry: $120,000, Exit: $120,300 (0.25% up)
        P&L = $1,000 × 0.0025 = $2.50 ✓
        """
        try:
            price_change_percent = (exit_price - entry_price) / entry_price
            pnl = position_data['position_size_usd'] * price_change_percent
            
            # Determine if SL or TP was hit
            if abs(exit_price - position_data['sl_price']) < abs(exit_price - position_data['tp_price']):
                result_type = 'STOP_LOSS'
                expected_pnl = -position_data['sl_amount']
            else:
                result_type = 'TAKE_PROFIT'
                expected_pnl = position_data['tp_amount']
            
            pnl_data = {
                'entry_price': entry_price,
                'exit_price': exit_price,
                'price_change_percent': price_change_percent * 100,
                'position_size_usd': position_data['position_size_usd'],
                'leverage': position_data['leverage'],
                'actual_pnl': pnl,
                'expected_pnl': expected_pnl,
                'pnl_difference': abs(pnl - expected_pnl),
                'result_type': result_type,
                'account_impact_percent': (pnl / self.account_balance) * 100,
                'new_account_balance': self.account_balance + pnl
            }
            
            logger.info("LEVERAGE P&L CALCULATED")
            logger.info(f"Price Change: {price_change_percent*100:.3f}%")
            logger.info(f"Position Size: ${position_data['position_size_usd']:.2f}")
            logger.info(f"Leverage: {position_data['leverage']:.1f}:1")
            logger.info(f"Actual P&L: ${pnl:.2f}")
            logger.info(f"Expected P&L: ${expected_pnl:.2f}")
            logger.info(f"Result: {result_type}")
            logger.info(f"Account Impact: {pnl_data['account_impact_percent']:.1f}%")
            
            return pnl_data
            
        except Exception as e:
            logger.error(f"Leverage P&L calculation failed: {e}")
            return None
    
    def get_margin_status(self):
        """Get comprehensive margin trading status"""
        try:
            status = {
                # Account Information
                'account_balance': self.account_balance,
                'min_balance_threshold': self.min_balance_threshold,
                
                # Position Parameters
                'target_sl_amount': self.target_sl_amount,
                'target_tp_amount': self.target_tp_amount,
                'position_size_usd': self.position_size_usd,
                'leverage': self.leverage,
                'margin_required': self.margin_required,
                
                # Grid Levels
                'sl_grid_level': self.sl_grid_level,
                'tp_grid_level': self.tp_grid_level,
                'risk_reward_ratio': self.risk_reward_ratio,
                
                # Risk Management
                'max_leverage': self.max_leverage,
                'liquidation_buffer': self.liquidation_buffer,
                
                # System Status
                'margin_trading_enabled': True,
                'exact_dollar_amounts': True,
                'grid_level_integration': True
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get margin status: {e}")
            return None

def main():
    """Test margin money manager"""
    print("🚀 MARGIN MONEY MANAGER TEST")
    print("$100 Account | $1 SL (0.1% grid) | $2.5 TP (0.25% grid) | 10:1 Leverage")
    print("="*80)
    
    try:
        # Initialize margin manager
        margin_manager = MarginMoneyManager()
        
        # Initialize margin connection
        if not margin_manager.initialize_margin_connection():
            print("❌ Failed to initialize margin connection")
            return
        
        # Get current BTC price
        ticker = margin_manager.binance.client.get_symbol_ticker(symbol='BTCUSDT')
        btc_price = float(ticker['price'])
        
        # Calculate margin position
        position_data = margin_manager.calculate_margin_position(btc_price)
        if not position_data:
            print("❌ Failed to calculate margin position")
            return
        
        # Validate margin safety
        is_safe, safety_checks = margin_manager.validate_margin_safety(position_data)
        
        # Get margin status
        status = margin_manager.get_margin_status()
        
        print(f"\n📊 MARGIN MONEY MANAGEMENT STATUS:")
        print(f"  Account Balance: ${status['account_balance']:.2f}")
        print(f"  Position Size: ${position_data['position_size_usd']:.2f}")
        print(f"  Leverage: {position_data['leverage']:.1f}:1")
        print(f"  Margin Required: ${position_data['margin_required']:.2f}")
        print(f"  Margin Utilization: {position_data['margin_utilization']:.1f}%")
        
        print(f"\n📋 EXACT DOLLAR AMOUNTS:")
        print(f"  Stop Loss: ${position_data['sl_amount']:.2f} at {position_data['sl_grid_level']:.3%} grid")
        print(f"  Take Profit: ${position_data['tp_amount']:.2f} at {position_data['tp_grid_level']:.3%} grid")
        print(f"  Risk-Reward: {position_data['risk_reward_ratio']:.1f}:1")
        
        print(f"\n📈 PRICE LEVELS:")
        print(f"  Entry: ${position_data['entry_price']:.2f}")
        print(f"  Stop Loss: ${position_data['sl_price']:.2f}")
        print(f"  Take Profit: ${position_data['tp_price']:.2f}")
        print(f"  Liquidation: ${position_data['liquidation_price']:.2f}")
        
        print(f"\n✅ MARGIN MONEY MANAGER READY!")
        print(f"✅ Leverage Integration: OPERATIONAL")
        print(f"✅ Exact Dollar Amounts: VERIFIED")
        print(f"✅ Grid Level Integration: CONFIRMED")
        print(f"✅ Safety Validation: {'PASSED' if is_safe else 'FAILED'}")
        
        if is_safe:
            print(f"\n🚀 READY FOR MARGIN TRADING WITH EXACT $1 SL / $2.5 TP!")
        else:
            print(f"\n⚠️ SAFETY CHECKS FAILED - REVIEW REQUIRED")
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
