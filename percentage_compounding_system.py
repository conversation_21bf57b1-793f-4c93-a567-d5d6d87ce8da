#!/usr/bin/env python3
"""
PERCENTAGE-BASED COMPOUNDING SYSTEM
Professional percentage-based risk management with automatic compounding
Compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
"""

import sys
import json
import time
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

class PercentageCompoundingSystem:
    """
    Professional percentage-based compounding system
    Risk and reward scale automatically with account balance
    """
    
    def __init__(self, risk_percentage=0.002, reward_multiplier=2.5):
        # Core parameters
        self.risk_percentage = risk_percentage      # 0.2% of account per trade
        self.reward_multiplier = reward_multiplier  # 2.5x risk for reward
        self.reward_percentage = risk_percentage * reward_multiplier  # 0.5% of account
        
        # Risk management
        self.max_daily_risk = 0.01                  # 1% max daily risk
        self.drawdown_threshold = 0.05              # 5% drawdown protection
        self.min_balance_threshold = 500.0          # Minimum balance to continue
        
        # State tracking
        self.starting_balance = 0.0
        self.current_balance = 0.0
        self.peak_balance = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.daily_risk_used = 0.0
        self.last_trade_date = None
        
        # Components
        self.binance = None
        self.telegram_bot = None
        
        print("📊 PERCENTAGE-BASED COMPOUNDING SYSTEM INITIALIZED")
        print(f"Risk: {self.risk_percentage:.1%} of account per trade")
        print(f"Reward: {self.reward_percentage:.1%} of account per trade")
        print(f"Risk-Reward Ratio: {self.reward_multiplier:.1f}:1")
        print(f"Compounding: Automatic after every trade")
    
    def initialize_components(self):
        """Initialize trading components"""
        try:
            # Initialize Binance
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Get starting balance
            balance_info = self.binance.get_isolated_margin_balance()
            self.starting_balance = balance_info['total_usdt_value']
            self.current_balance = self.starting_balance
            self.peak_balance = self.starting_balance
            
            print(f"✅ Starting Balance: ${self.starting_balance:.2f}")
            
            # Initialize Telegram
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                print("✅ Telegram bot: OPERATIONAL")
            except Exception as e:
                print(f"⚠️ Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            print(f"❌ Component initialization failed: {e}")
            return False
    
    def calculate_percentage_position(self):
        """Calculate position based on account percentage"""
        try:
            # Get current balance
            balance_info = self.binance.get_isolated_margin_balance()
            self.current_balance = balance_info['total_usdt_value']
            usdt_balance = balance_info['usdt_balance']
            
            # Update peak balance
            if self.current_balance > self.peak_balance:
                self.peak_balance = self.current_balance
            
            # Check drawdown protection
            drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            if drawdown > self.drawdown_threshold:
                # Reduce risk by 25% during drawdown
                effective_risk_percentage = self.risk_percentage * 0.75
                print(f"⚠️ Drawdown protection active: {drawdown:.1%}")
            else:
                effective_risk_percentage = self.risk_percentage
            
            # Calculate risk and reward amounts
            risk_amount = self.current_balance * effective_risk_percentage
            reward_amount = self.current_balance * (effective_risk_percentage * self.reward_multiplier)
            
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate position size for risk amount
            # Assuming we need position size that gives us risk_amount loss at 0.2% price movement
            position_size_for_risk = risk_amount / 0.002  # 0.2% grid level
            
            # Ensure position doesn't exceed available USDT
            max_position = usdt_balance * 0.95  # Use max 95% of USDT
            position_size = min(position_size_for_risk, max_position)
            
            # Calculate BTC quantity
            btc_quantity = position_size / current_price
            btc_quantity = max(0.00001, round(btc_quantity, 8))
            
            # Recalculate actual position and amounts
            actual_position_size = btc_quantity * current_price
            actual_risk_amount = actual_position_size * 0.002  # 0.2% grid
            actual_reward_amount = actual_position_size * 0.005  # 0.5% grid (2.5x risk)
            
            # Calculate grid levels
            sl_grid_level = actual_risk_amount / actual_position_size
            tp_grid_level = actual_reward_amount / actual_position_size
            
            # Calculate prices
            entry_price = current_price
            stop_loss_price = round(entry_price * (1 - sl_grid_level), 2)
            take_profit_price = round(entry_price * (1 + tp_grid_level), 2)
            
            position_data = {
                'current_balance': self.current_balance,
                'usdt_balance': usdt_balance,
                'peak_balance': self.peak_balance,
                'drawdown': drawdown,
                'effective_risk_percentage': effective_risk_percentage,
                'target_risk_amount': risk_amount,
                'target_reward_amount': reward_amount,
                'actual_risk_amount': actual_risk_amount,
                'actual_reward_amount': actual_reward_amount,
                'position_size': actual_position_size,
                'btc_quantity': btc_quantity,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'sl_grid_level': sl_grid_level,
                'tp_grid_level': tp_grid_level,
                'risk_percentage': effective_risk_percentage * 100,
                'reward_percentage': (effective_risk_percentage * self.reward_multiplier) * 100,
                'balance_usage': (actual_position_size / usdt_balance) * 100,
                'growth_from_start': ((self.current_balance - self.starting_balance) / self.starting_balance) * 100
            }
            
            print(f"📊 PERCENTAGE POSITION CALCULATED:")
            print(f"  Current Balance: ${self.current_balance:.2f}")
            print(f"  Risk: ${actual_risk_amount:.2f} ({effective_risk_percentage:.1%})")
            print(f"  Reward: ${actual_reward_amount:.2f} ({effective_risk_percentage * self.reward_multiplier:.1%})")
            print(f"  Position: ${actual_position_size:.2f}")
            print(f"  Growth from Start: {position_data['growth_from_start']:+.1f}%")
            
            return position_data
            
        except Exception as e:
            print(f"❌ Percentage position calculation failed: {e}")
            return None
    
    def check_daily_risk_limit(self, risk_amount):
        """Check if daily risk limit would be exceeded"""
        today = datetime.now().date()
        
        # Reset daily risk if new day
        if self.last_trade_date != today:
            self.daily_risk_used = 0.0
            self.last_trade_date = today
        
        # Check if adding this trade would exceed daily limit
        max_daily_risk_amount = self.current_balance * self.max_daily_risk
        if (self.daily_risk_used + risk_amount) > max_daily_risk_amount:
            return False, max_daily_risk_amount - self.daily_risk_used
        
        return True, max_daily_risk_amount - self.daily_risk_used - risk_amount
    
    def execute_percentage_trade(self):
        """Execute a single percentage-based trade"""
        try:
            print(f"\n📊 EXECUTING PERCENTAGE TRADE #{self.total_trades + 1}")
            
            # Calculate position with percentage-based sizing
            position_data = self.calculate_percentage_position()
            if not position_data:
                return False
            
            # Check daily risk limit
            can_trade, remaining_daily_risk = self.check_daily_risk_limit(position_data['actual_risk_amount'])
            if not can_trade:
                print(f"⚠️ Daily risk limit reached. Remaining: ${remaining_daily_risk:.2f}")
                return False
            
            # Check minimum balance
            if self.current_balance < self.min_balance_threshold:
                print(f"⚠️ Balance below minimum threshold: ${self.current_balance:.2f} < ${self.min_balance_threshold:.2f}")
                return False
            
            # Send pre-trade notification
            if self.telegram_bot:
                pre_trade_message = f"""
📊 PERCENTAGE COMPOUNDING TRADE #{self.total_trades + 1}

💰 ACCOUNT STATUS:
- Current Balance: ${position_data['current_balance']:.2f}
- Peak Balance: ${position_data['peak_balance']:.2f}
- Growth from Start: {position_data['growth_from_start']:+.1f}%
- Drawdown: {position_data['drawdown']:.1%}

🎯 PERCENTAGE PARAMETERS:
- Risk: ${position_data['actual_risk_amount']:.2f} ({position_data['risk_percentage']:.2f}%)
- Reward: ${position_data['actual_reward_amount']:.2f} ({position_data['reward_percentage']:.2f}%)
- Risk-Reward: {position_data['actual_reward_amount']/position_data['actual_risk_amount']:.1f}:1

📊 POSITION DETAILS:
- Position Size: ${position_data['position_size']:.2f}
- BTC Quantity: {position_data['btc_quantity']:.8f}
- Entry: ${position_data['entry_price']:,.2f}
- SL: ${position_data['stop_loss_price']:,.2f}
- TP: ${position_data['take_profit_price']:,.2f}

🔄 AUTOMATIC COMPOUNDING ACTIVE
Risk and reward scale with account balance!
"""
                self.telegram_bot.send_message(pre_trade_message)
            
            # Execute entry order
            quantity_str = f"{position_data['btc_quantity']:.8f}".rstrip('0').rstrip('.')
            
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=quantity_str
            )
            
            entry_order_id = buy_order['orderId']
            entry_time = datetime.now()
            
            print(f"🎉 ENTRY ORDER EXECUTED: {entry_order_id}")
            
            # Wait and get execution details
            time.sleep(3)
            
            order_details = self.binance.client.get_order(symbol='BTCUSDT', orderId=entry_order_id)
            actual_quantity = float(order_details['executedQty'])
            
            # Calculate actual fill price
            if 'fills' in buy_order and buy_order['fills']:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_entry_price = total_cost / actual_quantity
            else:
                actual_entry_price = position_data['entry_price']
            
            actual_cost = actual_quantity * actual_entry_price
            
            # Recalculate final amounts based on actual execution
            final_risk_amount = actual_cost * position_data['sl_grid_level']
            final_reward_amount = actual_cost * position_data['tp_grid_level']
            final_stop_loss = round(actual_entry_price * (1 - position_data['sl_grid_level']), 2)
            final_take_profit = round(actual_entry_price * (1 + position_data['tp_grid_level']), 2)
            
            # Place exit orders
            quantity_str = f"{actual_quantity:.8f}".rstrip('0').rstrip('.')
            
            # Place Take Profit order
            tp_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='SELL',
                type='LIMIT',
                timeInForce='GTC',
                quantity=quantity_str,
                price=f"{final_take_profit:.2f}"
            )
            
            # Place Stop Loss order
            sl_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='SELL',
                type='STOP_LOSS_LIMIT',
                timeInForce='GTC',
                quantity=quantity_str,
                price=f"{final_stop_loss * 0.999:.2f}",
                stopPrice=f"{final_stop_loss:.2f}"
            )
            
            tp_order_id = tp_order['orderId']
            sl_order_id = sl_order['orderId']
            
            print(f"🎯 EXIT ORDERS PLACED!")
            print(f"  Take Profit: {tp_order_id}")
            print(f"  Stop Loss: {sl_order_id}")
            
            # Update daily risk used
            self.daily_risk_used += final_risk_amount
            
            # Send execution notification
            if self.telegram_bot:
                execution_message = f"""
🎉 PERCENTAGE TRADE EXECUTED!

📋 ORDER NUMBERS:
Entry: {entry_order_id}
Take Profit: {tp_order_id}
Stop Loss: {sl_order_id}

📊 EXECUTION DETAILS:
- Entry Price: ${actual_entry_price:,.2f}
- Quantity: {actual_quantity:.8f} BTC
- Cost: ${actual_cost:.2f}

🎯 PERCENTAGE TARGETS:
- Risk: ${final_risk_amount:.2f} ({position_data['risk_percentage']:.2f}% of balance)
- Reward: ${final_reward_amount:.2f} ({position_data['reward_percentage']:.2f}% of balance)
- SL: ${final_stop_loss:,.2f}
- TP: ${final_take_profit:,.2f}

🔄 COMPOUNDING STATUS:
After WIN: Balance → ${self.current_balance + final_reward_amount:.2f}
After LOSS: Balance → ${self.current_balance - final_risk_amount:.2f}

📊 MONITORING UNTIL COMPLETION...
"""
                self.telegram_bot.send_message(execution_message)
            
            # Store trade data for monitoring
            trade_data = {
                'entry_order_id': entry_order_id,
                'tp_order_id': tp_order_id,
                'sl_order_id': sl_order_id,
                'entry_price': actual_entry_price,
                'quantity': actual_quantity,
                'cost': actual_cost,
                'stop_loss_price': final_stop_loss,
                'take_profit_price': final_take_profit,
                'risk_amount': final_risk_amount,
                'reward_amount': final_reward_amount,
                'risk_percentage': position_data['risk_percentage'],
                'reward_percentage': position_data['reward_percentage'],
                'balance_before': self.current_balance,
                'entry_time': entry_time
            }
            
            # Monitor until completion
            result = self.monitor_trade_completion(trade_data)
            
            if result:
                # Update statistics and balance
                self.total_trades += 1
                if result['result'] == 'WIN':
                    self.winning_trades += 1
                    self.current_balance += result['pnl']
                else:
                    self.losing_trades += 1
                    self.current_balance -= abs(result['pnl'])
                
                # Send completion notification with compounding update
                self.send_compounding_completion_notification(result)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Percentage trade execution failed: {e}")
            return False
    
    def monitor_trade_completion(self, trade_data):
        """Monitor trade until completion"""
        try:
            print(f"🔄 MONITORING PERCENTAGE TRADE...")
            
            start_time = datetime.now()
            check_count = 0
            
            while (datetime.now() - start_time).total_seconds() < (24 * 3600):  # 24 hours max
                check_count += 1
                
                # Check orders
                try:
                    tp_order = self.binance.client.get_order(symbol='BTCUSDT', orderId=trade_data['tp_order_id'])
                    sl_order = self.binance.client.get_order(symbol='BTCUSDT', orderId=trade_data['sl_order_id'])
                    
                    if tp_order['status'] == 'FILLED':
                        # Take profit hit
                        try:
                            self.binance.client.cancel_order(symbol='BTCUSDT', orderId=trade_data['sl_order_id'])
                        except:
                            pass
                        
                        return {
                            'result': 'WIN',
                            'result_type': 'TAKE_PROFIT',
                            'exit_price': float(tp_order['price']),
                            'pnl': trade_data['reward_amount'],
                            'exit_order_id': trade_data['tp_order_id'],
                            'exit_time': datetime.now(),
                            'trade_data': trade_data
                        }
                        
                    elif sl_order['status'] == 'FILLED':
                        # Stop loss hit
                        try:
                            self.binance.client.cancel_order(symbol='BTCUSDT', orderId=trade_data['tp_order_id'])
                        except:
                            pass
                        
                        return {
                            'result': 'LOSS',
                            'result_type': 'STOP_LOSS',
                            'exit_price': float(sl_order['price']),
                            'pnl': -trade_data['risk_amount'],
                            'exit_order_id': trade_data['sl_order_id'],
                            'exit_time': datetime.now(),
                            'trade_data': trade_data
                        }
                
                except Exception as e:
                    print(f"Order check failed: {e}")
                
                # Wait before next check
                time.sleep(60)  # Check every minute
                
                # Log progress
                if check_count % 30 == 0:  # Every 30 minutes
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    print(f"⏱️ Monitoring: {elapsed_minutes:.0f} minutes elapsed")
            
            print(f"⏰ Monitoring timeout after 24 hours")
            return None
            
        except Exception as e:
            print(f"❌ Trade monitoring failed: {e}")
            return None
    
    def send_compounding_completion_notification(self, result):
        """Send trade completion notification with compounding details"""
        if not self.telegram_bot:
            return
        
        result_emoji = "🎉" if result['result'] == 'WIN' else "📉"
        duration = (result['exit_time'] - result['trade_data']['entry_time']).total_seconds() / 60
        
        # Calculate new balance and next trade parameters
        new_balance = result['trade_data']['balance_before'] + result['pnl']
        new_risk = new_balance * self.risk_percentage
        new_reward = new_balance * self.reward_percentage
        growth_rate = (result['pnl'] / result['trade_data']['balance_before']) * 100
        total_growth = ((new_balance - self.starting_balance) / self.starting_balance) * 100
        
        completion_message = f"""
{result_emoji} PERCENTAGE COMPOUNDING TRADE COMPLETED!

📊 RESULT: {result['result']} ({result['result_type']})
- Entry: ${result['trade_data']['entry_price']:,.2f}
- Exit: ${result['exit_price']:,.2f}
- P&L: ${result['pnl']:.2f}
- Duration: {duration:.1f} minutes

🔄 AUTOMATIC COMPOUNDING UPDATE:
- Previous Balance: ${result['trade_data']['balance_before']:.2f}
- New Balance: ${new_balance:.2f}
- Growth This Trade: {growth_rate:+.2f}%
- Total Growth: {total_growth:+.1f}%

📈 NEXT TRADE PARAMETERS:
- Next Risk: ${new_risk:.2f} ({self.risk_percentage:.1%})
- Next Reward: ${new_reward:.2f} ({self.reward_percentage:.1%})
- Position Will Be: ~${new_risk/0.002:.0f}

📊 PERFORMANCE SUMMARY:
- Total Trades: {self.total_trades}
- Win Rate: {(self.winning_trades/self.total_trades)*100:.1f}%
- Wins: {self.winning_trades} | Losses: {self.losing_trades}

🎯 COMPOUNDING POWER:
Risk and reward automatically scaled with balance!
No manual adjustments needed.
"""
        
        self.telegram_bot.send_message(completion_message)
    
    def run_percentage_system(self, num_trades=None):
        """Run the percentage-based compounding system"""
        try:
            print(f"📊 STARTING PERCENTAGE-BASED COMPOUNDING SYSTEM")
            print(f"="*70)
            
            if not self.initialize_components():
                return False
            
            # Send system startup notification
            if self.telegram_bot:
                startup_message = f"""
📊 PERCENTAGE-BASED COMPOUNDING SYSTEM STARTED!

💰 PROFESSIONAL PERCENTAGE APPROACH:
- Risk: {self.risk_percentage:.1%} of account per trade
- Reward: {self.reward_percentage:.1%} of account per trade
- Risk-Reward: {self.reward_multiplier:.1f}:1
- Compounding: Automatic after every trade

📊 STARTING PARAMETERS:
- Starting Balance: ${self.starting_balance:.2f}
- Current Risk: ${self.starting_balance * self.risk_percentage:.2f}
- Current Reward: ${self.starting_balance * self.reward_percentage:.2f}

🎯 SYSTEM FEATURES:
✅ Automatic percentage-based position sizing
✅ Exponential compounding growth
✅ Professional risk management
✅ Drawdown protection (5% threshold)
✅ Daily risk limits (1% max)
✅ Real-time balance scaling

🔄 PERCENTAGE COMPOUNDING: ACTIVE
Risk and reward scale automatically with every trade!
"""
                self.telegram_bot.send_message(startup_message)
            
            # Execute trades
            if num_trades:
                # Execute specific number of trades
                for i in range(num_trades):
                    print(f"\n{'='*70}")
                    print(f"PERCENTAGE TRADE {i+1}/{num_trades}")
                    print(f"{'='*70}")
                    
                    if not self.execute_percentage_trade():
                        print(f"❌ Trade {i+1} failed or skipped")
                        continue
                    
                    print(f"✅ Trade {i+1} completed")
                    
                    # Brief pause between trades
                    if i < num_trades - 1:
                        time.sleep(5)
            else:
                # Continuous trading
                trade_count = 0
                while True:
                    trade_count += 1
                    print(f"\n{'='*70}")
                    print(f"PERCENTAGE TRADE {trade_count}")
                    print(f"{'='*70}")
                    
                    if not self.execute_percentage_trade():
                        print(f"❌ Trade {trade_count} failed or skipped")
                        time.sleep(60)  # Wait before retry
                        continue
                    
                    print(f"✅ Trade {trade_count} completed")
                    
                    # Brief pause between trades
                    time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ Percentage system failed: {e}")
            return False

def main():
    """Main execution"""
    print("📊 PERCENTAGE-BASED COMPOUNDING SYSTEM")
    print("Professional percentage-based risk management")
    print("="*70)
    
    try:
        # Configuration options
        print(f"\nRisk Percentage Options:")
        print(f"1. Conservative: 0.2% risk / 0.5% reward")
        print(f"2. Moderate: 0.3% risk / 0.75% reward")
        print(f"3. Aggressive: 0.5% risk / 1.25% reward")
        
        risk_choice = input("\nChoose risk level (1-3, default 1): ") or "1"
        
        if risk_choice == "1":
            risk_pct = 0.002  # 0.2%
        elif risk_choice == "2":
            risk_pct = 0.003  # 0.3%
        elif risk_choice == "3":
            risk_pct = 0.005  # 0.5%
        else:
            risk_pct = 0.002  # Default conservative
        
        system = PercentageCompoundingSystem(risk_percentage=risk_pct)
        
        print(f"\nExecution Options:")
        print(f"1. Execute 1 test trade")
        print(f"2. Execute 5 trades")
        print(f"3. Execute 10 trades")
        print(f"4. Run continuous percentage compounding")
        
        choice = input("\nEnter choice (1-4): ")
        
        if choice == '1':
            system.run_percentage_system(1)
        elif choice == '2':
            system.run_percentage_system(5)
        elif choice == '3':
            system.run_percentage_system(10)
        elif choice == '4':
            system.run_percentage_system()
        else:
            print("Invalid choice")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
