#!/usr/bin/env python3
"""
Quick Retrain TCN-CNN-PPO Model
Fast retraining with current architecture using recent data
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import TCNCNNPPOModel

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickTrainer:
    """Quick retraining system"""
    
    def __init__(self):
        self.model = TCNCNNPPOModel(input_size=135)
        self.binance = None
        
    def initialize_system(self):
        """Initialize training system"""
        try:
            logger.info("🚀 Initializing quick retraining system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            
            logger.info("✅ Quick training system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Quick training initialization failed: {e}")
            return False
    
    def fetch_recent_data(self, days=30):
        """Fetch recent Bitcoin data for quick training"""
        try:
            logger.info(f"📊 Fetching {days} days of recent Bitcoin data...")
            
            # Get recent data from Binance
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # Fetch BTCUSDT 1-hour data
            klines = self.binance.client.get_historical_klines(
                'BTCUSDT',
                '1h',
                start_time.strftime('%Y-%m-%d'),
                end_time.strftime('%Y-%m-%d')
            )
            
            if not klines:
                logger.error("❌ No recent data received")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # Calculate technical indicators
            df['rsi'] = self.calculate_rsi(df['close'])
            df['vwap'] = self.calculate_vwap(df)
            
            # Calculate grid features
            df['grid_level'] = self.calculate_grid_levels(df['close'])
            df['at_grid_level'] = self.check_grid_compliance(df['close'])
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"✅ Fetched {len(df)} recent data points")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch recent data: {e}")
            return None
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_vwap(self, df):
        """Calculate VWAP indicator"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap
    
    def calculate_grid_levels(self, prices):
        """Calculate grid levels (0.25% spacing)"""
        base_price = 100000
        grid_spacing = 0.0025
        return ((prices - base_price) / (base_price * grid_spacing)).round().astype(int)
    
    def check_grid_compliance(self, prices):
        """Check if prices are at exact grid levels"""
        base_price = 100000
        grid_spacing = 0.0025
        tolerance = 0.00001
        
        grid_prices = base_price * (1 + np.round((prices - base_price) / (base_price * grid_spacing)) * grid_spacing)
        return np.abs(prices - grid_prices) / prices <= tolerance
    
    def prepare_training_data(self, df):
        """Prepare data for quick training"""
        try:
            logger.info("🔧 Preparing training data...")
            
            features = []
            labels = []
            
            for i in range(len(df) - 4):
                # Create 135-feature vector
                feature_vector = np.zeros(135)
                
                # Price features (4 points for TCN)
                price_features = df['close'].iloc[i:i+4].values
                feature_vector[:4] = price_features
                
                # Technical indicators
                rsi = df['rsi'].iloc[i+3]
                vwap = df['vwap'].iloc[i+3]
                
                # Grid features
                grid_level = df['grid_level'].iloc[i+3]
                at_grid = df['at_grid_level'].iloc[i+3]
                
                # Fill remaining features with patterns
                feature_vector[4:68] = np.tile([rsi, vwap], 32)  # TCN features
                feature_vector[68:132] = np.tile([rsi, vwap], 32)  # CNN features
                feature_vector[132:135] = [grid_level, float(at_grid), rsi]  # Grid features
                
                features.append(feature_vector)
                
                # Create label (price direction)
                current_price = df['close'].iloc[i+3]
                next_price = df['close'].iloc[i+4] if i+4 < len(df) else current_price
                label = 1 if next_price > current_price else 0
                labels.append(label)
            
            features = np.array(features)
            labels = np.array(labels)
            
            logger.info(f"✅ Prepared {len(features)} training samples")
            return features, labels
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare training data: {e}")
            return None, None
    
    def quick_train(self, features, labels, epochs=50):
        """Quick training with recent data"""
        try:
            logger.info(f"🧠 Quick training for {epochs} epochs...")
            
            # Convert to tensors
            X = torch.FloatTensor(features)
            y = torch.LongTensor(labels)
            
            # Split data
            train_size = int(0.8 * len(X))
            X_train = X[:train_size]
            y_train = y[:train_size]
            X_val = X[train_size:]
            y_val = y[train_size:]
            
            # Setup training
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            
            best_val_acc = 0
            best_model_state = None
            
            # Quick training loop
            for epoch in range(epochs):
                self.model.train()
                
                # Forward pass
                policy_logits, value = self.model(X_train.unsqueeze(1))
                loss = criterion(policy_logits, y_train)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Validation every 10 epochs
                if epoch % 10 == 0:
                    self.model.eval()
                    with torch.no_grad():
                        val_policy, val_value = self.model(X_val.unsqueeze(1))
                        val_pred = torch.argmax(val_policy, dim=1)
                        val_acc = (val_pred == y_val).float().mean().item()
                        
                        if val_acc > best_val_acc:
                            best_val_acc = val_acc
                            best_model_state = self.model.state_dict().copy()
                        
                        logger.info(f"Epoch {epoch}: Loss={loss.item():.4f}, Val Acc={val_acc:.4f}")
            
            # Load best model
            if best_model_state:
                self.model.load_state_dict(best_model_state)
            
            logger.info(f"✅ Quick training completed - Best Val Acc: {best_val_acc:.4f}")
            return best_val_acc > 0.5  # Success if > 50% accuracy
            
        except Exception as e:
            logger.error(f"❌ Quick training failed: {e}")
            return False
    
    def save_model(self):
        """Save the quickly trained model"""
        try:
            model_dir = os.path.join('02_signal_generator', 'models')
            os.makedirs(model_dir, exist_ok=True)
            
            model_path = os.path.join(model_dir, 'best_real_3year_trained_model.pth')
            
            # Save with metadata
            checkpoint = {
                'model_state_dict': self.model.state_dict(),
                'model_architecture': 'TCN-CNN-PPO',
                'training_metadata': {
                    'training_date': datetime.now().isoformat(),
                    'training_type': 'quick_retrain',
                    'input_features': 135,
                    'architecture': 'Enhanced TCN-CNN-PPO'
                },
                'performance_metrics': {
                    'training_method': 'quick_retrain_30_days',
                    'epochs': 50
                }
            }
            
            torch.save(checkpoint, model_path)
            
            logger.info(f"✅ Model saved to: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save model: {e}")
            return False
    
    def run_quick_retrain(self):
        """Run complete quick retraining"""
        try:
            logger.info("🚀 Starting quick TCN-CNN-PPO retraining...")
            
            # Fetch recent data
            df = self.fetch_recent_data(days=30)
            if df is None:
                return False
            
            # Prepare training data
            features, labels = self.prepare_training_data(df)
            if features is None:
                return False
            
            # Quick train
            if not self.quick_train(features, labels, epochs=50):
                return False
            
            # Save model
            if not self.save_model():
                return False
            
            logger.info("✅ Quick retraining completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Quick retraining failed: {e}")
            return False

def main():
    """Main quick retraining function"""
    print("🧠 QUICK TCN-CNN-PPO RETRAINING")
    print("=" * 50)
    print("📋 Using 30 days of recent Bitcoin data")
    print("📋 Fast training with current architecture")
    print("📋 50 epochs for quick deployment")
    print("=" * 50)
    
    trainer = QuickTrainer()
    
    if not trainer.initialize_system():
        print("❌ Quick training initialization failed")
        return False
    
    print("🧠 Starting quick retraining...")
    if trainer.run_quick_retrain():
        print("✅ Quick retraining completed successfully!")
        print("📁 Model saved with current architecture")
        print("🚀 Ready for testing and deployment")
        return True
    else:
        print("❌ Quick retraining failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
