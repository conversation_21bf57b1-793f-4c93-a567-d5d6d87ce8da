#!/usr/bin/env python3
"""Check available margin for trading"""

import sys
sys.path.append('01_binance_connector')

from binance_real_money_connector import BinanceRealMoneyConnector

def main():
    try:
        connector = BinanceRealMoneyConnector()
        
        # Get detailed margin account info
        margin_account = connector.client.get_margin_account()
        
        print("🏦 DETAILED MARGIN ACCOUNT INFO")
        print("=" * 50)
        print(f"💰 Total Asset of BTC: {margin_account['totalAssetOfBtc']}")
        print(f"💰 Total Liability of BTC: {margin_account['totalLiabilityOfBtc']}")
        print(f"💰 Total Net Asset of BTC: {margin_account['totalNetAssetOfBtc']}")
        print(f"⚖️ Margin Level: {margin_account['marginLevel']}")
        print(f"📊 Index Price: {margin_account['indexPrice']}")
        print(f"🔄 Transfer In: {margin_account['transferIn']}")
        print(f"🔄 Transfer Out: {margin_account['transferOut']}")
        
        # Get isolated margin account info
        isolated_account = connector.client.get_isolated_margin_account()
        
        print(f"\n🏦 ISOLATED MARGIN ACCOUNT")
        print("=" * 50)
        print(f"💰 Total Asset of BTC: {isolated_account['totalAssetOfBtc']}")
        print(f"💰 Total Liability of BTC: {isolated_account['totalLiabilityOfBtc']}")
        print(f"💰 Total Net Asset of BTC: {isolated_account['totalNetAssetOfBtc']}")
        print(f"⚖️ Margin Level: {isolated_account['marginLevel']}")
        
        # Check BTCUSDT isolated margin
        for asset in isolated_account['assets']:
            if asset['symbol'] == 'BTCUSDT':
                print(f"\n📊 BTCUSDT ISOLATED MARGIN:")
                print("=" * 30)
                
                base_asset = asset['baseAsset']
                quote_asset = asset['quoteAsset']
                
                print(f"🔸 Base Asset (BTC):")
                print(f"   Free: {base_asset['free']}")
                print(f"   Locked: {base_asset['locked']}")
                print(f"   Borrowed: {base_asset['borrowed']}")
                print(f"   Interest: {base_asset['interest']}")
                print(f"   Net Asset: {base_asset['netAsset']}")
                
                print(f"🔸 Quote Asset (USDT):")
                print(f"   Free: {quote_asset['free']}")
                print(f"   Locked: {quote_asset['locked']}")
                print(f"   Borrowed: {quote_asset['borrowed']}")
                print(f"   Interest: {quote_asset['interest']}")
                print(f"   Net Asset: {quote_asset['netAsset']}")
                
                print(f"🔸 Trading Status:")
                print(f"   Enabled: {asset['enabled']}")
                print(f"   Margin Level: {asset['marginLevel']}")
                print(f"   Margin Ratio: {asset['marginRatio']}")
                print(f"   Index Price: {asset['indexPrice']}")
                print(f"   Liquidate Price: {asset['liquidatePrice']}")
                print(f"   Liquidate Rate: {asset['liquidateRate']}")
                
                # Calculate available margin
                free_usdt = float(quote_asset['free'])
                free_btc_value = float(base_asset['free']) * float(asset['indexPrice'])
                total_free_value = free_usdt + free_btc_value
                
                print(f"\n💡 AVAILABLE FOR TRADING:")
                print(f"   Free USDT: ${free_usdt:.2f}")
                print(f"   Free BTC Value: ${free_btc_value:.2f}")
                print(f"   Total Available: ${total_free_value:.2f}")
                
                # Calculate max position with 10x leverage
                max_position_10x = total_free_value * 10
                print(f"   Max Position (10x): ${max_position_10x:.2f}")
                
                break
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
