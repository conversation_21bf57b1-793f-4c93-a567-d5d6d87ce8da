#!/usr/bin/env python3
"""
🚀 REAL MONEY TRADING SYSTEM - IMMEDIATE EXECUTION
Direct real money trading with proper value levels
Compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('05_trading_engine')
sys.path.append('06_telegram_system')

from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import EnhancedGridAwareSignalGenerator
from automated_trading_engine import AutomatedTradingEngine
from telegram_trading_bot import TelegramTradingBot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('real_money_trading_execution.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealMoneyTradingSystem:
    def __init__(self):
        """Initialize the real money trading system"""
        logger.info("🚀 INITIALIZING REAL MONEY TRADING SYSTEM")
        
        # Initialize components
        self.binance = BinanceRealMoneyConnector()
        self.signal_generator = EnhancedGridAwareSignalGenerator()
        self.trading_engine = AutomatedTradingEngine(self.binance)
        self.telegram = TelegramTradingBot()
        
        # Trading parameters (conservative for real money)
        self.risk_percentage = 0.002  # 0.2% of account
        self.reward_percentage = 0.005  # 0.5% of account
        self.confidence_threshold = 0.4  # 40% minimum confidence
        
        logger.info("✅ All components initialized successfully")
    
    def get_account_status(self):
        """Get current account status"""
        try:
            balance_info = self.binance.get_isolated_margin_balance()
            return {
                'total_balance': balance_info['total_usdt_value'],
                'usdt_balance': balance_info['usdt_balance'],
                'btc_balance': balance_info.get('btc_balance', 0)
            }
        except Exception as e:
            logger.error(f"❌ Error getting account status: {e}")
            return None
    
    def calculate_position_size(self, account_balance, risk_percentage):
        """Calculate position size based on account balance and risk"""
        risk_amount = account_balance * risk_percentage
        reward_amount = account_balance * self.reward_percentage
        
        logger.info(f"💰 Position Sizing:")
        logger.info(f"   Account Balance: ${account_balance:.2f}")
        logger.info(f"   Risk Amount: ${risk_amount:.2f} ({risk_percentage:.1%})")
        logger.info(f"   Reward Amount: ${reward_amount:.2f} ({self.reward_percentage:.1%})")
        
        return risk_amount, reward_amount
    
    def execute_single_trade(self):
        """Execute a single real money trade"""
        logger.info("🎯 EXECUTING SINGLE REAL MONEY TRADE")
        
        try:
            # Get account status
            account = self.get_account_status()
            if not account:
                logger.error("❌ Cannot get account status")
                return False
            
            logger.info(f"💰 Account Status:")
            logger.info(f"   Total Balance: ${account['total_balance']:.2f}")
            logger.info(f"   Available USDT: ${account['usdt_balance']:.2f}")
            
            # Calculate position size
            risk_amount, reward_amount = self.calculate_position_size(
                account['total_balance'], 
                self.risk_percentage
            )
            
            # Get current price
            current_price = self.binance.get_current_price('BTCUSDT')
            logger.info(f"📈 Current BTC Price: ${current_price:.2f}")
            
            # Generate signal
            signal = self.signal_generator.generate_signal()
            logger.info(f"🎯 Signal Generated:")
            logger.info(f"   Action: {signal['action']}")
            logger.info(f"   Confidence: {signal['confidence']:.1%}")
            logger.info(f"   Entry Price: ${signal.get('entry_price', current_price):.2f}")
            
            # Check if signal meets criteria
            if signal['confidence'] < self.confidence_threshold:
                logger.warning(f"⚠️ Signal confidence {signal['confidence']:.1%} below threshold {self.confidence_threshold:.1%}")
                return False
            
            if signal['action'] not in ['BUY', 'SELL']:
                logger.warning(f"⚠️ Signal action '{signal['action']}' not tradeable")
                return False
            
            # Execute the trade
            logger.info("🚀 EXECUTING REAL MONEY TRADE")
            
            trade_params = {
                'symbol': 'BTCUSDT',
                'action': signal['action'],
                'risk_amount': risk_amount,
                'reward_amount': reward_amount,
                'entry_price': signal.get('entry_price', current_price),
                'confidence': signal['confidence']
            }
            
            # Execute through trading engine
            result = self.trading_engine.execute_trade(**trade_params)
            
            if result['success']:
                logger.info("✅ TRADE EXECUTED SUCCESSFULLY")
                logger.info(f"   Order ID: {result['order_id']}")
                logger.info(f"   Entry Price: ${result['entry_price']:.2f}")
                logger.info(f"   Stop Loss: ${result['stop_loss']:.2f}")
                logger.info(f"   Take Profit: ${result['take_profit']:.2f}")
                logger.info(f"   Quantity: {result['quantity']:.6f} BTC")
                
                # Send telegram notification
                self.telegram.send_trade_notification({
                    'action': signal['action'],
                    'entry_price': result['entry_price'],
                    'stop_loss': result['stop_loss'],
                    'take_profit': result['take_profit'],
                    'quantity': result['quantity'],
                    'risk_amount': risk_amount,
                    'reward_amount': reward_amount
                })
                
                return result
            else:
                logger.error(f"❌ TRADE EXECUTION FAILED: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error executing trade: {e}")
            return False
    
    def monitor_trade(self, trade_result):
        """Monitor trade until completion"""
        if not trade_result or not trade_result.get('order_id'):
            logger.error("❌ No valid trade to monitor")
            return
        
        order_id = trade_result['order_id']
        logger.info(f"👁️ MONITORING TRADE {order_id}")
        
        start_time = time.time()
        max_monitor_time = 3600  # 1 hour maximum
        
        while time.time() - start_time < max_monitor_time:
            try:
                # Check trade status
                status = self.trading_engine.get_trade_status(order_id)
                
                if status['completed']:
                    logger.info("🏁 TRADE COMPLETED")
                    logger.info(f"   Outcome: {status['outcome']}")
                    logger.info(f"   P&L: ${status['pnl']:.2f}")
                    logger.info(f"   Final Price: ${status['final_price']:.2f}")
                    
                    # Send completion notification
                    self.telegram.send_trade_completion({
                        'outcome': status['outcome'],
                        'pnl': status['pnl'],
                        'final_price': status['final_price']
                    })
                    
                    return status
                
                # Log current status
                logger.info(f"⏳ Trade in progress - Current Price: ${status.get('current_price', 0):.2f}")
                
                # Wait before next check
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Error monitoring trade: {e}")
                time.sleep(60)  # Wait longer on error
        
        logger.warning("⚠️ Trade monitoring timeout reached")
        return None

def main():
    """Main execution function"""
    logger.info("🚀 STARTING REAL MONEY TRADING SYSTEM")
    logger.info("=" * 60)
    
    try:
        # Initialize trading system
        trading_system = RealMoneyTradingSystem()
        
        # Execute single test trade
        logger.info("🎯 EXECUTING SINGLE REAL MONEY TRADE")
        trade_result = trading_system.execute_single_trade()
        
        if trade_result:
            logger.info("✅ Trade executed successfully, monitoring...")
            final_result = trading_system.monitor_trade(trade_result)
            
            if final_result:
                logger.info("🎉 TRADE CYCLE COMPLETED SUCCESSFULLY")
                logger.info(f"   Final P&L: ${final_result['pnl']:.2f}")
            else:
                logger.warning("⚠️ Trade monitoring incomplete")
        else:
            logger.error("❌ Trade execution failed")
        
        logger.info("✅ REAL MONEY TRADING SYSTEM COMPLETE")
        
    except KeyboardInterrupt:
        logger.info("🛑 Trading stopped by user")
    except Exception as e:
        logger.error(f"❌ System error: {e}")
    
    logger.info("=" * 60)
    logger.info("🏁 SYSTEM SHUTDOWN COMPLETE")

if __name__ == "__main__":
    main()
