#!/usr/bin/env python3
"""
Compliance System Module - Independent Execution
Run this module independently for testing compliance monitoring
"""

import sys
import os
import time

# Add paths
sys.path.append('../shared_config')

from guardrails_compliance_check import GuardrailsComplianceCheck
from auto_compliance_startup import AutoComplianceStartup
from shared_config.shared_utilities import setup_logging

def main():
    """Run compliance system module independently"""
    logger = setup_logging('compliance_system_standalone')
    
    print("🛡️ Compliance System Module - Independent Execution")
    print("=" * 60)
    
    try:
        # Initialize compliance system
        compliance_system = GuardrailsComplianceCheck()
        startup_system = AutoComplianceStartup()
        
        print("🔍 Running compliance startup check...")
        startup_success = startup_system.auto_startup()
        
        if startup_success:
            print("✅ Compliance startup check passed")
        else:
            print("❌ Compliance startup check failed")
        
        print("\n🔍 Running comprehensive compliance check...")
        compliance_success = compliance_system.run_comprehensive_compliance_check()
        
        if compliance_success:
            print("✅ All compliance checks passed")
        else:
            print("⚠️ Compliance violations detected")
        
        # Display compliance metrics
        metrics = compliance_system.compliance_metrics
        print(f"\n📊 Compliance Metrics:")
        print(f"   Compliance Rate: {metrics.get('compliance_percentage', 0):.1f}%")
        print(f"   Critical Violations: {metrics.get('critical_violations', 0)}")
        print(f"   High Violations: {metrics.get('high_violations', 0)}")
        print(f"   Medium Violations: {metrics.get('medium_violations', 0)}")
        
        print("\n🔄 Starting continuous monitoring...")
        print("Press Ctrl+C to stop")
        
        compliance_system.start_continuous_monitoring()
        
        try:
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            print("\n🛑 Compliance monitoring stopped")
            compliance_system.stop_monitoring()
        
        print("✅ Compliance system module ready for integration")
        
    except Exception as e:
        print(f"❌ Module execution failed: {e}")
        logger.error(f"Module execution failed: {e}")

if __name__ == "__main__":
    main()
