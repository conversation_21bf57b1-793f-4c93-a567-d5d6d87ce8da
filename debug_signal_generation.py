#!/usr/bin/env python3
"""
Debug Signal Generation - Find why model produces no signals
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_model_signals():
    """Debug why model produces no trading signals"""
    try:
        # Load data
        df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').tail(100).reset_index(drop=True)
        
        # Add ATR
        tr_list = []
        for i in range(len(df)):
            if i == 0:
                tr = df.iloc[i]['high'] - df.iloc[i]['low']
            else:
                tr1 = df.iloc[i]['high'] - df.iloc[i]['low']
                tr2 = abs(df.iloc[i]['high'] - df.iloc[i-1]['close'])
                tr3 = abs(df.iloc[i]['low'] - df.iloc[i-1]['close'])
                tr = max(tr1, tr2, tr3)
            tr_list.append(tr)
        
        df['atr'] = pd.Series(tr_list).rolling(14, min_periods=1).mean().fillna(0)
        
        # Load model (use quick_best that worked before)
        checkpoint = torch.load('quick_best_model.pth', map_location='cpu', weights_only=False)
        
        # Create simple model
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.tcn = nn.Sequential(
                    nn.Conv1d(7, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(64, 32)
                )
                self.cnn = nn.Sequential(
                    nn.Conv1d(7, 64, 5, padding=2),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(64, 32)
                )
                self.ppo = nn.Sequential(
                    nn.Linear(71, 64),
                    nn.ReLU(),
                    nn.Linear(64, 3)
                )
                self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
                self.tcn_classifier = nn.Linear(32, 3)
                self.cnn_classifier = nn.Linear(32, 3)
            
            def forward(self, x, grid_features):
                x_transposed = x.transpose(1, 2)
                tcn_features = self.tcn(x_transposed)
                cnn_features = self.cnn(x_transposed)
                ppo_state = torch.cat([tcn_features, cnn_features, grid_features], dim=1)
                
                tcn_pred = torch.softmax(self.tcn_classifier(tcn_features), dim=1)
                cnn_pred = torch.softmax(self.cnn_classifier(cnn_features), dim=1)
                ppo_pred = torch.softmax(self.ppo(ppo_state), dim=1)
                
                weights = torch.softmax(self.ensemble_weights, dim=0)
                ensemble_pred = weights[0] * tcn_pred + weights[1] * cnn_pred + weights[2] * ppo_pred
                
                return ensemble_pred, {
                    'tcn_pred': tcn_pred,
                    'cnn_pred': cnn_pred,
                    'ppo_pred': ppo_pred,
                    'weights': weights
                }
        
        model = SimpleModel()
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        logger.info("✅ Model loaded successfully")
        
        # Test signal generation
        signals_generated = []
        sequence_length = 60
        
        for i in range(sequence_length, min(sequence_length + 20, len(df))):
            try:
                # Prepare input
                sequence = df.iloc[i-sequence_length:i][
                    ['open', 'high', 'low', 'close', 'rsi', 'vwap', 'atr']
                ].values
                
                if np.any(np.isnan(sequence)) or np.any(np.isinf(sequence)):
                    continue
                
                grid_features = [
                    float(df.iloc[i]['grid_level']),
                    float(df.iloc[i]['grid_distance']),
                    1.0, 1.0, 1.0, 0.0025, 1.0
                ]
                
                X = torch.FloatTensor(sequence).unsqueeze(0)
                grid_tensor = torch.FloatTensor(grid_features).unsqueeze(0)
                
                # Get prediction
                with torch.no_grad():
                    prediction, components = model(X, grid_tensor)
                    probabilities = torch.softmax(prediction, dim=1)[0]
                    signal = torch.argmax(prediction, dim=1).item()
                    confidence = torch.max(probabilities).item()
                
                signal_record = {
                    'index': i,
                    'price': float(df.iloc[i]['close']),
                    'signal': signal,
                    'signal_name': ['BUY', 'SELL', 'HOLD'][signal],
                    'confidence': confidence,
                    'probabilities': {
                        'BUY': float(probabilities[0]),
                        'SELL': float(probabilities[1]),
                        'HOLD': float(probabilities[2])
                    },
                    'components': {
                        'tcn_weights': components['weights'][0].item(),
                        'cnn_weights': components['weights'][1].item(),
                        'ppo_weights': components['weights'][2].item()
                    }
                }
                
                signals_generated.append(signal_record)
                
                logger.info(f"Signal {len(signals_generated)}: {signal_record['signal_name']} "
                          f"(conf: {confidence:.3f}) at ${signal_record['price']:.2f}")
                
            except Exception as e:
                logger.error(f"Signal generation failed at index {i}: {e}")
                continue
        
        # Analyze signals
        if signals_generated:
            total_signals = len(signals_generated)
            buy_signals = sum(1 for s in signals_generated if s['signal'] == 0)
            sell_signals = sum(1 for s in signals_generated if s['signal'] == 1)
            hold_signals = sum(1 for s in signals_generated if s['signal'] == 2)
            
            logger.info(f"\n📊 SIGNAL ANALYSIS:")
            logger.info(f"   Total Signals: {total_signals}")
            logger.info(f"   BUY: {buy_signals} ({buy_signals/total_signals*100:.1f}%)")
            logger.info(f"   SELL: {sell_signals} ({sell_signals/total_signals*100:.1f}%)")
            logger.info(f"   HOLD: {hold_signals} ({hold_signals/total_signals*100:.1f}%)")
            
            avg_confidence = np.mean([s['confidence'] for s in signals_generated])
            logger.info(f"   Average Confidence: {avg_confidence:.3f}")
            
            # Save detailed analysis
            import json
            with open('signal_debug_results.json', 'w') as f:
                json.dump({
                    'signals_generated': signals_generated,
                    'summary': {
                        'total_signals': total_signals,
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'hold_signals': hold_signals,
                        'avg_confidence': avg_confidence
                    }
                }, f, indent=2, default=str)
            
            logger.info("💾 Detailed analysis saved to signal_debug_results.json")
            
            return True
        else:
            logger.error("❌ No signals generated - complete failure")
            return False
            
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 DEBUG SIGNAL GENERATION")
    print("="*50)
    
    success = debug_model_signals()
    
    if success:
        print("✅ Signal generation debug completed")
        print("📊 Check signal_debug_results.json for details")
    else:
        print("❌ Signal generation debug failed")

if __name__ == "__main__":
    main()
