#!/usr/bin/env python3
"""
Execute actual real money trade with proper value levels
"""

import sys
import time
import logging

# Add paths
sys.path.append('01_binance_connector')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def execute_real_trade():
    """Execute a real money trade"""
    logger.info("🚀 EXECUTING REAL MONEY TRADE")
    
    try:
        # Import Binance connector
        from binance_real_money_connector import BinanceRealMoneyConnector
        binance = BinanceRealMoneyConnector()
        
        # Get account balance
        balance_info = binance.get_isolated_margin_balance()
        total_balance = balance_info['total_usdt_value']
        usdt_balance = balance_info['usdt_balance']
        
        logger.info(f"💰 Account Balance: ${total_balance:.2f}")
        logger.info(f"💰 Available USDT: ${usdt_balance:.2f}")
        
        # Calculate trade parameters (0.2% risk)
        risk_pct = 0.002
        reward_pct = 0.005
        risk_amount = total_balance * risk_pct
        reward_amount = total_balance * reward_pct
        
        logger.info(f"📊 Trade Parameters:")
        logger.info(f"   Risk: ${risk_amount:.2f} ({risk_pct:.1%})")
        logger.info(f"   Reward: ${reward_amount:.2f} ({reward_pct:.1%})")
        
        # Get current BTC price
        current_price = binance.get_current_price('BTCUSDT')
        logger.info(f"📈 Current BTC Price: ${current_price:.2f}")
        
        # Calculate trade parameters
        entry_price = current_price
        stop_loss_pct = 0.001  # 0.1%
        take_profit_pct = 0.0025  # 0.25%
        
        # BUY trade setup
        stop_loss_price = entry_price * (1 - stop_loss_pct)
        take_profit_price = entry_price * (1 + take_profit_pct)
        
        # Calculate quantity
        price_diff = entry_price - stop_loss_price
        quantity = risk_amount / price_diff
        position_value = quantity * entry_price
        
        logger.info(f"🎯 Trade Setup:")
        logger.info(f"   Action: BUY")
        logger.info(f"   Entry: ${entry_price:.2f}")
        logger.info(f"   Stop Loss: ${stop_loss_price:.2f}")
        logger.info(f"   Take Profit: ${take_profit_price:.2f}")
        logger.info(f"   Quantity: {quantity:.6f} BTC")
        logger.info(f"   Position Value: ${position_value:.2f}")
        
        # Validate trade
        if position_value < 10:
            logger.error("❌ Position too small (min $10)")
            return False
            
        if position_value > usdt_balance:
            logger.error("❌ Insufficient balance")
            return False
        
        # Execute trade
        logger.info("🚀 PLACING MARKET BUY ORDER")
        
        # Place market buy order
        buy_order = binance.client.create_margin_order(
            symbol='BTCUSDT',
            side='BUY',
            type='MARKET',
            quantity=f"{quantity:.6f}",
            isIsolated='TRUE'
        )
        
        logger.info(f"✅ BUY ORDER EXECUTED: {buy_order['orderId']}")
        
        # Wait a moment for order to fill
        time.sleep(2)
        
        # Place OCO sell order (TP/SL)
        logger.info("🎯 PLACING OCO SELL ORDER")
        
        oco_order = binance.client.create_margin_oco_order(
            symbol='BTCUSDT',
            side='SELL',
            quantity=f"{quantity:.6f}",
            price=f"{take_profit_price:.2f}",
            stopPrice=f"{stop_loss_price:.2f}",
            stopLimitPrice=f"{stop_loss_price * 0.999:.2f}",
            isIsolated='TRUE'
        )
        
        logger.info(f"✅ OCO ORDER PLACED: {oco_order['orderListId']}")
        
        # Monitor trade
        logger.info("👁️ MONITORING TRADE...")
        
        start_time = time.time()
        max_time = 3600  # 1 hour
        
        while time.time() - start_time < max_time:
            try:
                # Check OCO status
                oco_status = binance.client.get_margin_oco_order(
                    orderListId=oco_order['orderListId'],
                    isIsolated='TRUE',
                    symbol='BTCUSDT'
                )
                
                if oco_status['listStatusType'] == 'ALL_DONE':
                    logger.info("🏁 TRADE COMPLETED!")
                    
                    # Find which order executed
                    for order in oco_status['orders']:
                        order_detail = binance.client.get_margin_order(
                            symbol='BTCUSDT',
                            orderId=order['orderId'],
                            isIsolated='TRUE'
                        )
                        
                        if order_detail['status'] == 'FILLED':
                            exit_price = float(order_detail['price'])
                            
                            if exit_price >= take_profit_price * 0.99:
                                outcome = "PROFIT"
                                pnl = reward_amount
                            else:
                                outcome = "LOSS"
                                pnl = -risk_amount
                            
                            logger.info(f"   Outcome: {outcome}")
                            logger.info(f"   Exit Price: ${exit_price:.2f}")
                            logger.info(f"   P&L: ${pnl:.2f}")
                            
                            return {
                                'success': True,
                                'outcome': outcome,
                                'pnl': pnl,
                                'entry_price': entry_price,
                                'exit_price': exit_price
                            }
                
                # Log current status
                current_price = binance.get_current_price('BTCUSDT')
                logger.info(f"⏳ Current Price: ${current_price:.2f}")
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                time.sleep(60)
        
        logger.warning("⚠️ Monitoring timeout")
        return {'success': False, 'error': 'Timeout'}
        
    except Exception as e:
        logger.error(f"❌ Trade execution failed: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🚀 STARTING REAL MONEY TRADE EXECUTION")
    print("=" * 50)
    
    result = execute_real_trade()
    
    if result and result.get('success'):
        print(f"✅ TRADE COMPLETED: {result['outcome']}")
        print(f"💰 P&L: ${result['pnl']:.2f}")
    else:
        print(f"❌ TRADE FAILED: {result.get('error', 'Unknown error')}")
    
    print("=" * 50)
    print("🏁 EXECUTION COMPLETE")
