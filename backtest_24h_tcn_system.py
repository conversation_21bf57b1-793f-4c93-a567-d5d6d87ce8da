#!/usr/bin/env python3
"""
24-Hour Backtest of TCN-CNN-PPO Trading System
Comprehensive validation before live deployment
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import json

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TCNBacktester:
    """24-hour backtesting system for TCN-CNN-PPO"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.backtest_data = None
        self.results = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0,
            'win_rate': 0,
            'avg_win': 0,
            'avg_loss': 0,
            'max_drawdown': 0,
            'trades_per_hour': 0,
            'grid_compliance': 0,
            'confidence_avg': 0
        }
        
    def initialize_system(self):
        """Initialize backtesting system"""
        try:
            logger.info("🚀 Initializing 24-hour backtesting system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ Backtesting system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Backtesting system initialization failed: {e}")
            return False
    
    def fetch_24h_data(self):
        """Fetch last 24 hours of real Bitcoin data"""
        try:
            logger.info("📊 Fetching last 24 hours of Bitcoin data...")
            
            # Get 24 hours of 5-minute data (288 data points)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            
            klines = self.binance.client.get_historical_klines(
                'BTCUSDT',
                '5m',
                start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time.strftime('%Y-%m-%d %H:%M:%S')
            )
            
            if not klines:
                logger.error("❌ No 24-hour data received")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # Convert timestamp
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Calculate technical indicators
            df['rsi'] = self.calculate_rsi(df['close'])
            df['vwap'] = self.calculate_vwap(df)
            
            # Calculate grid features
            df['grid_level'] = self.calculate_grid_levels(df['close'])
            df['at_grid_level'] = self.check_grid_compliance(df['close'])
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"✅ Fetched {len(df)} data points for 24-hour backtest")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch 24-hour data: {e}")
            return None
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_vwap(self, df):
        """Calculate VWAP indicator"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).rolling(window=20).mean()
        return vwap
    
    def calculate_grid_levels(self, prices):
        """Calculate grid levels (0.25% spacing)"""
        base_price = 100000
        grid_spacing = 0.0025
        return ((prices - base_price) / (base_price * grid_spacing)).round().astype(int)
    
    def check_grid_compliance(self, prices):
        """Check if prices are at exact grid levels"""
        base_price = 100000
        grid_spacing = 0.0025
        tolerance = 0.00001  # 0.001%
        
        grid_prices = base_price * (1 + np.round((prices - base_price) / (base_price * grid_spacing)) * grid_spacing)
        return np.abs(prices - grid_prices) / prices <= tolerance
    
    def run_24h_backtest(self):
        """Run comprehensive 24-hour backtest"""
        try:
            logger.info("🧪 Starting 24-hour TCN-CNN-PPO backtest...")
            
            # Fetch data
            df = self.fetch_24h_data()
            if df is None:
                return False
            
            # Initialize tracking variables
            trades = []
            current_position = None
            balance = 1000.0  # Starting balance
            peak_balance = balance
            
            total_signals = 0
            grid_compliant_signals = 0
            confidence_sum = 0
            
            # Send backtest start notification
            if self.telegram:
                start_message = f"""
🧪 **24-HOUR TCN-CNN-PPO BACKTEST STARTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Data Points:** {len(df)}
💰 **Starting Balance:** ${balance:.2f}
⏰ **Period:** Last 24 hours
🧠 **Model:** TCN-CNN-PPO (135 features)
📊 **Grid Spacing:** 0.25%
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Running backtest...**
"""
                self.telegram.send_message(start_message)
            
            # Iterate through data points
            for i in range(len(df)):
                try:
                    current_data = df.iloc[i]
                    current_price = current_data['close']
                    current_rsi = current_data['rsi']
                    current_vwap = current_data['vwap']
                    at_grid = current_data['at_grid_level']
                    
                    # Generate signal using TCN-CNN-PPO
                    signal_data = self.signal_generator.generate_signal()
                    total_signals += 1
                    
                    if signal_data['signal'] != 'HOLD':
                        confidence_sum += signal_data.get('confidence', 0)
                        
                        # Check grid compliance
                        if at_grid and signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL':
                            grid_compliant_signals += 1
                            
                            # Execute trade if no current position
                            if current_position is None and signal_data.get('confidence', 0) > 0.75:
                                # Enter position
                                entry_price = current_price
                                position_size = balance * 0.02  # 2% risk per trade
                                
                                # Calculate stop loss and take profit
                                if signal_data['signal'] == 'BUY':
                                    stop_loss = entry_price * 0.999  # 0.1% SL
                                    take_profit = entry_price * 1.0025  # 0.25% TP
                                else:
                                    stop_loss = entry_price * 1.001  # 0.1% SL
                                    take_profit = entry_price * 0.9975  # 0.25% TP
                                
                                current_position = {
                                    'signal': signal_data['signal'],
                                    'entry_price': entry_price,
                                    'entry_time': current_data['datetime'],
                                    'stop_loss': stop_loss,
                                    'take_profit': take_profit,
                                    'position_size': position_size,
                                    'confidence': signal_data.get('confidence', 0)
                                }
                                
                                logger.info(f"📊 {signal_data['signal']} position opened at ${entry_price:.2f}")
                    
                    # Check position exit
                    if current_position is not None:
                        exit_triggered = False
                        exit_reason = None
                        
                        if current_position['signal'] == 'BUY':
                            if current_price <= current_position['stop_loss']:
                                exit_triggered = True
                                exit_reason = 'STOP_LOSS'
                            elif current_price >= current_position['take_profit']:
                                exit_triggered = True
                                exit_reason = 'TAKE_PROFIT'
                        else:  # SELL
                            if current_price >= current_position['stop_loss']:
                                exit_triggered = True
                                exit_reason = 'STOP_LOSS'
                            elif current_price <= current_position['take_profit']:
                                exit_triggered = True
                                exit_reason = 'TAKE_PROFIT'
                        
                        if exit_triggered:
                            # Calculate P&L
                            if current_position['signal'] == 'BUY':
                                pnl = (current_price - current_position['entry_price']) / current_position['entry_price'] * current_position['position_size']
                            else:
                                pnl = (current_position['entry_price'] - current_price) / current_position['entry_price'] * current_position['position_size']
                            
                            # Update balance
                            balance += pnl
                            peak_balance = max(peak_balance, balance)
                            
                            # Record trade
                            trade_record = {
                                'signal': current_position['signal'],
                                'entry_price': current_position['entry_price'],
                                'exit_price': current_price,
                                'entry_time': current_position['entry_time'],
                                'exit_time': current_data['datetime'],
                                'pnl': pnl,
                                'exit_reason': exit_reason,
                                'confidence': current_position['confidence']
                            }
                            trades.append(trade_record)
                            
                            logger.info(f"📊 Position closed: {exit_reason}, P&L: ${pnl:+.2f}")
                            current_position = None
                
                except Exception as e:
                    logger.warning(f"⚠️ Error processing data point {i}: {e}")
                    continue
            
            # Calculate results
            self.calculate_backtest_results(trades, balance, peak_balance, total_signals, grid_compliant_signals, confidence_sum)
            
            # Send results
            self.send_backtest_results()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 24-hour backtest failed: {e}")
            return False
    
    def calculate_backtest_results(self, trades, final_balance, peak_balance, total_signals, grid_compliant, confidence_sum):
        """Calculate comprehensive backtest results"""
        try:
            self.results['total_trades'] = len(trades)
            
            if len(trades) > 0:
                winning_trades = [t for t in trades if t['pnl'] > 0]
                losing_trades = [t for t in trades if t['pnl'] <= 0]
                
                self.results['winning_trades'] = len(winning_trades)
                self.results['losing_trades'] = len(losing_trades)
                self.results['win_rate'] = len(winning_trades) / len(trades) * 100
                
                self.results['total_pnl'] = final_balance - 1000.0
                self.results['avg_win'] = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
                self.results['avg_loss'] = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0
                self.results['max_drawdown'] = (peak_balance - min(1000.0, final_balance)) / peak_balance * 100
                self.results['trades_per_hour'] = len(trades) / 24
            
            self.results['grid_compliance'] = (grid_compliant / total_signals * 100) if total_signals > 0 else 0
            self.results['confidence_avg'] = (confidence_sum / total_signals) if total_signals > 0 else 0
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate results: {e}")
    
    def send_backtest_results(self):
        """Send comprehensive backtest results"""
        try:
            if self.telegram:
                results_message = f"""
🧪 **24-HOUR BACKTEST RESULTS**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **TRADING PERFORMANCE:**
   Total Trades: {self.results['total_trades']}
   Winning Trades: {self.results['winning_trades']}
   Losing Trades: {self.results['losing_trades']}
   Win Rate: {self.results['win_rate']:.1f}%
   
💰 **FINANCIAL RESULTS:**
   Total P&L: ${self.results['total_pnl']:+.2f}
   Average Win: ${self.results['avg_win']:+.2f}
   Average Loss: ${self.results['avg_loss']:+.2f}
   Max Drawdown: {self.results['max_drawdown']:.1f}%
   
📈 **SYSTEM METRICS:**
   Trades/Hour: {self.results['trades_per_hour']:.1f}
   Grid Compliance: {self.results['grid_compliance']:.1f}%
   Avg Confidence: {self.results['confidence_avg']:.3f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **BACKTEST COMPLETED**
⏰ **Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(results_message)
            
            # Log results
            logger.info("📊 24-HOUR BACKTEST RESULTS:")
            logger.info(f"   Total Trades: {self.results['total_trades']}")
            logger.info(f"   Win Rate: {self.results['win_rate']:.1f}%")
            logger.info(f"   Total P&L: ${self.results['total_pnl']:+.2f}")
            logger.info(f"   Grid Compliance: {self.results['grid_compliance']:.1f}%")
            logger.info(f"   Avg Confidence: {self.results['confidence_avg']:.3f}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send results: {e}")
    
    def is_backtest_successful(self):
        """Determine if backtest meets deployment criteria"""
        criteria = {
            'min_trades': self.results['total_trades'] >= 2,  # At least 2 trades in 24h
            'min_win_rate': self.results['win_rate'] >= 50.0,  # At least 50% win rate
            'positive_pnl': self.results['total_pnl'] > -50.0,  # Max $50 loss acceptable
            'grid_compliance': self.results['grid_compliance'] >= 80.0,  # 80% grid compliance
            'min_confidence': self.results['confidence_avg'] >= 0.5  # Average confidence > 0.5
        }
        
        passed = all(criteria.values())
        
        logger.info("🎯 DEPLOYMENT CRITERIA CHECK:")
        for criterion, result in criteria.items():
            status = "✅" if result else "❌"
            logger.info(f"   {status} {criterion}: {result}")
        
        return passed

def main():
    """Main backtesting function"""
    print("🧪 24-HOUR TCN-CNN-PPO BACKTEST")
    print("=" * 60)
    print("📋 Comprehensive validation before live deployment")
    print("📋 Real Bitcoin data from last 24 hours")
    print("📋 Grid compliance and confidence validation")
    print("=" * 60)
    
    backtester = TCNBacktester()
    
    if not backtester.initialize_system():
        print("❌ Backtesting system initialization failed")
        return
    
    print("🧪 Running 24-hour backtest...")
    if backtester.run_24h_backtest():
        print("✅ 24-hour backtest completed successfully!")
        
        if backtester.is_backtest_successful():
            print("🚀 BACKTEST PASSED - READY FOR LIVE DEPLOYMENT")
            return True
        else:
            print("⚠️ BACKTEST FAILED - SYSTEM NEEDS IMPROVEMENT")
            return False
    else:
        print("❌ 24-hour backtest failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
