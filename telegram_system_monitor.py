#!/usr/bin/env python3
"""
TELEGRAM SYSTEM MONITORING
Real-time monitoring and control for proven 1-year performance model

FEATURES:
- Real-time trade notifications
- System health monitoring
- Performance tracking
- Emergency controls
- Live balance updates
- Signal quality monitoring
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime, timedelta

# Add module paths
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TelegramSystemMonitor:
    """Comprehensive Telegram monitoring for proven trading system"""
    
    def __init__(self):
        self.monitoring_active = False
        self.system_status = "INITIALIZING"
        
        # Performance tracking
        self.daily_stats = {
            'trades_today': 0,
            'wins_today': 0,
            'losses_today': 0,
            'pnl_today': 0.0,
            'start_balance': 0.0,
            'current_balance': 0.0,
            'last_reset': datetime.now().date()
        }
        
        # System health tracking
        self.system_health = {
            'binance_connection': 'UNKNOWN',
            'signal_generator': 'UNKNOWN',
            'trading_engine': 'UNKNOWN',
            'isolated_margin': 'UNKNOWN',
            'last_signal_time': None,
            'last_trade_time': None,
            'uptime_start': datetime.now()
        }
        
        # Proven model metrics for reference
        self.proven_metrics = {
            'proven_win_rate': 44.5,
            'proven_trades_per_day': 8.9,
            'proven_annual_return': **********.93,
            'proven_max_drawdown': 12.3,
            'risk_per_trade': 1.0,
            'stop_loss': 0.1,
            'take_profit': 0.25,
            'risk_reward_ratio': 2.5
        }
        
        logger.info("📱 TELEGRAM SYSTEM MONITOR INITIALIZED")
        logger.info("🚀 Ready to monitor proven 1-year performance model")
    
    def initialize_telegram_bot(self):
        """Initialize Telegram bot with enhanced monitoring commands"""
        try:
            from telegram_trading_bot import ComprehensiveTelegramTradingBot
            self.telegram_bot = ComprehensiveTelegramTradingBot()
            
            # Add custom monitoring commands
            self.add_monitoring_commands()
            
            logger.info("✅ Telegram bot initialized with monitoring")
            return True
            
        except Exception as e:
            logger.error(f"❌ Telegram bot initialization failed: {e}")
            return False
    
    def add_monitoring_commands(self):
        """Add custom monitoring commands to Telegram bot"""
        try:
            # Enhanced status command
            original_handle_status = self.telegram_bot.handle_status_command
            
            def enhanced_status_command():
                """Enhanced status with proven model metrics"""
                try:
                    # Get current system status
                    self.update_system_health()
                    
                    # Calculate live performance
                    live_win_rate = 0
                    if self.daily_stats['trades_today'] > 0:
                        live_win_rate = (self.daily_stats['wins_today'] / self.daily_stats['trades_today']) * 100
                    
                    # Calculate uptime
                    uptime = datetime.now() - self.system_health['uptime_start']
                    uptime_hours = uptime.total_seconds() / 3600
                    
                    message = f"""
🚀 <b>PROVEN MODEL SYSTEM STATUS</b>

📊 <b>PROVEN METRICS (Reference):</b>
• Win Rate: {self.proven_metrics['proven_win_rate']:.1f}%
• Trades/Day: {self.proven_metrics['proven_trades_per_day']:.1f}
• Annual Return: {self.proven_metrics['proven_annual_return']:,.0f}%
• Max Drawdown: {self.proven_metrics['proven_max_drawdown']:.1f}%

📈 <b>TODAY'S LIVE PERFORMANCE:</b>
• Trades: {self.daily_stats['trades_today']}
• Wins: {self.daily_stats['wins_today']} | Losses: {self.daily_stats['losses_today']}
• Live Win Rate: {live_win_rate:.1f}%
• P&L Today: ${self.daily_stats['pnl_today']:.2f}
• Current Balance: ${self.daily_stats['current_balance']:.2f}

🔧 <b>SYSTEM HEALTH:</b>
• Binance: {self.get_status_emoji(self.system_health['binance_connection'])} {self.system_health['binance_connection']}
• Signal Gen: {self.get_status_emoji(self.system_health['signal_generator'])} {self.system_health['signal_generator']}
• Trading Engine: {self.get_status_emoji(self.system_health['trading_engine'])} {self.system_health['trading_engine']}
• Isolated Margin: {self.get_status_emoji(self.system_health['isolated_margin'])} {self.system_health['isolated_margin']}
• Uptime: {uptime_hours:.1f} hours

⏰ <b>LAST ACTIVITY:</b>
• Last Signal: {self.format_time_ago(self.system_health['last_signal_time'])}
• Last Trade: {self.format_time_ago(self.system_health['last_trade_time'])}

🎯 <b>PARAMETERS:</b>
• Risk: {self.proven_metrics['risk_per_trade']:.1f}% per trade
• SL: {self.proven_metrics['stop_loss']:.1f}% | TP: {self.proven_metrics['take_profit']:.1f}%
• Risk-Reward: {self.proven_metrics['risk_reward_ratio']:.1f}:1
"""
                    
                    self.telegram_bot.send_message(message)
                    
                except Exception as e:
                    error_msg = f"❌ Enhanced status error: {str(e)}"
                    self.telegram_bot.send_message(error_msg)
            
            # Replace the original status command
            self.telegram_bot.handle_status_command = enhanced_status_command
            
            logger.info("✅ Enhanced monitoring commands added")
            
        except Exception as e:
            logger.error(f"❌ Failed to add monitoring commands: {e}")
    
    def get_status_emoji(self, status):
        """Get emoji for system status"""
        status_emojis = {
            'ACTIVE': '🟢',
            'CONNECTED': '🟢',
            'WORKING': '🟢',
            'HEALTHY': '🟢',
            'INACTIVE': '🔴',
            'DISCONNECTED': '🔴',
            'FAILED': '🔴',
            'ERROR': '🔴',
            'WARNING': '🟡',
            'UNKNOWN': '⚪'
        }
        return status_emojis.get(status, '⚪')
    
    def format_time_ago(self, timestamp):
        """Format time ago string"""
        if not timestamp:
            return "Never"
        
        time_diff = datetime.now() - timestamp
        minutes = time_diff.total_seconds() / 60
        
        if minutes < 1:
            return "Just now"
        elif minutes < 60:
            return f"{minutes:.0f}m ago"
        else:
            hours = minutes / 60
            return f"{hours:.1f}h ago"
    
    def update_system_health(self):
        """Update system health status"""
        try:
            # Check Binance connection
            try:
                sys.path.append('01_binance_connector')
                from binance_real_money_connector import BinanceRealMoneyConnector
                connector = BinanceRealMoneyConnector()
                account_info = connector.get_account_info()
                
                if account_info:
                    self.system_health['binance_connection'] = 'CONNECTED'
                    
                    # Update balance
                    margin_balance = connector.get_isolated_margin_balance()
                    if margin_balance:
                        self.daily_stats['current_balance'] = margin_balance['total_usdt_value']
                        self.system_health['isolated_margin'] = 'HEALTHY'
                    else:
                        self.system_health['isolated_margin'] = 'WARNING'
                else:
                    self.system_health['binance_connection'] = 'DISCONNECTED'
                    
            except Exception as e:
                self.system_health['binance_connection'] = 'ERROR'
                logger.warning(f"⚠️ Binance health check failed: {e}")
            
            # Check signal generator
            try:
                sys.path.append('02_signal_generator')
                from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
                generator = GridAwareSignalGenerator()
                
                if generator.load_model():
                    self.system_health['signal_generator'] = 'WORKING'
                else:
                    self.system_health['signal_generator'] = 'FAILED'
                    
            except Exception as e:
                self.system_health['signal_generator'] = 'ERROR'
                logger.warning(f"⚠️ Signal generator health check failed: {e}")
            
            # Check trading engine
            try:
                sys.path.append('05_trading_engine')
                from automated_trading_engine import AutomatedTradingEngine
                engine = AutomatedTradingEngine()
                self.system_health['trading_engine'] = 'ACTIVE'
                
            except Exception as e:
                self.system_health['trading_engine'] = 'ERROR'
                logger.warning(f"⚠️ Trading engine health check failed: {e}")
            
            logger.info("✅ System health updated")
            
        except Exception as e:
            logger.error(f"❌ System health update failed: {e}")
    
    def send_trade_notification(self, trade_data, event_type):
        """Send trade notification via Telegram"""
        try:
            if event_type == "ENTRY":
                message = f"""
⚡ <b>PROVEN MODEL TRADE ENTRY</b>

📊 <b>SIGNAL:</b> {trade_data.get('action', 'Unknown')}
🎯 <b>Confidence:</b> {trade_data.get('confidence', 0):.1%}
💰 <b>Entry Price:</b> ${trade_data.get('entry_price', 0):.2f}
📈 <b>Take Profit:</b> ${trade_data.get('take_profit', 0):.2f} (+0.25%)
📉 <b>Stop Loss:</b> ${trade_data.get('stop_loss', 0):.2f} (-0.1%)
💵 <b>Position Size:</b> ${trade_data.get('position_size', 0):.2f}
⚠️ <b>Risk Amount:</b> ${trade_data.get('risk_amount', 0):.2f}

🚀 <b>PROVEN PARAMETERS ACTIVE</b>
Risk-Reward: 2.5:1 | Win Rate Target: 44.5%
"""
            
            elif event_type == "EXIT":
                result_emoji = "🎉" if trade_data.get('result') == 'WIN' else "📉"
                message = f"""
{result_emoji} <b>TRADE COMPLETED: {trade_data.get('result', 'Unknown')}</b>

💰 <b>ENTRY:</b> ${trade_data.get('entry_price', 0):.2f}
💰 <b>EXIT:</b> ${trade_data.get('exit_price', 0):.2f}
💵 <b>P&L:</b> ${trade_data.get('pnl', 0):.2f}
⏱️ <b>Duration:</b> {trade_data.get('duration_minutes', 0):.0f} minutes

📊 <b>TODAY'S STATS:</b>
• Trades: {self.daily_stats['trades_today']}
• Wins: {self.daily_stats['wins_today']} | Losses: {self.daily_stats['losses_today']}
• P&L Today: ${self.daily_stats['pnl_today']:.2f}
• Balance: ${self.daily_stats['current_balance']:.2f}

🚀 <b>PROVEN MODEL PERFORMANCE</b>
Target: 44.5% win rate, 8.9 trades/day
"""
            
            self.telegram_bot.send_message(message)
            logger.info(f"✅ Trade {event_type.lower()} notification sent")
            
        except Exception as e:
            logger.error(f"❌ Failed to send trade notification: {e}")
    
    def update_daily_stats(self, trade_result):
        """Update daily trading statistics"""
        try:
            # Reset stats if new day
            if datetime.now().date() > self.daily_stats['last_reset']:
                self.daily_stats = {
                    'trades_today': 0,
                    'wins_today': 0,
                    'losses_today': 0,
                    'pnl_today': 0.0,
                    'start_balance': self.daily_stats['current_balance'],
                    'current_balance': self.daily_stats['current_balance'],
                    'last_reset': datetime.now().date()
                }
            
            # Update stats
            self.daily_stats['trades_today'] += 1
            if trade_result['result'] == 'WIN':
                self.daily_stats['wins_today'] += 1
            else:
                self.daily_stats['losses_today'] += 1
            
            self.daily_stats['pnl_today'] += trade_result.get('pnl', 0)
            self.system_health['last_trade_time'] = datetime.now()
            
            logger.info(f"✅ Daily stats updated: {self.daily_stats['trades_today']} trades today")
            
        except Exception as e:
            logger.error(f"❌ Failed to update daily stats: {e}")
    
    def start_monitoring(self):
        """Start comprehensive system monitoring"""
        try:
            logger.info("🔄 Starting Telegram system monitoring...")
            
            # Initialize Telegram bot
            if not self.initialize_telegram_bot():
                logger.error("❌ Failed to initialize Telegram bot")
                return False
            
            # Start Telegram bot in separate thread
            telegram_thread = threading.Thread(target=self.telegram_bot.start_bot, daemon=True)
            telegram_thread.start()
            
            # Start monitoring loop
            monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
            monitoring_thread.start()
            
            self.monitoring_active = True
            self.system_status = "MONITORING"
            
            # Send startup notification
            startup_message = f"""
🚀 <b>PROVEN MODEL MONITORING STARTED</b>

📊 <b>SYSTEM STATUS:</b>
• Telegram Bot: ✅ ACTIVE
• System Monitor: ✅ RUNNING
• Proven Model: ✅ LOADED
• Auto-Margin: ✅ ENABLED

🎯 <b>PROVEN PERFORMANCE:</b>
• Win Rate: {self.proven_metrics['proven_win_rate']:.1f}%
• Annual Return: {self.proven_metrics['proven_annual_return']:,.0f}%
• Trades/Day: {self.proven_metrics['proven_trades_per_day']:.1f}
• Max Drawdown: {self.proven_metrics['proven_max_drawdown']:.1f}%

📱 <b>MONITORING ACTIVE</b>
Use /status for real-time updates
"""
            
            self.telegram_bot.send_message(startup_message)
            
            logger.info("✅ Telegram system monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start monitoring: {e}")
            return False
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("🔄 Monitoring loop started")
        
        while self.monitoring_active:
            try:
                # Update system health every 5 minutes
                self.update_system_health()
                
                # Check for system alerts
                self.check_system_alerts()
                
                # Sleep for 5 minutes
                time.sleep(300)
                
            except Exception as e:
                logger.error(f"❌ Monitoring loop error: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def check_system_alerts(self):
        """Check for system alerts and notifications"""
        try:
            # Check for connection issues
            if self.system_health['binance_connection'] != 'CONNECTED':
                alert_message = f"""
🚨 <b>SYSTEM ALERT</b>

⚠️ <b>Binance Connection Issue</b>
Status: {self.system_health['binance_connection']}
Time: {datetime.now().strftime('%H:%M:%S')}

🔧 <b>Action Required:</b>
Check network connection and API keys
"""
                self.telegram_bot.send_message(alert_message)
                logger.warning("⚠️ Binance connection alert sent")
            
            # Check for long periods without signals
            if self.system_health['last_signal_time']:
                time_since_signal = datetime.now() - self.system_health['last_signal_time']
                if time_since_signal.total_seconds() > 7200:  # 2 hours
                    alert_message = f"""
🚨 <b>SIGNAL ALERT</b>

⚠️ <b>No Signals for 2+ Hours</b>
Last Signal: {self.format_time_ago(self.system_health['last_signal_time'])}

🔧 <b>Status:</b>
This may be normal during low volatility periods
System continues monitoring...
"""
                    self.telegram_bot.send_message(alert_message)
                    logger.info("📊 Long signal gap notification sent")
            
        except Exception as e:
            logger.error(f"❌ System alerts check failed: {e}")

def main():
    """Main monitoring execution"""
    print("📱 TELEGRAM SYSTEM MONITORING")
    print("🚀 Proven 1-year performance model monitoring")
    print("✅ Real-time notifications and control")
    print("📊 Live performance tracking")
    print("="*80)
    
    try:
        # Initialize monitor
        monitor = TelegramSystemMonitor()
        
        # Start monitoring
        if monitor.start_monitoring():
            print("\n✅ TELEGRAM MONITORING STARTED!")
            print("📱 Bot is active and monitoring system")
            print("🔄 Real-time notifications enabled")
            print("📊 Use /status for system updates")
            
            # Keep monitoring running
            try:
                while True:
                    time.sleep(60)
                    print(f"🔄 Monitoring Status: {monitor.system_status} - {datetime.now().strftime('%H:%M:%S')}")
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped by user")
                monitor.monitoring_active = False
        else:
            print("\n❌ MONITORING FAILED TO START")
            
    except Exception as e:
        print(f"\n🚨 MONITORING ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
