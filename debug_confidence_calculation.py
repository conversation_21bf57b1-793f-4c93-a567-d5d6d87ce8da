#!/usr/bin/env python3
"""
Debug Confidence Calculation
Investigate why confidence is always zero
"""

import sys
import os
import torch
import numpy as np
import logging
from datetime import datetime

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConfidenceDebugger:
    """Debug confidence calculation issues"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        
    def initialize_system(self):
        """Initialize debugging system"""
        try:
            logger.info("🔍 Initializing confidence debugging system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            
            logger.info("✅ Confidence debugging system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Debugging system initialization failed: {e}")
            return False
    
    def debug_signal_generation_step_by_step(self):
        """Debug signal generation step by step"""
        try:
            logger.info("🔍 Debugging signal generation step by step...")
            
            # Step 1: Check market data
            logger.info("📊 Step 1: Getting market data...")
            try:
                market_data = self.binance.get_market_data()
                if market_data is not None:
                    current_price = market_data['close'].iloc[-1]
                    current_rsi = market_data['rsi'].iloc[-1]
                    current_vwap = market_data['vwap'].iloc[-1]
                    
                    logger.info(f"✅ Market data retrieved:")
                    logger.info(f"   Current Price: ${current_price:.2f}")
                    logger.info(f"   Current RSI: {current_rsi:.2f}")
                    logger.info(f"   Current VWAP: ${current_vwap:.2f}")
                else:
                    logger.error("❌ Failed to get market data")
                    return False
            except Exception as e:
                logger.error(f"❌ Market data error: {e}")
                return False
            
            # Step 2: Check grid features calculation
            logger.info("🔲 Step 2: Checking grid features...")
            try:
                # Calculate grid features manually
                base_price = 100000
                grid_spacing = 0.0025
                tolerance = 0.00001
                
                # Grid level
                grid_level = int((current_price - base_price) / (base_price * grid_spacing))
                
                # Grid distance
                nearest_grid_price = base_price * (1 + grid_level * grid_spacing)
                grid_distance = abs(current_price - nearest_grid_price) / current_price
                
                # At grid level check
                at_grid_level = grid_distance <= tolerance
                
                logger.info(f"✅ Grid features calculated:")
                logger.info(f"   Grid Level: {grid_level}")
                logger.info(f"   Nearest Grid Price: ${nearest_grid_price:.2f}")
                logger.info(f"   Grid Distance: {grid_distance:.6f}")
                logger.info(f"   Tolerance: {tolerance:.6f}")
                logger.info(f"   At Grid Level: {at_grid_level}")
                
                if not at_grid_level:
                    logger.warning(f"⚠️ NOT AT GRID LEVEL - This causes confidence = 0.0")
                    logger.info(f"   Distance {grid_distance:.6f} > Tolerance {tolerance:.6f}")
                
            except Exception as e:
                logger.error(f"❌ Grid features error: {e}")
                return False
            
            # Step 3: Check model loading
            logger.info("🧠 Step 3: Checking model loading...")
            try:
                if hasattr(self.signal_generator, 'model_loaded'):
                    logger.info(f"   Model Loaded: {self.signal_generator.model_loaded}")
                else:
                    logger.warning("   Model loaded status unknown")
                
                if hasattr(self.signal_generator, 'model'):
                    logger.info("   Model object exists")
                    
                    # Test model inference
                    logger.info("🧪 Testing model inference...")
                    
                    # Create test inputs
                    test_market_data = torch.randn(1, 4, 4)  # [batch, channels, sequence]
                    test_grid_features = torch.randn(1, 7)   # [batch, grid_features]
                    
                    with torch.no_grad():
                        policy_logits, value = self.signal_generator.model(test_market_data, test_grid_features)
                        probabilities = torch.softmax(policy_logits, dim=1)
                        confidence = torch.max(probabilities).item()
                        
                        logger.info(f"✅ Model inference test:")
                        logger.info(f"   Policy logits shape: {policy_logits.shape}")
                        logger.info(f"   Probabilities: {probabilities}")
                        logger.info(f"   Max confidence: {confidence:.4f}")
                
            except Exception as e:
                logger.error(f"❌ Model inference error: {e}")
                logger.error(f"   This explains zero confidence!")
                return False
            
            # Step 4: Test actual signal generation
            logger.info("📡 Step 4: Testing actual signal generation...")
            try:
                signal_result = self.signal_generator.generate_signal()
                
                logger.info(f"✅ Signal generation result:")
                logger.info(f"   Signal: {signal_result.get('signal', 'N/A')}")
                logger.info(f"   Confidence: {signal_result.get('confidence', 'N/A')}")
                logger.info(f"   Reason: {signal_result.get('reason', 'N/A')}")
                
                if 'error' in signal_result:
                    logger.error(f"   Error: {signal_result['error']}")
                
            except Exception as e:
                logger.error(f"❌ Signal generation error: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Step-by-step debugging failed: {e}")
            return False
    
    def test_confidence_with_relaxed_grid(self):
        """Test confidence with relaxed grid requirements"""
        try:
            logger.info("🔧 Testing confidence with relaxed grid requirements...")
            
            # Temporarily modify grid tolerance for testing
            original_tolerance = 0.00001
            relaxed_tolerance = 0.01  # 1% tolerance instead of 0.001%
            
            logger.info(f"🔧 Using relaxed grid tolerance: {relaxed_tolerance:.4f} (vs {original_tolerance:.6f})")
            
            # Get current market data
            market_data = self.binance.get_market_data()
            if market_data is None:
                return False
            
            current_price = market_data['close'].iloc[-1]
            
            # Calculate with relaxed tolerance
            base_price = 100000
            grid_spacing = 0.0025
            grid_level = int((current_price - base_price) / (base_price * grid_spacing))
            nearest_grid_price = base_price * (1 + grid_level * grid_spacing)
            grid_distance = abs(current_price - nearest_grid_price) / current_price
            at_grid_level_relaxed = grid_distance <= relaxed_tolerance
            
            logger.info(f"📊 Relaxed grid check:")
            logger.info(f"   Current Price: ${current_price:.2f}")
            logger.info(f"   Nearest Grid: ${nearest_grid_price:.2f}")
            logger.info(f"   Distance: {grid_distance:.6f}")
            logger.info(f"   At Grid (relaxed): {at_grid_level_relaxed}")
            
            if at_grid_level_relaxed:
                logger.info("✅ With relaxed tolerance, we ARE at grid level")
                logger.info("💡 This suggests the grid tolerance is too strict")
            else:
                logger.warning("⚠️ Even with relaxed tolerance, not at grid level")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Relaxed grid test failed: {e}")
            return False
    
    def run_complete_confidence_debug(self):
        """Run complete confidence debugging"""
        try:
            logger.info("🔍 Starting complete confidence debugging...")
            
            print("\n" + "="*70)
            print("CONFIDENCE CALCULATION DEBUG REPORT")
            print("="*70)
            
            # Step-by-step debugging
            if not self.debug_signal_generation_step_by_step():
                print("❌ Step-by-step debugging failed")
                return False
            
            print("\n" + "-"*50)
            
            # Relaxed grid testing
            if not self.test_confidence_with_relaxed_grid():
                print("❌ Relaxed grid testing failed")
                return False
            
            print("\n" + "="*70)
            print("CONFIDENCE DEBUG COMPLETED")
            print("="*70)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Complete confidence debugging failed: {e}")
            return False

def main():
    """Main debugging function"""
    print("🔍 CONFIDENCE CALCULATION DEBUGGING")
    print("=" * 60)
    print("📋 Investigate zero confidence issue")
    print("📋 Check grid level calculations")
    print("📋 Test model inference")
    print("📋 Identify root cause")
    print("=" * 60)
    
    debugger = ConfidenceDebugger()
    
    if not debugger.initialize_system():
        print("❌ Debugging system initialization failed")
        return False
    
    print("🔍 Running confidence debugging...")
    if debugger.run_complete_confidence_debug():
        print("✅ Confidence debugging completed")
        return True
    else:
        print("❌ Confidence debugging failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
