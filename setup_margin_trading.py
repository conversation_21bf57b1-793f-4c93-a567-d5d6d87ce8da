#!/usr/bin/env python3
"""
Setup Margin Trading Account
Transfer funds from spot to isolated margin and execute leveraged trade
"""

import sys
import os
import json
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MarginTradingSetup:
    """Setup margin trading with fund transfer"""
    
    def __init__(self):
        self.binance = None
        self.telegram = None
        
    def initialize_system(self):
        """Initialize system"""
        try:
            logger.info("🚀 Initializing margin trading setup...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ Margin trading setup initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def check_account_balances(self):
        """Check spot and margin account balances"""
        try:
            logger.info("🔍 Checking account balances...")
            
            # Get spot account balance
            spot_account = self.binance.client.get_account()
            
            spot_usdt = 0
            spot_btc = 0
            
            for balance in spot_account['balances']:
                if balance['asset'] == 'USDT':
                    spot_usdt = float(balance['free'])
                elif balance['asset'] == 'BTC':
                    spot_btc = float(balance['free'])
            
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            btc_price = float(ticker['price'])
            
            spot_btc_value = spot_btc * btc_price
            total_spot_value = spot_usdt + spot_btc_value
            
            logger.info(f"💰 Spot Account: ${total_spot_value:.2f} (USDT: ${spot_usdt:.2f}, BTC: {spot_btc:.6f})")
            
            # Check isolated margin account
            try:
                isolated_account = self.binance.client.get_isolated_margin_account()
                margin_usdt = 0
                margin_btc = 0
                
                for asset in isolated_account['assets']:
                    if asset['symbol'] == 'BTCUSDT':
                        margin_usdt = float(asset['quoteAsset']['free'])
                        margin_btc = float(asset['baseAsset']['free'])
                        break
                
                margin_btc_value = margin_btc * btc_price
                total_margin_value = margin_usdt + margin_btc_value
                
                logger.info(f"💰 Isolated Margin: ${total_margin_value:.2f} (USDT: ${margin_usdt:.2f}, BTC: {margin_btc:.6f})")
                
            except Exception as e:
                logger.warning(f"⚠️ Could not check isolated margin: {e}")
                total_margin_value = 0
            
            return {
                'spot_usdt': spot_usdt,
                'spot_btc': spot_btc,
                'spot_total_value': total_spot_value,
                'margin_total_value': total_margin_value,
                'btc_price': btc_price
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to check account balances: {e}")
            return None
    
    def transfer_to_isolated_margin(self, amount_usdt):
        """Transfer USDT from spot to isolated margin"""
        try:
            logger.info(f"🔄 Transferring ${amount_usdt:.2f} USDT to isolated margin...")
            
            # Transfer USDT to isolated margin
            transfer_result = self.binance.client.transfer_spot_to_isolated_margin(
                asset='USDT',
                symbol='BTCUSDT',
                amount=str(amount_usdt)
            )
            
            if transfer_result:
                logger.info(f"✅ Successfully transferred ${amount_usdt:.2f} USDT to isolated margin")
                
                if self.telegram:
                    transfer_message = f"""
🔄 **FUNDS TRANSFERRED TO MARGIN**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Amount:** ${amount_usdt:.2f} USDT
📊 **From:** Spot Account
📊 **To:** BTCUSDT Isolated Margin
✅ **Status:** Successful
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚡ **Ready for leveraged trading!**
"""
                    self.telegram.send_message(transfer_message)
                
                return True
            else:
                logger.error("❌ Transfer failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Transfer error: {e}")
            return False
    
    def execute_leveraged_test_trade(self):
        """Execute leveraged test trade after fund transfer"""
        try:
            logger.info("⚡ Executing leveraged test trade...")
            
            # Check balances after transfer
            balances = self.check_account_balances()
            if not balances:
                return False
            
            if balances['margin_total_value'] < 50:
                logger.error("❌ Insufficient margin balance for trading")
                return False
            
            # Get market data
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False
            
            current_price = balances['btc_price']
            current_rsi = market_data['rsi'].iloc[-1]
            
            # Simple signal
            signal = 'BUY' if current_rsi < 50 else 'SELL'
            
            # Calculate position for available margin
            available_margin = balances['margin_total_value'] * 0.8  # Use 80% of margin
            leverage = 10
            max_position_value = available_margin * leverage
            
            # Calculate for 0.1% SL
            sl_percentage = 0.001
            position_size_btc = max_position_value / current_price
            
            # Round to valid step size
            position_size_btc = round(position_size_btc, 5)
            
            # Ensure minimum
            if position_size_btc < 0.00001:
                position_size_btc = 0.00001
            
            # Calculate final values
            final_position_value = position_size_btc * current_price
            final_margin_needed = final_position_value / leverage
            risk_amount = position_size_btc * current_price * sl_percentage
            reward_amount = risk_amount * 2.5
            
            # Calculate price levels
            if signal == 'BUY':
                entry_price = current_price
                stop_loss_price = current_price * (1 - sl_percentage)
                take_profit_price = current_price * (1 + (sl_percentage * 2.5))
            else:
                entry_price = current_price
                stop_loss_price = current_price * (1 + sl_percentage)
                take_profit_price = current_price * (1 - (sl_percentage * 2.5))
            
            # Send trade notification
            if self.telegram:
                trade_message = f"""
⚡ **LEVERAGED TEST TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Margin Available:** ${balances['margin_total_value']:.2f}
🎯 **Signal:** {signal}
💰 **Entry:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position:** {position_size_btc:.5f} BTC
💵 **Value:** ${final_position_value:.2f}
⚡ **Leverage:** {leverage}x
💰 **Margin Used:** ${final_margin_needed:.2f}
🔴 **Risk:** ${risk_amount:.2f}
🟢 **Reward:** ${reward_amount:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing leveraged trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute the trade
            trade_result = self.binance.execute_full_trade(
                signal,
                entry_price,
                0.9
            )
            
            if trade_result:
                logger.info("✅ Leveraged test trade executed successfully")
                
                if self.telegram:
                    success_message = f"""
✅ **LEVERAGED TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Order ID:** {trade_result.get('orderId', 'N/A')}
⚡ **Leverage:** {leverage}x isolated margin
🎯 **Risk:** ${risk_amount:.2f}
🎯 **Reward:** ${reward_amount:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👁️ **Monitoring until TP/SL hit...**
🚀 **TCN-CNN-PPO activation after completion**
"""
                    self.telegram.send_message(success_message)
                
                return True
            else:
                logger.error("❌ Leveraged test trade failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Leveraged test trade error: {e}")
            return False
    
    def setup_and_trade(self):
        """Complete setup and execute trade"""
        try:
            # Check current balances
            balances = self.check_account_balances()
            if not balances:
                return False
            
            # If margin account is empty, transfer funds
            if balances['margin_total_value'] < 50:
                logger.info("💡 Margin account needs funding...")
                
                # Transfer 80% of spot USDT to margin
                transfer_amount = balances['spot_usdt'] * 0.8
                
                if transfer_amount < 50:
                    logger.error("❌ Insufficient spot balance for margin trading")
                    return False
                
                if not self.transfer_to_isolated_margin(transfer_amount):
                    return False
                
                # Wait for transfer to complete
                time.sleep(5)
            
            # Execute leveraged test trade
            return self.execute_leveraged_test_trade()
            
        except Exception as e:
            logger.error(f"❌ Setup and trade error: {e}")
            return False

def main():
    """Main execution"""
    print("🔄 MARGIN TRADING SETUP - FUND TRANSFER & LEVERAGED TRADE")
    print("=" * 70)
    print("📋 Step 1: Transfer funds from spot to isolated margin")
    print("📋 Step 2: Execute leveraged test trade with $1 SL target")
    print("📋 Step 3: Activate TCN-CNN-PPO after successful completion")
    print("=" * 70)
    
    setup = MarginTradingSetup()
    
    if not setup.initialize_system():
        print("❌ System initialization failed")
        return
    
    print("🔄 Setting up margin trading and executing trade...")
    if setup.setup_and_trade():
        print("✅ Margin trading setup and trade execution successful!")
        print("👁️ Monitor Telegram for trade updates and TCN-CNN-PPO activation")
    else:
        print("❌ Margin trading setup failed")

if __name__ == "__main__":
    main()
