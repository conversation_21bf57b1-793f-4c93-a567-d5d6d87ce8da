#!/usr/bin/env python3
"""
FULL CYCLE REAL MONEY TEST TRADE
Execute complete test trade cycle with full monitoring until TP or SL hit

REQUIREMENTS:
- Real money execution
- Provide all order numbers for Binance app verification
- Monitor until complete cycle (TP or SL hit)
- Iterate until successful completion
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_cycle_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FullCycleTestTrade:
    """Full cycle real money test trade with complete monitoring"""
    
    def __init__(self):
        self.test_status = "INITIALIZING"
        self.attempt_number = 1
        self.max_attempts = 5
        
        # PROVEN MODEL PARAMETERS
        self.stop_loss_percent = 0.001      # 0.1% SL
        self.take_profit_percent = 0.0025   # 0.25% TP
        self.risk_reward_ratio = 2.5        # 2.5:1 RR
        
        # Conservative position sizing for guaranteed execution
        self.test_position_size = 10.0      # $10 position
        
        # Trade tracking
        self.trade_data = {}
        self.order_numbers = {}
        
        logger.info("FULL CYCLE TEST TRADE EXECUTOR INITIALIZED")
        logger.info(f"Using ${self.test_position_size:.2f} position size")
        logger.info("Will iterate until successful completion")
    
    def initialize_connections(self):
        """Initialize all connections"""
        try:
            logger.info("Initializing connections...")
            
            # Initialize Binance connector
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test connection
            account_info = self.binance.get_account_info()
            if not account_info:
                raise Exception("Binance connection failed")
            
            # Get balance
            balance_info = self.binance.get_isolated_margin_balance()
            if not balance_info:
                raise Exception("Isolated margin access failed")
            
            self.current_balance = balance_info['total_usdt_value']
            self.usdt_balance = balance_info['usdt_balance']
            
            logger.info(f"Account Balance: ${self.current_balance:.2f}")
            logger.info(f"Available USDT: ${self.usdt_balance:.2f}")
            
            # Initialize Telegram bot
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                logger.info("Telegram bot: SUCCESS")
            except Exception as e:
                logger.warning(f"Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            logger.error(f"Connection initialization failed: {e}")
            return False
    
    def calculate_trade_parameters(self):
        """Calculate precise trade parameters"""
        try:
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Get symbol requirements
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            step_size = None
            tick_size = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                elif filter_item['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_item['tickSize'])
            
            # Calculate BTC quantity
            btc_quantity = self.test_position_size / current_price
            
            # Round to step size
            import decimal
            decimal.getcontext().rounding = decimal.ROUND_DOWN
            qty_decimal = decimal.Decimal(str(btc_quantity))
            step_decimal = decimal.Decimal(str(step_size))
            btc_quantity = float(qty_decimal.quantize(step_decimal))
            
            # Recalculate actual position size
            actual_position_size = btc_quantity * current_price
            
            # Calculate SL and TP prices
            decimal.getcontext().rounding = decimal.ROUND_HALF_UP
            price_decimal = decimal.Decimal(str(current_price))
            tick_decimal = decimal.Decimal(str(tick_size))
            
            entry_price = float(price_decimal.quantize(tick_decimal))
            stop_loss_price = float(decimal.Decimal(str(entry_price * (1 - self.stop_loss_percent))).quantize(tick_decimal))
            take_profit_price = float(decimal.Decimal(str(entry_price * (1 + self.take_profit_percent))).quantize(tick_decimal))
            
            # Calculate risk and reward
            risk_amount = actual_position_size * self.stop_loss_percent
            reward_amount = actual_position_size * self.take_profit_percent
            
            # Store parameters
            self.trade_data = {
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'btc_quantity': btc_quantity,
                'position_size': actual_position_size,
                'risk_amount': risk_amount,
                'reward_amount': reward_amount,
                'step_size': step_size,
                'tick_size': tick_size
            }
            
            logger.info("TRADE PARAMETERS CALCULATED:")
            logger.info(f"  Entry Price: ${entry_price:.2f}")
            logger.info(f"  Stop Loss: ${stop_loss_price:.2f} (-0.1%)")
            logger.info(f"  Take Profit: ${take_profit_price:.2f} (+0.25%)")
            logger.info(f"  BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"  Position Size: ${actual_position_size:.2f}")
            logger.info(f"  Risk: ${risk_amount:.4f}")
            logger.info(f"  Reward: ${reward_amount:.4f}")
            logger.info(f"  RR Ratio: {reward_amount/risk_amount:.1f}:1")
            
            # Validate sufficient balance
            if actual_position_size <= self.usdt_balance:
                logger.info("✅ BALANCE CHECK: PASSED")
                return True
            else:
                logger.error(f"❌ BALANCE CHECK: FAILED - Need ${actual_position_size:.2f}, Have ${self.usdt_balance:.2f}")
                return False
            
        except Exception as e:
            logger.error(f"Parameter calculation failed: {e}")
            return False
    
    def execute_entry_order(self):
        """Execute entry order and capture order number"""
        try:
            logger.info("EXECUTING ENTRY ORDER...")
            
            # Format quantity
            quantity_str = f"{self.trade_data['btc_quantity']:.8f}".rstrip('0').rstrip('.')
            
            logger.info("📋 ENTRY ORDER DETAILS:")
            logger.info(f"  Symbol: BTCUSDT")
            logger.info(f"  Side: BUY")
            logger.info(f"  Type: MARKET")
            logger.info(f"  Quantity: {quantity_str}")
            logger.info(f"  Estimated Cost: ${self.trade_data['position_size']:.2f}")
            
            # Execute BUY market order
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=quantity_str
            )
            
            # Store order number
            self.order_numbers['entry_order_id'] = buy_order['orderId']
            
            logger.info("🎉 ENTRY ORDER EXECUTED SUCCESSFULLY!")
            logger.info(f"📋 ENTRY ORDER NUMBER: {buy_order['orderId']}")
            logger.info(f"  Status: {buy_order.get('status', 'Unknown')}")
            
            # Get execution details
            order_details = self.binance.client.get_order(
                symbol='BTCUSDT',
                orderId=buy_order['orderId']
            )
            
            actual_quantity = float(order_details['executedQty'])
            
            # Calculate average fill price
            if 'fills' in buy_order and buy_order['fills']:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_price = total_cost / actual_quantity if actual_quantity > 0 else self.trade_data['entry_price']
            else:
                actual_price = self.trade_data['entry_price']
            
            # Update trade data with actual execution
            self.trade_data.update({
                'actual_entry_price': actual_price,
                'actual_quantity': actual_quantity,
                'actual_cost': actual_quantity * actual_price,
                'entry_time': datetime.now(),
                'entry_order_status': order_details['status']
            })
            
            # Recalculate SL and TP based on actual entry
            import decimal
            decimal.getcontext().rounding = decimal.ROUND_HALF_UP
            tick_decimal = decimal.Decimal(str(self.trade_data['tick_size']))
            
            actual_stop_loss = float(decimal.Decimal(str(actual_price * (1 - self.stop_loss_percent))).quantize(tick_decimal))
            actual_take_profit = float(decimal.Decimal(str(actual_price * (1 + self.take_profit_percent))).quantize(tick_decimal))
            
            self.trade_data.update({
                'actual_stop_loss': actual_stop_loss,
                'actual_take_profit': actual_take_profit
            })
            
            logger.info("📊 ACTUAL EXECUTION DETAILS:")
            logger.info(f"  Actual Entry Price: ${actual_price:.2f}")
            logger.info(f"  Actual Quantity: {actual_quantity:.8f} BTC")
            logger.info(f"  Actual Cost: ${self.trade_data['actual_cost']:.2f}")
            logger.info(f"  Actual Stop Loss: ${actual_stop_loss:.2f}")
            logger.info(f"  Actual Take Profit: ${actual_take_profit:.2f}")
            
            # Send Telegram notification with order number
            self.send_telegram_notification("ENTRY_EXECUTED")
            
            return True
            
        except Exception as e:
            logger.error(f"Entry order execution failed: {e}")
            return False
    
    def place_exit_orders(self):
        """Place OCO exit orders and capture order numbers"""
        try:
            logger.info("PLACING OCO EXIT ORDERS...")
            
            # Format parameters
            quantity_str = f"{self.trade_data['actual_quantity']:.8f}".rstrip('0').rstrip('.')
            tp_price_str = f"{self.trade_data['actual_take_profit']:.2f}"
            sl_price_str = f"{self.trade_data['actual_stop_loss']:.2f}"
            sl_limit_price = self.trade_data['actual_stop_loss'] * 0.999
            sl_limit_str = f"{sl_limit_price:.2f}"
            
            logger.info("📋 OCO ORDER DETAILS:")
            logger.info(f"  Quantity: {quantity_str}")
            logger.info(f"  Take Profit Price: {tp_price_str}")
            logger.info(f"  Stop Loss Price: {sl_price_str}")
            logger.info(f"  Stop Limit Price: {sl_limit_str}")
            
            # Place OCO order
            oco_order = self.binance.client.create_oco_order(
                symbol='BTCUSDT',
                side='SELL',
                quantity=quantity_str,
                price=tp_price_str,
                stopPrice=sl_price_str,
                stopLimitPrice=sl_limit_str,
                stopLimitTimeInForce='GTC'
            )
            
            # Store OCO order numbers
            self.order_numbers['oco_order_id'] = oco_order['orderListId']
            
            # Extract individual order IDs
            for order in oco_order['orders']:
                if order['type'] == 'LIMIT_MAKER':
                    self.order_numbers['take_profit_order_id'] = order['orderId']
                elif order['type'] == 'STOP_LOSS_LIMIT':
                    self.order_numbers['stop_loss_order_id'] = order['orderId']
            
            logger.info("🎉 OCO ORDERS PLACED SUCCESSFULLY!")
            logger.info(f"📋 OCO ORDER NUMBER: {oco_order['orderListId']}")
            logger.info(f"📋 TAKE PROFIT ORDER: {self.order_numbers.get('take_profit_order_id', 'N/A')}")
            logger.info(f"📋 STOP LOSS ORDER: {self.order_numbers.get('stop_loss_order_id', 'N/A')}")
            
            # Send Telegram notification with all order numbers
            self.send_telegram_notification("OCO_PLACED")
            
            return True
            
        except Exception as e:
            logger.error(f"OCO order placement failed: {e}")
            return False

    def monitor_full_cycle(self, max_hours=24):
        """Monitor trade until full cycle completion"""
        try:
            logger.info("🔄 STARTING FULL CYCLE MONITORING")
            logger.info(f"Maximum monitoring time: {max_hours} hours")
            logger.info("Monitoring until TP or SL is hit...")
            logger.info("="*80)

            start_time = datetime.now()
            check_count = 0
            last_telegram_update = datetime.now()

            while (datetime.now() - start_time).total_seconds() < (max_hours * 3600):
                check_count += 1
                current_time = datetime.now().strftime('%H:%M:%S')

                logger.info(f"🔍 CHECK #{check_count} - {current_time}")

                # Get current price
                ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])

                # Calculate distances
                distance_to_tp = self.trade_data['actual_take_profit'] - current_price
                distance_to_sl = current_price - self.trade_data['actual_stop_loss']

                logger.info(f"📊 Current Price: ${current_price:.2f}")
                logger.info(f"📈 Distance to TP: ${distance_to_tp:.2f}")
                logger.info(f"📉 Distance to SL: ${distance_to_sl:.2f}")

                # Check OCO order status
                try:
                    oco_order = self.binance.client.get_oco_order(orderListId=self.order_numbers['oco_order_id'])
                    oco_status = oco_order['listStatusType']

                    logger.info(f"🎯 OCO Status: {oco_status}")

                    if oco_status == 'ALL_DONE':
                        # Trade completed!
                        for order in oco_order['orders']:
                            if order['status'] == 'FILLED':
                                exit_price = float(order['price'])
                                exit_time = datetime.now()

                                # Determine result
                                tp_price = self.trade_data['actual_take_profit']
                                sl_price = self.trade_data['actual_stop_loss']

                                if abs(exit_price - tp_price) < abs(exit_price - sl_price):
                                    result = 'WIN'
                                    result_type = 'TAKE PROFIT'
                                    pnl = self.trade_data['actual_cost'] * self.take_profit_percent
                                    logger.info("🎉 TAKE PROFIT HIT - TRADE WON!")
                                else:
                                    result = 'LOSS'
                                    result_type = 'STOP LOSS'
                                    pnl = -self.trade_data['actual_cost'] * self.stop_loss_percent
                                    logger.info("📉 STOP LOSS HIT - TRADE LOST")

                                # Update trade data
                                self.trade_data.update({
                                    'exit_time': exit_time,
                                    'exit_price': exit_price,
                                    'result': result,
                                    'result_type': result_type,
                                    'pnl': pnl,
                                    'duration_minutes': (exit_time - self.trade_data['entry_time']).total_seconds() / 60,
                                    'exit_order_id': order['orderId']
                                })

                                # Store exit order number
                                self.order_numbers['exit_order_id'] = order['orderId']

                                logger.info("📋 FINAL TRADE DETAILS:")
                                logger.info(f"  Entry Price: ${self.trade_data['actual_entry_price']:.2f}")
                                logger.info(f"  Exit Price: ${exit_price:.2f}")
                                logger.info(f"  Result: {result} ({result_type})")
                                logger.info(f"  P&L: ${pnl:.4f}")
                                logger.info(f"  Duration: {self.trade_data['duration_minutes']:.1f} minutes")
                                logger.info(f"📋 EXIT ORDER NUMBER: {order['orderId']}")

                                # Send completion notification
                                self.send_telegram_notification("CYCLE_COMPLETED")

                                # Save results
                                self.save_cycle_results()

                                logger.info("🎉 FULL CYCLE MONITORING COMPLETED SUCCESSFULLY!")
                                self.test_status = "COMPLETED"
                                return True

                except Exception as e:
                    logger.warning(f"OCO status check failed: {e}")

                    # Alternative check: look for open orders
                    try:
                        open_orders = self.binance.client.get_open_orders(symbol='BTCUSDT')
                        if len(open_orders) == 0:
                            logger.info("⚠️ No open orders - trade may be completed")
                            # Check recent trades for our exit
                            recent_trades = self.binance.client.get_my_trades(symbol='BTCUSDT', limit=5)
                            for trade in recent_trades:
                                if (float(trade['qty']) == self.trade_data['actual_quantity'] and
                                    trade['side'] == 'SELL'):
                                    exit_price = float(trade['price'])

                                    # Determine result
                                    if abs(exit_price - self.trade_data['actual_take_profit']) < abs(exit_price - self.trade_data['actual_stop_loss']):
                                        result = 'WIN'
                                        result_type = 'TAKE PROFIT'
                                        pnl = self.trade_data['actual_cost'] * self.take_profit_percent
                                    else:
                                        result = 'LOSS'
                                        result_type = 'STOP LOSS'
                                        pnl = -self.trade_data['actual_cost'] * self.stop_loss_percent

                                    self.trade_data.update({
                                        'exit_time': datetime.now(),
                                        'exit_price': exit_price,
                                        'result': result,
                                        'result_type': result_type,
                                        'pnl': pnl,
                                        'duration_minutes': (datetime.now() - self.trade_data['entry_time']).total_seconds() / 60
                                    })

                                    logger.info(f"🎉 TRADE COMPLETED - {result_type} HIT!")
                                    self.send_telegram_notification("CYCLE_COMPLETED")
                                    self.save_cycle_results()
                                    self.test_status = "COMPLETED"
                                    return True
                    except Exception as e2:
                        logger.warning(f"Alternative check failed: {e2}")

                # Send periodic updates (every 30 minutes)
                if (datetime.now() - last_telegram_update).total_seconds() > 1800:
                    self.send_telegram_notification("MONITORING_UPDATE")
                    last_telegram_update = datetime.now()

                # Wait before next check
                time.sleep(60)  # Check every minute

                # Log progress every 10 checks
                if check_count % 10 == 0:
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    logger.info(f"⏱️ Monitoring progress: {elapsed_minutes:.0f} minutes elapsed")

            logger.warning(f"⏰ Monitoring timeout after {max_hours} hours")
            return False

        except Exception as e:
            logger.error(f"Full cycle monitoring failed: {e}")
            return False

    def send_telegram_notification(self, event_type):
        """Send comprehensive Telegram notifications"""
        try:
            if not self.telegram_bot:
                return

            if event_type == "ENTRY_EXECUTED":
                message = f"""
🚀 FULL CYCLE TEST TRADE - ENTRY EXECUTED

📋 ORDER NUMBERS FOR BINANCE APP:
Entry Order ID: {self.order_numbers['entry_order_id']}

📊 Entry Details:
- Price: ${self.trade_data['actual_entry_price']:.2f}
- Quantity: {self.trade_data['actual_quantity']:.8f} BTC
- Cost: ${self.trade_data['actual_cost']:.2f}
- Time: {self.trade_data['entry_time'].strftime('%H:%M:%S')}

🎯 Target Levels:
- Take Profit: ${self.trade_data['actual_take_profit']:.2f} (+0.25%)
- Stop Loss: ${self.trade_data['actual_stop_loss']:.2f} (-0.1%)

Placing OCO orders next...
"""

            elif event_type == "OCO_PLACED":
                message = f"""
🎯 OCO ORDERS PLACED SUCCESSFULLY

📋 ALL ORDER NUMBERS FOR BINANCE APP:
Entry Order: {self.order_numbers['entry_order_id']}
OCO Order: {self.order_numbers['oco_order_id']}
Take Profit: {self.order_numbers.get('take_profit_order_id', 'N/A')}
Stop Loss: {self.order_numbers.get('stop_loss_order_id', 'N/A')}

🔄 FULL CYCLE MONITORING ACTIVE
- Entry: ${self.trade_data['actual_entry_price']:.2f}
- TP Target: ${self.trade_data['actual_take_profit']:.2f}
- SL Target: ${self.trade_data['actual_stop_loss']:.2f}

Monitoring until TP or SL hit...
Real money test trade active! 💰
"""

            elif event_type == "MONITORING_UPDATE":
                ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
                distance_to_tp = self.trade_data['actual_take_profit'] - current_price
                distance_to_sl = current_price - self.trade_data['actual_stop_loss']

                message = f"""
📊 MONITORING UPDATE

Current Status: ACTIVE
- Current Price: ${current_price:.2f}
- Entry Price: ${self.trade_data['actual_entry_price']:.2f}
- Distance to TP: ${distance_to_tp:.2f}
- Distance to SL: ${distance_to_sl:.2f}

📋 Order Numbers:
Entry: {self.order_numbers['entry_order_id']}
OCO: {self.order_numbers['oco_order_id']}

Continuing to monitor...
"""

            elif event_type == "CYCLE_COMPLETED":
                result_emoji = "🎉" if self.trade_data['result'] == 'WIN' else "📉"
                message = f"""
{result_emoji} FULL CYCLE COMPLETED!

📋 FINAL ORDER NUMBERS:
Entry: {self.order_numbers['entry_order_id']}
OCO: {self.order_numbers['oco_order_id']}
Exit: {self.order_numbers.get('exit_order_id', 'N/A')}

📊 Final Results:
- Result: {self.trade_data['result']} ({self.trade_data['result_type']})
- Entry: ${self.trade_data['actual_entry_price']:.2f}
- Exit: ${self.trade_data['exit_price']:.2f}
- P&L: ${self.trade_data['pnl']:.4f}
- Duration: {self.trade_data['duration_minutes']:.1f} minutes

🎯 SYSTEM VALIDATION COMPLETE!
✅ Real money execution: CONFIRMED
✅ Full cycle completion: VERIFIED
✅ TP/SL functionality: VALIDATED

🚀 READY FOR LIVE DEPLOYMENT!
"""

            self.telegram_bot.send_message(message)
            logger.info(f"📱 Telegram notification sent: {event_type}")

        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")

    def save_cycle_results(self):
        """Save complete cycle results"""
        try:
            results = {
                'test_type': 'Full Cycle Real Money Test Trade',
                'test_status': self.test_status,
                'attempt_number': self.attempt_number,
                'timestamp': datetime.now().isoformat(),
                'order_numbers': self.order_numbers,
                'trade_data': self.trade_data,
                'system_validation': {
                    'real_money_execution': 'CONFIRMED',
                    'full_cycle_completion': 'VERIFIED',
                    'tp_sl_functionality': 'VALIDATED',
                    'order_management': 'OPERATIONAL',
                    'monitoring_system': 'COMPLETE'
                },
                'ready_for_live_trading': True
            }

            # Save with timestamp
            filename = f'full_cycle_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"📄 Cycle results saved to {filename}")

        except Exception as e:
            logger.error(f"Failed to save cycle results: {e}")

    def run_full_cycle_attempt(self):
        """Run single full cycle attempt"""
        try:
            logger.info(f"🚀 STARTING FULL CYCLE ATTEMPT #{self.attempt_number}")
            logger.info("="*80)

            # Step 1: Initialize connections
            if not self.initialize_connections():
                logger.error("❌ Connection initialization failed")
                return False

            # Step 2: Calculate trade parameters
            if not self.calculate_trade_parameters():
                logger.error("❌ Trade parameter calculation failed")
                return False

            # Step 3: Execute entry order
            if not self.execute_entry_order():
                logger.error("❌ Entry order execution failed")
                return False

            # Step 4: Place exit orders
            if not self.place_exit_orders():
                logger.error("❌ Exit order placement failed")
                return False

            # Step 5: Monitor until completion
            if not self.monitor_full_cycle():
                logger.error("❌ Full cycle monitoring failed or timeout")
                return False

            # Success!
            logger.info("🎉 FULL CYCLE ATTEMPT COMPLETED SUCCESSFULLY!")
            return True

        except Exception as e:
            logger.error(f"Full cycle attempt failed: {e}")
            return False

    def iterate_until_success(self):
        """Iterate attempts until successful completion"""
        try:
            logger.info("🔄 STARTING FULL CYCLE ITERATION")
            logger.info(f"Maximum attempts: {self.max_attempts}")
            logger.info("Will continue until successful TP or SL hit")
            logger.info("="*80)

            while self.attempt_number <= self.max_attempts:
                logger.info(f"🎯 ATTEMPT #{self.attempt_number}/{self.max_attempts}")

                if self.run_full_cycle_attempt():
                    logger.info("🎉 FULL CYCLE COMPLETED SUCCESSFULLY!")
                    return True
                else:
                    logger.warning(f"❌ Attempt #{self.attempt_number} failed")
                    self.attempt_number += 1

                    if self.attempt_number <= self.max_attempts:
                        wait_time = 60 * self.attempt_number  # Increasing wait time
                        logger.info(f"⏱️ Waiting {wait_time} seconds before next attempt...")
                        time.sleep(wait_time)

            logger.error("❌ All attempts exhausted - could not complete full cycle")
            return False

        except Exception as e:
            logger.error(f"Iteration failed: {e}")
            return False

def main():
    """Main execution"""
    print("🚀 FULL CYCLE REAL MONEY TEST TRADE")
    print("Complete execution with order numbers and full monitoring")
    print("Using proven 1-year performance model parameters")
    print("SL: 0.1%, TP: 0.25%, RR: 2.5:1, Position: $10")
    print("="*80)
    print("⚠️  WARNING: This executes REAL trades with REAL money!")
    print("📋 All order numbers will be provided for Binance app verification")
    print("🔄 Will iterate until successful TP or SL hit")
    print("="*80)

    try:
        # Initialize full cycle executor
        executor = FullCycleTestTrade()

        # Run iteration until success
        if executor.iterate_until_success():
            print("\n🎉 SUCCESS: FULL CYCLE TEST TRADE COMPLETED!")
            print("System validation complete - ready for live deployment")

            # Display final results
            if executor.trade_data:
                data = executor.trade_data
                orders = executor.order_numbers

                print(f"\n📋 ALL ORDER NUMBERS:")
                print(f"  Entry Order: {orders.get('entry_order_id', 'N/A')}")
                print(f"  OCO Order: {orders.get('oco_order_id', 'N/A')}")
                print(f"  Take Profit: {orders.get('take_profit_order_id', 'N/A')}")
                print(f"  Stop Loss: {orders.get('stop_loss_order_id', 'N/A')}")
                print(f"  Exit Order: {orders.get('exit_order_id', 'N/A')}")

                print(f"\n📊 FINAL RESULTS:")
                print(f"  Result: {data.get('result', 'Unknown')} ({data.get('result_type', 'Unknown')})")
                print(f"  Entry: ${data.get('actual_entry_price', 0):.2f}")
                print(f"  Exit: ${data.get('exit_price', 0):.2f}")
                print(f"  P&L: ${data.get('pnl', 0):.4f}")
                print(f"  Duration: {data.get('duration_minutes', 0):.1f} minutes")
                print(f"  Attempt: #{executor.attempt_number}")

                if data.get('result') == 'WIN':
                    print("🎉 Test trade WON - Take Profit hit!")
                else:
                    print("📉 Test trade LOST - Stop Loss hit (normal)")

                print("\n✅ SYSTEM VALIDATION COMPLETE:")
                print("✅ Real money execution: CONFIRMED")
                print("✅ Full cycle completion: VERIFIED")
                print("✅ TP/SL functionality: VALIDATED")
                print("✅ Order management: OPERATIONAL")
                print("✅ Monitoring system: COMPLETE")

                print("\n🚀 SYSTEM IS READY FOR LIVE TRADING DEPLOYMENT!")

        else:
            print("\n❌ FAILED: Could not complete full cycle test trade")
            print("Check full_cycle_test.log for details")
            print("System is NOT ready for live deployment")

    except Exception as e:
        print(f"\nCRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
