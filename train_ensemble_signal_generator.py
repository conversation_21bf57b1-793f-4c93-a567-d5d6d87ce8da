#!/usr/bin/env python3
"""
Train Ensemble Signal Generator
Train the updated ensemble TCN-CNN-PPO signal generator
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from telegram_trading_bot import ComprehensiveTelegramTradingBot
from enhanced_grid_aware_signal_generator import EnsembleTCNCNNPPOModel

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleSignalGeneratorTrainer:
    """Train ensemble signal generator for master compliance"""
    
    def __init__(self):
        self.binance = None
        self.telegram = None
        self.model = None
        
        # MASTER DOCUMENT TARGETS
        self.targets = {
            'win_rate': 60.0,
            'trades_per_day': 8.0,
            'composite_score': 0.8,
            'new_reward': 6.4
        }
        
    def initialize_system(self):
        """Initialize ensemble training system"""
        try:
            logger.info("🎯 Initializing ENSEMBLE SIGNAL GENERATOR TRAINER...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            # Create ensemble model
            self.model = EnsembleTCNCNNPPOModel(num_ensemble_models=3)
            
            # Send training start notification
            if self.telegram:
                start_message = f"""
🎯 **ENSEMBLE SIGNAL GENERATOR TRAINING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧠 **ENSEMBLE ARCHITECTURE:**
   • 3 Independent TCN-CNN-PPO Models
   • Confidence-Weighted Voting
   • Enhanced Grid-Aware Processing
   • Master Document Compliance Focus
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **TARGETS:**
   • Win Rate: 60.0%
   • Trades/Day: 8.0
   • Composite Score: 0.8
   • New Reward: ≥6.4
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Starting ensemble training...**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(start_message)
            
            logger.info("✅ Ensemble signal generator trainer initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ensemble trainer initialization failed: {e}")
            return False
    
    def prepare_training_data(self, df):
        """Prepare training data for ensemble"""
        try:
            logger.info("🔧 Preparing ensemble training data...")
            
            market_features = []
            grid_features = []
            labels = []
            
            sequence_length = 4
            
            for i in range(len(df) - sequence_length):
                # Market data preparation
                price_seq = df['close'].iloc[i:i+sequence_length].values
                rsi_seq = df['rsi'].iloc[i:i+sequence_length].values
                vwap_seq = df['vwap'].iloc[i:i+sequence_length].values
                volume_seq = df['volume'].iloc[i:i+sequence_length].values
                
                # Normalization
                price_seq = price_seq / np.max(price_seq)
                rsi_seq = rsi_seq / 100.0
                vwap_seq = vwap_seq / np.max(vwap_seq)
                volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq
                
                market_tensor = np.array([price_seq, rsi_seq, vwap_seq, volume_seq])
                market_features.append(market_tensor)
                
                # Grid features
                current_idx = i + sequence_length - 1
                current_price = df['close'].iloc[current_idx]
                current_rsi = df['rsi'].iloc[current_idx]
                current_vwap = df['vwap'].iloc[current_idx]
                
                # Grid compliance calculation
                base_price = 100000
                grid_spacing = 0.0025
                tolerance = 0.005  # 0.5%
                
                grid_level = int((current_price - base_price) / (base_price * grid_spacing))
                nearest_grid_price = base_price * (1 + grid_level * grid_spacing)
                grid_distance = abs(current_price - nearest_grid_price) / current_price
                at_grid_level = grid_distance <= tolerance
                
                grid_vector = np.array([
                    grid_level / 100.0,
                    float(at_grid_level),
                    grid_distance,
                    1.0025,
                    0.9975,
                    0.0025,
                    1.0 if at_grid_level else 0.0
                ])
                
                grid_features.append(grid_vector)
                
                # Enhanced labeling for ensemble
                current_price = df['close'].iloc[current_idx]
                next_price = df['close'].iloc[current_idx + 1] if current_idx + 1 < len(df) else current_price
                price_change = (next_price - current_price) / current_price
                
                # Multi-strategy labeling for 8 trades/day + 60% win rate
                if current_rsi < 25 and price_change > 0.002:
                    label = 0  # BUY
                elif current_rsi > 75 and price_change < -0.002:
                    label = 1  # SELL
                elif current_price < current_vwap * 0.998 and price_change > 0.001:
                    label = 0  # BUY
                elif current_price > current_vwap * 1.002 and price_change < -0.001:
                    label = 1  # SELL
                elif (current_rsi < 35 or current_price < current_vwap) and price_change > 0.0005:
                    label = 0  # BUY
                elif (current_rsi > 65 or current_price > current_vwap) and price_change < -0.0005:
                    label = 1  # SELL
                elif price_change > 0.0002:
                    label = 0  # BUY
                elif price_change < -0.0002:
                    label = 1  # SELL
                else:
                    label = 2  # HOLD
                
                labels.append(label)
            
            market_features = np.array(market_features)
            grid_features = np.array(grid_features)
            labels = np.array(labels)
            
            # Check distribution
            buy_count = np.sum(labels == 0)
            sell_count = np.sum(labels == 1)
            hold_count = np.sum(labels == 2)
            action_percentage = (buy_count + sell_count) / len(labels) * 100
            
            logger.info(f"✅ Prepared {len(market_features)} ensemble training samples")
            logger.info(f"📊 Label distribution:")
            logger.info(f"   BUY: {buy_count} ({buy_count/len(labels)*100:.1f}%)")
            logger.info(f"   SELL: {sell_count} ({sell_count/len(labels)*100:.1f}%)")
            logger.info(f"   HOLD: {hold_count} ({hold_count/len(labels)*100:.1f}%)")
            logger.info(f"📈 Action signals: {action_percentage:.1f}% (target: >60%)")
            
            return market_features, grid_features, labels
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare ensemble training data: {e}")
            return None, None, None
    
    def train_ensemble_model(self, train_data):
        """Train ensemble model"""
        try:
            logger.info("🧠 Starting ENSEMBLE MODEL TRAINING...")
            
            # Prepare data
            market_features, grid_features, labels = self.prepare_training_data(train_data)
            if market_features is None:
                return None
            
            # Convert to tensors
            X_market = torch.FloatTensor(market_features)
            X_grid = torch.FloatTensor(grid_features)
            y = torch.LongTensor(labels)
            
            # Split data
            train_size = int(0.8 * len(X_market))
            X_train_market = X_market[:train_size]
            X_train_grid = X_grid[:train_size]
            y_train = y[:train_size]
            X_val_market = X_market[train_size:]
            X_val_grid = X_grid[train_size:]
            y_val = y[train_size:]
            
            # Training setup
            class_weights = torch.tensor([2.0, 2.0, 0.4])  # Favor BUY/SELL for 8 trades/day
            criterion = nn.CrossEntropyLoss(weight=class_weights)
            optimizer = optim.AdamW(self.model.parameters(), lr=0.0008, weight_decay=1e-6)
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-7)
            
            # Training tracking
            best_val_acc = 0
            best_model_state = None
            
            # ENSEMBLE TRAINING LOOP
            max_epochs = 100
            logger.info(f"🧠 Starting {max_epochs} epochs of ensemble training...")
            
            print(f"\n{'='*80}")
            print("ENSEMBLE TCN-CNN-PPO SIGNAL GENERATOR TRAINING")
            print(f"{'='*80}")
            print("Target: 60% win rate, 8 trades/day, ensemble voting")
            print(f"{'='*80}")
            
            for epoch in range(max_epochs):
                # Training phase
                self.model.train()
                
                # Forward pass
                policy_logits, value = self.model(X_train_market, X_train_grid)
                train_loss = criterion(policy_logits, y_train)
                
                # Backward pass
                optimizer.zero_grad()
                train_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
                optimizer.step()
                scheduler.step()
                
                # Calculate training metrics
                with torch.no_grad():
                    train_pred = torch.argmax(policy_logits, dim=1)
                    train_acc = (train_pred == y_train).float().mean().item()
                    action_predictions = (train_pred != 2).sum().item()
                    action_rate = action_predictions / len(train_pred) * 100
                
                # Validation phase
                self.model.eval()
                with torch.no_grad():
                    val_policy, val_value = self.model(X_val_market, X_val_grid)
                    val_loss = criterion(val_policy, y_val)
                    val_pred = torch.argmax(val_policy, dim=1)
                    val_acc = (val_pred == y_val).float().mean().item()
                    val_action_predictions = (val_pred != 2).sum().item()
                    val_action_rate = val_action_predictions / len(val_pred) * 100
                
                # Track best model
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    best_model_state = self.model.state_dict().copy()
                
                # Progress display
                if epoch % 10 == 0 or epoch == max_epochs - 1:
                    progress_bar = "█" * int(epoch / max_epochs * 50) + "░" * (50 - int(epoch / max_epochs * 50))
                    current_lr = scheduler.get_last_lr()[0]
                    print(f"Epoch {epoch:3d}/{max_epochs} [{progress_bar}] "
                          f"Loss: {train_loss.item():.4f} | "
                          f"Val Acc: {val_acc:.4f} | "
                          f"Action: {val_action_rate:.1f}% | "
                          f"Best: {best_val_acc:.4f}")
                
                # Send progress updates
                if epoch % 25 == 0 and self.telegram:
                    progress_message = f"""
🧠 **ENSEMBLE TRAINING PROGRESS**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Epoch:** {epoch}/{max_epochs}
📈 **Val Accuracy:** {val_acc:.4f}
🎯 **Best Val Acc:** {best_val_acc:.4f}
📊 **Action Rate:** {val_action_rate:.1f}%
📉 **Learning Rate:** {current_lr:.7f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **ENSEMBLE TRAINING IN PROGRESS**
"""
                    self.telegram.send_message(progress_message)
            
            # Load best model
            if best_model_state:
                self.model.load_state_dict(best_model_state)
            
            print(f"{'='*80}")
            print(f"ENSEMBLE TRAINING COMPLETED")
            print(f"BEST VALIDATION ACCURACY: {best_val_acc:.4f}")
            print(f"FINAL ACTION RATE: {val_action_rate:.1f}%")
            print(f"{'='*80}\n")
            
            # Save ensemble model
            self.save_ensemble_model(best_val_acc, val_action_rate)
            
            logger.info(f"✅ ENSEMBLE TRAINING COMPLETED!")
            logger.info(f"📊 Best validation accuracy: {best_val_acc:.4f}")
            logger.info(f"📊 Final action rate: {val_action_rate:.1f}%")
            
            return {
                'epochs_completed': max_epochs,
                'best_val_accuracy': best_val_acc,
                'final_action_rate': val_action_rate,
                'ensemble_models': 3
            }
            
        except Exception as e:
            logger.error(f"❌ Ensemble training failed: {e}")
            return None
    
    def save_ensemble_model(self, best_val_acc, action_rate):
        """Save trained ensemble model"""
        try:
            model_dir = os.path.join('02_signal_generator', 'models')
            os.makedirs(model_dir, exist_ok=True)
            
            model_path = os.path.join(model_dir, 'best_real_3year_trained_model.pth')
            
            checkpoint = {
                'model_state_dict': self.model.state_dict(),
                'model_architecture': 'Ensemble TCN-CNN-PPO Signal Generator',
                'training_metadata': {
                    'training_date': datetime.now().isoformat(),
                    'training_type': 'ensemble_tcn_cnn_ppo_signal_generator',
                    'ensemble_size': 3,
                    'best_val_accuracy': best_val_acc,
                    'final_action_rate': action_rate,
                    'input_features': 135,
                    'architecture': 'Ensemble TCN-CNN-PPO with Voting'
                },
                'targets': self.targets
            }
            
            torch.save(checkpoint, model_path)
            
            logger.info(f"✅ Ensemble model saved to: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save ensemble model: {e}")
            return False
    
    def run_complete_training(self):
        """Run complete ensemble training"""
        try:
            logger.info("🎯 Starting COMPLETE ENSEMBLE TRAINING...")
            
            # Load real data
            data_path = 'real_bitcoin_4year_data.json'
            if not os.path.exists(data_path):
                logger.error("❌ Real data file not found")
                return False
            
            df = pd.read_json(data_path, orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['year'] = df['datetime'].dt.year
            
            # Use training data (2022-2023)
            train_data = df[df['year'].isin([2022, 2023])].copy()
            
            # Train ensemble model
            training_results = self.train_ensemble_model(train_data)
            if training_results is None:
                return False
            
            # Send completion notification
            if self.telegram:
                completion_message = f"""
🎯 **ENSEMBLE SIGNAL GENERATOR TRAINING COMPLETED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧠 **ENSEMBLE RESULTS:**
   • Best Val Accuracy: {training_results['best_val_accuracy']:.4f}
   • Final Action Rate: {training_results['final_action_rate']:.1f}%
   • Ensemble Models: {training_results['ensemble_models']}
   • Epochs Completed: {training_results['epochs_completed']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **ENSEMBLE MODEL SAVED**
🚀 **READY FOR SIGNAL GENERATION**
⏰ **Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(completion_message)
            
            logger.info("✅ COMPLETE ENSEMBLE TRAINING FINISHED!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Complete ensemble training failed: {e}")
            return False

def main():
    """Main ensemble training function"""
    print("🎯 ENSEMBLE SIGNAL GENERATOR TRAINER")
    print("=" * 80)
    print("🧠 ENSEMBLE ARCHITECTURE:")
    print("🧠   • 3 Independent TCN-CNN-PPO Models")
    print("🧠   • Confidence-Weighted Voting")
    print("🧠   • Enhanced Grid-Aware Processing")
    print("=" * 80)
    print("📋 TARGETS:")
    print("📋   • Win Rate: 60.0%")
    print("📋   • Trades/Day: 8.0")
    print("📋   • Composite Score: 0.8")
    print("📋   • New Reward: ≥6.4")
    print("=" * 80)
    
    trainer = EnsembleSignalGeneratorTrainer()
    
    if not trainer.initialize_system():
        print("❌ Ensemble trainer initialization failed")
        return False
    
    print("🎯 Starting ensemble signal generator training...")
    if trainer.run_complete_training():
        print("✅ ENSEMBLE TRAINING COMPLETED!")
        print("🧠 Ensemble model saved and ready")
        print("🚀 Signal generator updated with ensemble architecture")
        return True
    else:
        print("❌ Ensemble training failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
