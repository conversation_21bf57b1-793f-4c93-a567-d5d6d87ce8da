#!/usr/bin/env python3
"""
Shared Utilities
Common utility functions used across all modules
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

def setup_logging(module_name, log_level="INFO"):
    """Setup standardized logging for all modules"""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=f'%(asctime)s - {module_name} - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'08_logging_system/logs/{module_name}.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(module_name)

def load_config(config_path):
    """Load configuration from JSON file"""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        raise Exception(f"Failed to load config from {config_path}: {e}")

def save_config(config_data, config_path):
    """Save configuration to JSON file"""
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        return True
    except Exception as e:
        raise Exception(f"Failed to save config to {config_path}: {e}")

def get_project_root():
    """Get the project root directory"""
    return Path(__file__).parent.parent

def ensure_directory(directory_path):
    """Ensure directory exists, create if not"""
    os.makedirs(directory_path, exist_ok=True)
    return directory_path

def get_timestamp():
    """Get current timestamp in ISO format"""
    return datetime.now().isoformat()

def validate_grid_level(price, grid_level, tolerance=0.00001):
    """Validate if price is at grid level within tolerance"""
    distance = abs(price - grid_level) / price
    return distance <= tolerance

def calculate_grid_level(price, grid_spacing=0.0025):
    """Calculate exact grid level for given price"""
    return round(price / (price * grid_spacing)) * (price * grid_spacing)

def format_currency(amount, decimals=2):
    """Format amount as currency"""
    return f"${amount:.{decimals}f}"

def format_percentage(value, decimals=1):
    """Format value as percentage"""
    return f"{value * 100:.{decimals}f}%"

def safe_divide(numerator, denominator, default=0):
    """Safe division with default value"""
    try:
        return numerator / denominator if denominator != 0 else default
    except:
        return default

def clamp(value, min_value, max_value):
    """Clamp value between min and max"""
    return max(min_value, min(value, max_value))

def is_market_hours():
    """Check if within market hours (crypto trades 24/7)"""
    return True  # Crypto markets are always open

def get_module_status(module_name):
    """Get standardized module status"""
    return {
        'module': module_name,
        'status': 'OPERATIONAL',
        'timestamp': get_timestamp(),
        'version': '1.0.0'
    }

def create_error_response(error_message, error_code=None):
    """Create standardized error response"""
    return {
        'success': False,
        'error': error_message,
        'error_code': error_code,
        'timestamp': get_timestamp()
    }

def create_success_response(data=None, message=None):
    """Create standardized success response"""
    response = {
        'success': True,
        'timestamp': get_timestamp()
    }
    if data is not None:
        response['data'] = data
    if message is not None:
        response['message'] = message
    return response

def validate_required_fields(data, required_fields):
    """Validate that all required fields are present"""
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise ValueError(f"Missing required fields: {missing_fields}")
    return True

def sanitize_filename(filename):
    """Sanitize filename for safe file operations"""
    import re
    return re.sub(r'[<>:"/\\|?*]', '_', filename)

def get_file_hash(filepath):
    """Get SHA256 hash of file"""
    import hashlib
    try:
        with open(filepath, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    except:
        return None

def is_file_modified(filepath, expected_hash):
    """Check if file has been modified"""
    current_hash = get_file_hash(filepath)
    return current_hash != expected_hash if current_hash else True

class ModuleInterface:
    """Base interface for all system modules"""
    
    def __init__(self, module_name):
        self.module_name = module_name
        self.logger = setup_logging(module_name)
        self.status = 'INITIALIZING'
        self.start_time = datetime.now()
    
    def get_status(self):
        """Get module status"""
        return {
            'module': self.module_name,
            'status': self.status,
            'uptime': (datetime.now() - self.start_time).total_seconds(),
            'timestamp': get_timestamp()
        }
    
    def set_status(self, status):
        """Set module status"""
        self.status = status
        self.logger.info(f"Module status changed to: {status}")
    
    def log_info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def log_error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def log_warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
