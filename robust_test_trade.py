#!/usr/bin/env python3
"""
ROBUST TEST TRADE EXECUTION
Iterate until successful test trade completion

REQUIREMENTS:
- Execute 1 real test trade with proven parameters
- Handle all edge cases and errors
- Retry until successful completion
- Validate full cycle (entry -> TP or SL hit)
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup robust logging without Unicode issues
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('robust_test_trade.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RobustTestTrade:
    """Robust test trade execution with error handling and retries"""
    
    def __init__(self):
        self.test_status = "INITIALIZING"
        self.max_retries = 3
        self.retry_count = 0
        
        # PROVEN MODEL TEST PARAMETERS
        self.stop_loss_percent = 0.001      # 0.1% SL
        self.take_profit_percent = 0.0025   # 0.25% TP
        self.risk_reward_ratio = 2.5        # 2.5:1 RR
        self.test_risk_amount = 5.0         # $5 test risk
        
        # Test trade tracking
        self.test_trade_data = {}
        self.binance = None
        self.telegram_bot = None
        
        logger.info("ROBUST TEST TRADE EXECUTOR INITIALIZED")
        logger.info(f"Test Parameters: SL={self.stop_loss_percent:.1%}, TP={self.take_profit_percent:.1%}, RR={self.risk_reward_ratio:.1f}:1")
    
    def initialize_connections(self):
        """Initialize all connections with error handling"""
        try:
            logger.info("Initializing connections...")
            
            # Initialize Binance connector
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test connection
            account_info = self.binance.get_account_info()
            if not account_info:
                raise Exception("Binance connection failed")
            
            logger.info("Binance connection: SUCCESS")
            
            # Get isolated margin balance
            balance_info = self.binance.get_isolated_margin_balance()
            if not balance_info:
                raise Exception("Isolated margin access failed")
            
            self.current_balance = balance_info['total_usdt_value']
            logger.info(f"Isolated margin balance: ${self.current_balance:.2f}")
            
            # Initialize Telegram bot
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                logger.info("Telegram bot: SUCCESS")
            except Exception as e:
                logger.warning(f"Telegram bot failed (continuing without): {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            logger.error(f"Connection initialization failed: {e}")
            return False
    
    def calculate_test_position(self):
        """Calculate test position with robust error handling"""
        try:
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate position size for $5 risk with 0.1% stop loss
            position_size_usdt = self.test_risk_amount / self.stop_loss_percent  # $5000 position
            btc_quantity = position_size_usdt / current_price
            
            # Calculate SL and TP prices
            entry_price = current_price
            stop_loss_price = entry_price * (1 - self.stop_loss_percent)  # -0.1%
            take_profit_price = entry_price * (1 + self.take_profit_percent)  # +0.25%
            
            # Get symbol info for minimum requirements
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            min_qty = None
            min_notional = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    min_qty = float(filter_item['minQty'])
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    min_notional = float(filter_item['minNotional'])
            
            # Ensure quantity meets minimum requirements
            if min_qty and btc_quantity < min_qty:
                btc_quantity = min_qty * 1.1  # 10% above minimum
                position_size_usdt = btc_quantity * current_price
                logger.info(f"Adjusted quantity to meet minimum: {btc_quantity:.8f} BTC")
            
            if min_notional and position_size_usdt < min_notional:
                position_size_usdt = min_notional * 1.1  # 10% above minimum
                btc_quantity = position_size_usdt / current_price
                logger.info(f"Adjusted position to meet minimum notional: ${position_size_usdt:.2f}")
            
            # Store calculated values
            self.test_trade_data = {
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'btc_quantity': btc_quantity,
                'position_size_usdt': position_size_usdt,
                'min_qty': min_qty,
                'min_notional': min_notional,
                'calculated_risk': position_size_usdt * self.stop_loss_percent
            }
            
            logger.info("TEST POSITION CALCULATION:")
            logger.info(f"  Entry Price: ${entry_price:.2f}")
            logger.info(f"  Stop Loss: ${stop_loss_price:.2f} (-0.1%)")
            logger.info(f"  Take Profit: ${take_profit_price:.2f} (+0.25%)")
            logger.info(f"  BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"  Position Size: ${position_size_usdt:.2f}")
            logger.info(f"  Calculated Risk: ${self.test_trade_data['calculated_risk']:.2f}")
            logger.info(f"  Min Quantity: {min_qty}")
            logger.info(f"  Min Notional: ${min_notional}")
            
            return True
            
        except Exception as e:
            logger.error(f"Position calculation failed: {e}")
            return False
    
    def execute_test_order(self):
        """Execute test order with comprehensive error handling"""
        try:
            logger.info("EXECUTING TEST ORDER...")
            
            # Send Telegram notification
            self.send_telegram_notification("TEST_STARTING")
            
            # Execute BUY market order
            logger.info("Placing BUY market order...")
            
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=f"{self.test_trade_data['btc_quantity']:.8f}"
            )
            
            logger.info(f"BUY order executed successfully: {buy_order['orderId']}")
            
            # Get actual execution details
            order_details = self.binance.client.get_order(
                symbol='BTCUSDT',
                orderId=buy_order['orderId']
            )
            
            actual_quantity = float(order_details['executedQty'])
            actual_price = float(order_details.get('price', '0'))
            
            # If price is 0, calculate from fills
            if actual_price == 0 and 'fills' in buy_order:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_price = total_cost / actual_quantity if actual_quantity > 0 else self.test_trade_data['entry_price']
            
            # Update trade data with actual execution
            self.test_trade_data.update({
                'entry_order_id': buy_order['orderId'],
                'actual_entry_price': actual_price,
                'actual_quantity': actual_quantity,
                'entry_time': datetime.now(),
                'order_status': order_details['status']
            })
            
            # Recalculate SL and TP based on actual entry
            actual_stop_loss = actual_price * (1 - self.stop_loss_percent)
            actual_take_profit = actual_price * (1 + self.take_profit_percent)
            
            self.test_trade_data.update({
                'actual_stop_loss': actual_stop_loss,
                'actual_take_profit': actual_take_profit
            })
            
            logger.info("ACTUAL EXECUTION DETAILS:")
            logger.info(f"  Actual Entry Price: ${actual_price:.2f}")
            logger.info(f"  Actual Quantity: {actual_quantity:.8f} BTC")
            logger.info(f"  Actual Stop Loss: ${actual_stop_loss:.2f}")
            logger.info(f"  Actual Take Profit: ${actual_take_profit:.2f}")
            logger.info(f"  Order Status: {order_details['status']}")
            
            # Send execution notification
            self.send_telegram_notification("TEST_EXECUTED")
            
            return True
            
        except Exception as e:
            logger.error(f"Test order execution failed: {e}")
            self.send_telegram_notification("TEST_FAILED")
            return False
    
    def place_exit_orders(self):
        """Place OCO exit orders (TP + SL)"""
        try:
            logger.info("Placing OCO exit orders...")
            
            # Place OCO order (Take Profit + Stop Loss)
            oco_order = self.binance.client.create_oco_order(
                symbol='BTCUSDT',
                side='SELL',
                quantity=f"{self.test_trade_data['actual_quantity']:.8f}",
                price=f"{self.test_trade_data['actual_take_profit']:.2f}",
                stopPrice=f"{self.test_trade_data['actual_stop_loss']:.2f}",
                stopLimitPrice=f"{self.test_trade_data['actual_stop_loss'] * 0.999:.2f}",
                stopLimitTimeInForce='GTC'
            )
            
            self.test_trade_data['oco_order_id'] = oco_order['orderListId']
            
            logger.info(f"OCO order placed successfully: {oco_order['orderListId']}")
            logger.info("TEST TRADE ACTIVE - Monitoring for TP or SL...")
            
            return True
            
        except Exception as e:
            logger.error(f"OCO order placement failed: {e}")
            return False
    
    def monitor_test_trade(self, max_wait_hours=2):
        """Monitor test trade until completion"""
        try:
            logger.info(f"Monitoring test trade (max {max_wait_hours} hours)...")
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < (max_wait_hours * 3600):
                try:
                    # Check OCO order status
                    oco_status = self.binance.client.get_order_list(
                        orderListId=self.test_trade_data['oco_order_id']
                    )
                    
                    if oco_status['listStatusType'] == 'ALL_DONE':
                        # Trade completed
                        for order in oco_status['orders']:
                            if order['status'] == 'FILLED':
                                exit_price = float(order['price'])
                                entry_price = self.test_trade_data['actual_entry_price']
                                
                                # Determine result
                                tp_price = self.test_trade_data['actual_take_profit']
                                sl_price = self.test_trade_data['actual_stop_loss']
                                
                                if abs(exit_price - tp_price) < abs(exit_price - sl_price):
                                    result = 'WIN'
                                    pnl = self.test_risk_amount * 2.5  # 2.5:1 RR
                                    logger.info("TEST TRADE WON - TAKE PROFIT HIT!")
                                else:
                                    result = 'LOSS'
                                    pnl = -self.test_risk_amount
                                    logger.info("TEST TRADE LOST - STOP LOSS HIT")
                                
                                # Update test data
                                self.test_trade_data.update({
                                    'exit_time': datetime.now(),
                                    'exit_price': exit_price,
                                    'result': result,
                                    'pnl': pnl,
                                    'duration_minutes': (datetime.now() - self.test_trade_data['entry_time']).total_seconds() / 60
                                })
                                
                                # Send completion notification
                                self.send_telegram_notification("TEST_COMPLETED")
                                
                                logger.info("TEST TRADE MONITORING COMPLETED")
                                self.test_status = "COMPLETED"
                                return True
                
                except Exception as e:
                    logger.warning(f"Error checking OCO status: {e}")
                
                # Wait 30 seconds before next check
                time.sleep(30)
                
                # Log progress every 5 minutes
                elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                if elapsed_minutes % 5 == 0:
                    logger.info(f"Monitoring progress: {elapsed_minutes:.0f} minutes elapsed")
            
            logger.warning(f"Test trade monitoring timeout after {max_wait_hours} hours")
            return False
            
        except Exception as e:
            logger.error(f"Test trade monitoring failed: {e}")
            return False
    
    def send_telegram_notification(self, event_type):
        """Send Telegram notification"""
        try:
            if not self.telegram_bot:
                return
            
            if event_type == "TEST_STARTING":
                message = f"""
ROBUST TEST TRADE STARTING

System Verification Test:
- Binance Connection: VERIFIED
- Isolated Margin: ${self.current_balance:.2f}
- Test Risk: ${self.test_risk_amount:.2f}
- Parameters: SL=0.1%, TP=0.25%, RR=2.5:1

Executing test trade...
"""
            
            elif event_type == "TEST_EXECUTED":
                data = self.test_trade_data
                message = f"""
TEST TRADE EXECUTED

Entry Details:
- Price: ${data['actual_entry_price']:.2f}
- Quantity: {data['actual_quantity']:.8f} BTC
- Stop Loss: ${data['actual_stop_loss']:.2f} (-0.1%)
- Take Profit: ${data['actual_take_profit']:.2f} (+0.25%)

Monitoring until TP or SL hit...
"""
            
            elif event_type == "TEST_COMPLETED":
                data = self.test_trade_data
                result_emoji = "🎉" if data['result'] == 'WIN' else "📉"
                message = f"""
{result_emoji} TEST TRADE COMPLETED

Result: {data['result']}
- Entry: ${data['actual_entry_price']:.2f}
- Exit: ${data['exit_price']:.2f}
- P&L: ${data['pnl']:.2f}
- Duration: {data['duration_minutes']:.0f} minutes

SYSTEM VALIDATION COMPLETE
All systems verified and operational
READY FOR LIVE TRADING!
"""
            
            elif event_type == "TEST_FAILED":
                message = """
TEST TRADE FAILED

System check required:
- Check Binance connection
- Verify API permissions
- Review account balance

Will retry automatically...
"""
            
            self.telegram_bot.send_message(message)
            logger.info(f"Telegram notification sent: {event_type}")
            
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
    
    def save_test_results(self):
        """Save comprehensive test results"""
        try:
            results = {
                'test_type': 'Robust Test Trade Execution',
                'test_status': self.test_status,
                'retry_count': self.retry_count,
                'timestamp': datetime.now().isoformat(),
                'test_parameters': {
                    'stop_loss_percent': self.stop_loss_percent,
                    'take_profit_percent': self.take_profit_percent,
                    'risk_reward_ratio': self.risk_reward_ratio,
                    'test_risk_amount': self.test_risk_amount
                },
                'trade_data': self.test_trade_data,
                'system_validation': {
                    'binance_connection': 'PASSED',
                    'isolated_margin': 'PASSED',
                    'order_execution': 'PASSED' if self.test_status == 'COMPLETED' else 'FAILED',
                    'tp_sl_functionality': 'PASSED' if self.test_status == 'COMPLETED' else 'FAILED',
                    'telegram_integration': 'PASSED' if self.telegram_bot else 'SKIPPED'
                },
                'ready_for_live_trading': self.test_status == 'COMPLETED'
            }
            
            # Save results
            with open('robust_test_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info("Test results saved to robust_test_results.json")
            return results
            
        except Exception as e:
            logger.error(f"Failed to save test results: {e}")
            return None

    def run_test_with_retries(self):
        """Run test trade with automatic retries"""
        try:
            logger.info("STARTING ROBUST TEST TRADE WITH RETRIES")
            logger.info("="*80)

            while self.retry_count < self.max_retries:
                try:
                    logger.info(f"TEST ATTEMPT {self.retry_count + 1}/{self.max_retries}")

                    # Step 1: Initialize connections
                    if not self.initialize_connections():
                        raise Exception("Connection initialization failed")

                    # Step 2: Calculate position
                    if not self.calculate_test_position():
                        raise Exception("Position calculation failed")

                    # Step 3: Execute test order
                    if not self.execute_test_order():
                        raise Exception("Test order execution failed")

                    # Step 4: Place exit orders
                    if not self.place_exit_orders():
                        raise Exception("Exit order placement failed")

                    # Step 5: Monitor until completion
                    if not self.monitor_test_trade():
                        raise Exception("Test trade monitoring failed or timeout")

                    # Success!
                    logger.info("TEST TRADE COMPLETED SUCCESSFULLY!")
                    self.test_status = "COMPLETED"
                    self.save_test_results()
                    return True

                except Exception as e:
                    self.retry_count += 1
                    logger.error(f"Test attempt {self.retry_count} failed: {e}")

                    if self.retry_count < self.max_retries:
                        wait_time = 60 * self.retry_count  # Increasing wait time
                        logger.info(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                    else:
                        logger.error("All retry attempts exhausted")
                        self.test_status = "FAILED"
                        self.save_test_results()
                        return False

            return False

        except Exception as e:
            logger.error(f"Robust test trade failed: {e}")
            self.test_status = "FAILED"
            return False

def main():
    """Main execution with comprehensive error handling"""
    print("ROBUST TEST TRADE EXECUTION")
    print("Iterating until successful test trade completion")
    print("Using proven 1-year performance model parameters")
    print("SL: 0.1%, TP: 0.25%, RR: 2.5:1, Risk: $5")
    print("="*80)

    try:
        # Initialize robust test executor
        test_executor = RobustTestTrade()

        # Run test with retries
        if test_executor.run_test_with_retries():
            print("\nSUCCESS: TEST TRADE COMPLETED!")
            print("All systems verified and operational")
            print("System is ready for live deployment")

            # Display results
            if hasattr(test_executor, 'test_trade_data') and test_executor.test_trade_data:
                data = test_executor.test_trade_data
                print(f"\nTEST RESULTS:")
                print(f"  Result: {data.get('result', 'Unknown')}")
                print(f"  Entry: ${data.get('actual_entry_price', 0):.2f}")
                print(f"  Exit: ${data.get('exit_price', 0):.2f}")
                print(f"  P&L: ${data.get('pnl', 0):.2f}")
                print(f"  Duration: {data.get('duration_minutes', 0):.0f} minutes")
                print(f"  Retry Count: {test_executor.retry_count}")

                if data.get('result') == 'WIN':
                    print("🎉 Test trade WON - Take Profit hit!")
                else:
                    print("📉 Test trade LOST - Stop Loss hit (normal)")

                print("\nSYSTEM VALIDATION COMPLETE:")
                print("✅ Binance Connection: WORKING")
                print("✅ Order Execution: PERFECT")
                print("✅ TP/SL Functionality: CONFIRMED")
                print("✅ Risk Management: VALIDATED")
                print("✅ Isolated Margin: OPERATIONAL")
                print("\n🚀 READY FOR LIVE TRADING DEPLOYMENT!")

        else:
            print("\nFAILED: Test trade could not be completed")
            print(f"Attempted {test_executor.retry_count} times")
            print("Check robust_test_trade.log for details")
            print("System is NOT ready for live deployment")

    except Exception as e:
        print(f"\nCRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
