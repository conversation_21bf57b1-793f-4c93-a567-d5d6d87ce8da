#!/usr/bin/env python3
"""
SIMPLE DOLLAR AMOUNT TEST TRADE
Execute test trade targeting $1 SL and $2.5 TP using available balance
"""

import sys
import json
import logging
import decimal
from datetime import datetime

sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_dollar_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleDollarTest:
    """Simple dollar amount test trade"""
    
    def __init__(self):
        self.target_sl_amount = 1.0
        self.target_tp_amount = 2.5
        self.order_numbers = {}
        self.trade_data = {}
        
        logger.info("SIMPLE DOLLAR AMOUNT TEST INITIALIZED")
        logger.info(f"Target SL: ${self.target_sl_amount:.2f}")
        logger.info(f"Target TP: ${self.target_tp_amount:.2f}")
    
    def initialize_connections(self):
        """Initialize connections"""
        try:
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test connection
            account_info = self.binance.get_account_info()
            if not account_info:
                raise Exception("Binance connection failed")
            
            # Get balance
            balance_info = self.binance.get_isolated_margin_balance()
            self.usdt_balance = balance_info['usdt_balance']
            
            logger.info(f"Available USDT: ${self.usdt_balance:.2f}")
            
            # Initialize Telegram
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                logger.info("Telegram bot: SUCCESS")
            except Exception as e:
                logger.warning(f"Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def calculate_simple_position(self):
        """Calculate position using available balance"""
        try:
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Use a reasonable position size from available balance
            position_size = min(50.0, self.usdt_balance * 0.3)  # Use $50 or 30% of balance
            
            # Calculate BTC quantity
            btc_quantity = position_size / current_price
            
            # Round to 8 decimals (standard for BTC)
            btc_quantity = round(btc_quantity, 8)
            
            # Ensure minimum quantity (0.00001 BTC)
            if btc_quantity < 0.00001:
                btc_quantity = 0.00001
            
            # Recalculate actual position size
            actual_position_size = btc_quantity * current_price
            
            # Calculate SL and TP percentages to achieve target amounts
            sl_percent = self.target_sl_amount / actual_position_size
            tp_percent = self.target_tp_amount / actual_position_size
            
            # Calculate SL and TP prices
            entry_price = current_price
            stop_loss_price = round(entry_price * (1 - sl_percent), 2)
            take_profit_price = round(entry_price * (1 + tp_percent), 2)
            
            # Calculate actual dollar amounts
            actual_sl_amount = actual_position_size * sl_percent
            actual_tp_amount = actual_position_size * tp_percent
            
            self.trade_data = {
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'btc_quantity': btc_quantity,
                'position_size': actual_position_size,
                'sl_percent': sl_percent,
                'tp_percent': tp_percent,
                'actual_sl_amount': actual_sl_amount,
                'actual_tp_amount': actual_tp_amount
            }
            
            logger.info("POSITION CALCULATION:")
            logger.info(f"  Entry Price: ${entry_price:.2f}")
            logger.info(f"  BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"  Position Size: ${actual_position_size:.2f}")
            logger.info(f"  Stop Loss: ${stop_loss_price:.2f} (${actual_sl_amount:.2f})")
            logger.info(f"  Take Profit: ${take_profit_price:.2f} (${actual_tp_amount:.2f})")
            logger.info(f"  Risk-Reward: {actual_tp_amount/actual_sl_amount:.1f}:1")
            
            return True
            
        except Exception as e:
            logger.error(f"Position calculation failed: {e}")
            return False
    
    def execute_entry(self):
        """Execute entry order"""
        try:
            logger.info("EXECUTING ENTRY ORDER...")
            
            quantity_str = f"{self.trade_data['btc_quantity']:.8f}"
            
            # Execute market buy order
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=quantity_str
            )
            
            self.order_numbers['entry_order_id'] = buy_order['orderId']
            
            logger.info(f"🎉 ENTRY ORDER EXECUTED!")
            logger.info(f"📋 Entry Order ID: {buy_order['orderId']}")
            
            # Get actual execution details
            order_details = self.binance.client.get_order(
                symbol='BTCUSDT',
                orderId=buy_order['orderId']
            )
            
            actual_quantity = float(order_details['executedQty'])
            
            # Calculate average fill price
            if 'fills' in buy_order and buy_order['fills']:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_price = total_cost / actual_quantity
            else:
                actual_price = self.trade_data['entry_price']
            
            # Update with actual execution
            self.trade_data.update({
                'actual_entry_price': actual_price,
                'actual_quantity': actual_quantity,
                'actual_cost': actual_quantity * actual_price,
                'entry_time': datetime.now()
            })
            
            # Recalculate SL and TP based on actual execution
            actual_sl_amount = self.trade_data['actual_cost'] * self.trade_data['sl_percent']
            actual_tp_amount = self.trade_data['actual_cost'] * self.trade_data['tp_percent']
            
            actual_stop_loss = round(actual_price * (1 - self.trade_data['sl_percent']), 2)
            actual_take_profit = round(actual_price * (1 + self.trade_data['tp_percent']), 2)
            
            self.trade_data.update({
                'actual_stop_loss': actual_stop_loss,
                'actual_take_profit': actual_take_profit,
                'final_sl_amount': actual_sl_amount,
                'final_tp_amount': actual_tp_amount
            })
            
            logger.info("📊 ACTUAL EXECUTION:")
            logger.info(f"  Entry Price: ${actual_price:.2f}")
            logger.info(f"  Quantity: {actual_quantity:.8f} BTC")
            logger.info(f"  Cost: ${self.trade_data['actual_cost']:.2f}")
            logger.info(f"  SL: ${actual_stop_loss:.2f} (${actual_sl_amount:.2f})")
            logger.info(f"  TP: ${actual_take_profit:.2f} (${actual_tp_amount:.2f})")
            
            self.send_telegram_notification("ENTRY_EXECUTED")
            
            return True
            
        except Exception as e:
            logger.error(f"Entry execution failed: {e}")
            return False
    
    def place_exit_orders(self):
        """Place exit orders"""
        try:
            logger.info("PLACING EXIT ORDERS...")
            
            quantity_str = f"{self.trade_data['actual_quantity']:.8f}"
            tp_price_str = f"{self.trade_data['actual_take_profit']:.2f}"
            sl_price_str = f"{self.trade_data['actual_stop_loss']:.2f}"
            sl_limit_str = f"{self.trade_data['actual_stop_loss'] * 0.999:.2f}"
            
            # Try separate orders (more reliable)
            try:
                # Place Take Profit order
                tp_order = self.binance.client.create_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    type='LIMIT',
                    timeInForce='GTC',
                    quantity=quantity_str,
                    price=tp_price_str
                )
                
                # Place Stop Loss order
                sl_order = self.binance.client.create_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    type='STOP_LOSS_LIMIT',
                    timeInForce='GTC',
                    quantity=quantity_str,
                    price=sl_limit_str,
                    stopPrice=sl_price_str
                )
                
                self.order_numbers['take_profit_order_id'] = tp_order['orderId']
                self.order_numbers['stop_loss_order_id'] = sl_order['orderId']
                
                logger.info("🎉 EXIT ORDERS PLACED!")
                logger.info(f"📋 Take Profit Order: {tp_order['orderId']}")
                logger.info(f"📋 Stop Loss Order: {sl_order['orderId']}")
                
                self.send_telegram_notification("EXIT_ORDERS_PLACED")
                
                return True
                
            except Exception as e:
                logger.error(f"Exit orders failed: {e}")
                return False
            
        except Exception as e:
            logger.error(f"Exit order placement failed: {e}")
            return False
    
    def monitor_trade(self):
        """Monitor trade until completion"""
        try:
            logger.info("🔄 MONITORING TRADE UNTIL COMPLETION")
            logger.info("="*60)
            
            import time
            start_time = datetime.now()
            check_count = 0
            
            while (datetime.now() - start_time).total_seconds() < (24 * 3600):  # 24 hours max
                check_count += 1
                
                logger.info(f"🔍 CHECK #{check_count} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Get current price
                ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
                
                # Calculate current P&L
                current_pnl = (current_price - self.trade_data['actual_entry_price']) * self.trade_data['actual_quantity']
                
                logger.info(f"📊 Current Price: ${current_price:.2f}")
                logger.info(f"💰 Current P&L: ${current_pnl:.2f}")
                
                # Check orders
                try:
                    tp_order = self.binance.client.get_order(
                        symbol='BTCUSDT',
                        orderId=self.order_numbers['take_profit_order_id']
                    )
                    
                    sl_order = self.binance.client.get_order(
                        symbol='BTCUSDT',
                        orderId=self.order_numbers['stop_loss_order_id']
                    )
                    
                    if tp_order['status'] == 'FILLED':
                        # Take profit hit
                        exit_price = float(tp_order['price'])
                        result = 'WIN'
                        result_type = 'TAKE PROFIT'
                        pnl = self.trade_data['final_tp_amount']
                        
                        # Cancel SL order
                        try:
                            self.binance.client.cancel_order(
                                symbol='BTCUSDT',
                                orderId=self.order_numbers['stop_loss_order_id']
                            )
                        except:
                            pass
                        
                        self.complete_trade(exit_price, result, result_type, pnl, tp_order['orderId'])
                        return True
                        
                    elif sl_order['status'] == 'FILLED':
                        # Stop loss hit
                        exit_price = float(sl_order['price'])
                        result = 'LOSS'
                        result_type = 'STOP LOSS'
                        pnl = -self.trade_data['final_sl_amount']
                        
                        # Cancel TP order
                        try:
                            self.binance.client.cancel_order(
                                symbol='BTCUSDT',
                                orderId=self.order_numbers['take_profit_order_id']
                            )
                        except:
                            pass
                        
                        self.complete_trade(exit_price, result, result_type, pnl, sl_order['orderId'])
                        return True
                    
                except Exception as e:
                    logger.warning(f"Order check failed: {e}")
                
                # Wait before next check
                time.sleep(60)  # Check every minute
                
                # Log progress every 10 checks
                if check_count % 10 == 0:
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    logger.info(f"⏱️ Monitoring: {elapsed_minutes:.0f} minutes elapsed")
            
            logger.warning("Monitoring timeout")
            return False
            
        except Exception as e:
            logger.error(f"Monitoring failed: {e}")
            return False
    
    def complete_trade(self, exit_price, result, result_type, pnl, exit_order_id):
        """Complete trade and save results"""
        try:
            self.trade_data.update({
                'exit_price': exit_price,
                'result': result,
                'result_type': result_type,
                'pnl': pnl,
                'exit_order_id': exit_order_id,
                'exit_time': datetime.now(),
                'duration_minutes': (datetime.now() - self.trade_data['entry_time']).total_seconds() / 60
            })
            
            logger.info("🎉 TRADE COMPLETED!")
            logger.info(f"📋 Result: {result} ({result_type})")
            logger.info(f"📋 Exit Price: ${exit_price:.2f}")
            logger.info(f"📋 P&L: ${pnl:.2f}")
            logger.info(f"📋 Exit Order: {exit_order_id}")
            
            self.send_telegram_notification("TRADE_COMPLETED")
            self.save_results()
            
        except Exception as e:
            logger.error(f"Trade completion failed: {e}")
    
    def send_telegram_notification(self, event_type):
        """Send Telegram notifications"""
        try:
            if not self.telegram_bot:
                return
            
            if event_type == "ENTRY_EXECUTED":
                message = f"""
🎉 DOLLAR AMOUNT TEST - ENTRY EXECUTED!

📋 ORDER NUMBERS:
Entry Order: {self.order_numbers['entry_order_id']}

📊 EXECUTION DETAILS:
- Entry Price: ${self.trade_data['actual_entry_price']:.2f}
- Quantity: {self.trade_data['actual_quantity']:.8f} BTC
- Cost: ${self.trade_data['actual_cost']:.2f}

🎯 TARGET LEVELS:
- Take Profit: ${self.trade_data['actual_take_profit']:.2f} (${self.trade_data['final_tp_amount']:.2f})
- Stop Loss: ${self.trade_data['actual_stop_loss']:.2f} (${self.trade_data['final_sl_amount']:.2f})

Placing exit orders...
"""
            
            elif event_type == "EXIT_ORDERS_PLACED":
                message = f"""
🎯 EXIT ORDERS PLACED!

📋 ALL ORDER NUMBERS:
Entry: {self.order_numbers['entry_order_id']}
Take Profit: {self.order_numbers['take_profit_order_id']}
Stop Loss: {self.order_numbers['stop_loss_order_id']}

💰 DOLLAR TARGETS:
- SL Amount: ${self.trade_data['final_sl_amount']:.2f}
- TP Amount: ${self.trade_data['final_tp_amount']:.2f}
- Risk-Reward: {self.trade_data['final_tp_amount']/self.trade_data['final_sl_amount']:.1f}:1

🔄 MONITORING UNTIL COMPLETION
"""
            
            elif event_type == "TRADE_COMPLETED":
                result_emoji = "🎉" if self.trade_data['result'] == 'WIN' else "📉"
                message = f"""
{result_emoji} DOLLAR AMOUNT TEST COMPLETED!

📋 FINAL ORDER NUMBERS:
Entry: {self.order_numbers['entry_order_id']}
Exit: {self.trade_data['exit_order_id']}
TP: {self.order_numbers['take_profit_order_id']}
SL: {self.order_numbers['stop_loss_order_id']}

📊 FINAL RESULTS:
- Result: {self.trade_data['result']} ({self.trade_data['result_type']})
- Entry: ${self.trade_data['actual_entry_price']:.2f}
- Exit: ${self.trade_data['exit_price']:.2f}
- P&L: ${self.trade_data['pnl']:.2f}
- Duration: {self.trade_data['duration_minutes']:.1f} minutes

💰 DOLLAR AMOUNT VALIDATION:
- Target: ${self.target_sl_amount:.2f} SL / ${self.target_tp_amount:.2f} TP
- Achieved: ${abs(self.trade_data['pnl']):.2f}

✅ SYSTEM VALIDATION COMPLETE!
"""
            
            self.telegram_bot.send_message(message)
            logger.info(f"📱 Telegram notification sent: {event_type}")
            
        except Exception as e:
            logger.error(f"Telegram notification failed: {e}")
    
    def save_results(self):
        """Save results"""
        try:
            results = {
                'test_type': 'Simple Dollar Amount Test',
                'timestamp': datetime.now().isoformat(),
                'target_amounts': {
                    'stop_loss': self.target_sl_amount,
                    'take_profit': self.target_tp_amount
                },
                'actual_amounts': {
                    'stop_loss': self.trade_data['final_sl_amount'],
                    'take_profit': self.trade_data['final_tp_amount'],
                    'achieved_pnl': self.trade_data['pnl']
                },
                'order_numbers': self.order_numbers,
                'trade_data': self.trade_data,
                'validation_complete': True
            }
            
            filename = f'simple_dollar_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"📄 Results saved to {filename}")
            
        except Exception as e:
            logger.error(f"Save results failed: {e}")
    
    def run_test(self):
        """Run complete test"""
        try:
            logger.info("🚀 STARTING SIMPLE DOLLAR AMOUNT TEST")
            
            if not self.initialize_connections():
                return False
            
            if not self.calculate_simple_position():
                return False
            
            if not self.execute_entry():
                return False
            
            if not self.place_exit_orders():
                return False
            
            if not self.monitor_trade():
                return False
            
            logger.info("🎉 TEST COMPLETED SUCCESSFULLY!")
            return True
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            return False

def main():
    """Main execution"""
    print("🚀 SIMPLE DOLLAR AMOUNT TEST TRADE")
    print("Target: $1 Stop Loss | $2.5 Take Profit | 2.5:1 Risk-Reward")
    print("="*70)
    print("⚠️  REAL MONEY TRADE - All order numbers provided")
    print("="*70)
    
    try:
        test = SimpleDollarTest()
        
        if test.run_test():
            print("\n🎉 SUCCESS: DOLLAR AMOUNT TEST COMPLETED!")
            
            if test.trade_data and 'result' in test.trade_data:
                data = test.trade_data
                orders = test.order_numbers
                
                print(f"\n📋 ALL ORDER NUMBERS:")
                print(f"  Entry: {orders.get('entry_order_id', 'N/A')}")
                print(f"  Take Profit: {orders.get('take_profit_order_id', 'N/A')}")
                print(f"  Stop Loss: {orders.get('stop_loss_order_id', 'N/A')}")
                print(f"  Exit: {data.get('exit_order_id', 'N/A')}")
                
                print(f"\n📊 RESULTS:")
                print(f"  Result: {data.get('result', 'Unknown')} ({data.get('result_type', 'Unknown')})")
                print(f"  P&L: ${data.get('pnl', 0):.2f}")
                print(f"  Duration: {data.get('duration_minutes', 0):.1f} minutes")
                
                print(f"\n💰 DOLLAR VALIDATION:")
                print(f"  Target SL: ${test.target_sl_amount:.2f}")
                print(f"  Target TP: ${test.target_tp_amount:.2f}")
                print(f"  Achieved: ${abs(data.get('pnl', 0)):.2f}")
                
                print("\n✅ VALIDATION COMPLETE!")
        else:
            print("\n❌ TEST FAILED")
            
    except Exception as e:
        print(f"\nERROR: {e}")

if __name__ == "__main__":
    main()
