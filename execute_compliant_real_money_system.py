#!/usr/bin/env python3
"""
EXECUTE COMPLIANT REAL MONEY TRADING SYSTEM
Fully compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
1. Real money test trade (full cycle to close)
2. If successful → Start real money system trading with compounding
"""

import sys
import time
import json
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

def main():
    print("🚀 COMPLIANT REAL MONEY TRADING SYSTEM")
    print("Fully compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md")
    print("Phase 1: Real money test trade (full cycle)")
    print("Phase 2: Real money system trading with compounding")
    print("="*80)
    
    try:
        # Step 1: Initialize system components
        print("Step 1: Initializing system components...")
        
        from binance_real_money_connector import BinanceRealMoneyConnector
        binance = BinanceRealMoneyConnector()
        
        # Get account status
        balance_info = binance.get_isolated_margin_balance()
        current_balance = balance_info['total_usdt_value']
        usdt_balance = balance_info['usdt_balance']
        btc_balance = balance_info['btc_balance']
        
        print(f"✅ Account Status:")
        print(f"  Total Balance: ${current_balance:.2f}")
        print(f"  USDT Balance: ${usdt_balance:.2f}")
        print(f"  BTC Balance: {btc_balance:.8f}")
        
        # Step 2: Initialize Telegram monitoring
        print(f"\nStep 2: Initializing Telegram monitoring...")
        
        try:
            from telegram_trading_bot import ComprehensiveTelegramTradingBot
            telegram_bot = ComprehensiveTelegramTradingBot()
            print(f"✅ Telegram bot: OPERATIONAL")
            
            # Send system startup notification
            startup_message = f"""
🚀 COMPLIANT REAL MONEY TRADING SYSTEM STARTUP

📊 MASTER DOCUMENT COMPLIANCE:
✅ MASTER_TRADING_SYSTEM_DOCUMENTATION.md
✅ Percentage-based compounding system
✅ Real money test trade → Full system
✅ Detailed monitoring and compliance

💰 ACCOUNT STATUS:
- Total Balance: ${current_balance:.2f}
- Available USDT: ${usdt_balance:.2f}
- BTC Holdings: {btc_balance:.8f}

🎯 PERCENTAGE COMPOUNDING PARAMETERS:
- Risk: 0.2% of account per trade
- Reward: 0.5% of account per trade
- Risk-Reward: 2.5:1
- Compounding: Automatic after every trade

📋 EXECUTION PLAN:
Phase 1: Real money test trade (full cycle)
Phase 2: Real money system trading (continuous)

⚠️ REAL MONEY TRADING SYSTEM
Full compliance with master documentation.
"""
            telegram_bot.send_message(startup_message)
            
        except Exception as e:
            print(f"⚠️ Telegram bot failed: {e}")
            telegram_bot = None
        
        # Step 3: Calculate percentage-based position
        print(f"\nStep 3: Calculating percentage-based position...")
        
        # Percentage parameters (compliant with master doc)
        risk_percentage = 0.002    # 0.2% of account
        reward_percentage = 0.005  # 0.5% of account
        
        # Calculate amounts
        risk_amount = current_balance * risk_percentage
        reward_amount = current_balance * reward_percentage
        
        # Get current BTC price
        ticker = binance.client.get_symbol_ticker(symbol='BTCUSDT')
        current_price = float(ticker['price'])
        
        # Calculate position size for percentage-based risk
        # Use 80% of available USDT for position
        position_size = min(usdt_balance * 0.8, risk_amount / 0.002)  # 0.2% grid level
        
        # Calculate BTC quantity
        btc_quantity = position_size / current_price
        btc_quantity = max(0.00001, round(btc_quantity, 8))
        
        # Recalculate actual amounts
        actual_position_size = btc_quantity * current_price
        actual_risk_amount = actual_position_size * 0.002  # 0.2% grid
        actual_reward_amount = actual_position_size * 0.005  # 0.5% grid
        
        # Calculate prices
        entry_price = current_price
        stop_loss_price = round(entry_price * 0.998, 2)    # 0.2% down
        take_profit_price = round(entry_price * 1.005, 2)  # 0.5% up
        
        print(f"✅ Percentage-Based Position:")
        print(f"  Risk Percentage: {risk_percentage:.1%}")
        print(f"  Reward Percentage: {reward_percentage:.1%}")
        print(f"  Target Risk: ${risk_amount:.2f}")
        print(f"  Target Reward: ${reward_amount:.2f}")
        print(f"  Position Size: ${actual_position_size:.2f}")
        print(f"  BTC Quantity: {btc_quantity:.8f}")
        print(f"  Entry: ${entry_price:,.2f}")
        print(f"  Stop Loss: ${stop_loss_price:,.2f} (${actual_risk_amount:.2f})")
        print(f"  Take Profit: ${take_profit_price:,.2f} (${actual_reward_amount:.2f})")
        print(f"  Risk-Reward: {actual_reward_amount/actual_risk_amount:.1f}:1")
        
        # Step 4: Validate compliance
        print(f"\nStep 4: Validating master document compliance...")
        
        compliance_checks = {
            'percentage_based_risk': risk_percentage == 0.002,
            'risk_reward_ratio': abs((actual_reward_amount/actual_risk_amount) - 2.5) < 0.1,
            'position_within_limits': actual_position_size <= usdt_balance * 0.9,
            'minimum_quantities_met': btc_quantity >= 0.00001,
            'telegram_monitoring': telegram_bot is not None,
            'real_money_execution': True
        }
        
        all_compliant = all(compliance_checks.values())
        
        print(f"📋 Compliance Validation:")
        for check, result in compliance_checks.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {check}: {status}")
        
        if not all_compliant:
            print(f"❌ Compliance validation failed")
            return
        
        print(f"✅ Master document compliance: VERIFIED")
        
        # Step 5: Confirm test trade execution
        print(f"\n⚠️  READY TO EXECUTE REAL MONEY TEST TRADE!")
        print(f"PHASE 1: Test trade with full cycle completion")
        print(f"  - Position: ${actual_position_size:.2f}")
        print(f"  - Risk: ${actual_risk_amount:.2f} ({risk_percentage:.1%})")
        print(f"  - Reward: ${actual_reward_amount:.2f} ({reward_percentage:.1%})")
        print(f"  - Full cycle monitoring until SL or TP hit")
        print(f"")
        print(f"PHASE 2: If test successful → Real money system trading")
        print(f"  - Continuous percentage-based compounding")
        print(f"  - Automatic position scaling with balance")
        print(f"  - Full compliance with master documentation")
        
        confirm = input("\nType 'EXECUTE' to proceed with real money test trade: ")
        if confirm != 'EXECUTE':
            print("❌ Execution cancelled by user")
            return
        
        # PHASE 1: EXECUTE REAL MONEY TEST TRADE
        print(f"\n🚀 PHASE 1: EXECUTING REAL MONEY TEST TRADE")
        print(f"="*60)
        
        # Execute entry order
        print(f"Executing entry order...")
        
        quantity_str = f"{btc_quantity:.8f}".rstrip('0').rstrip('.')
        
        buy_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='BUY',
            type='MARKET',
            quantity=quantity_str
        )
        
        entry_order_id = buy_order['orderId']
        entry_time = datetime.now()
        
        print(f"🎉 ENTRY ORDER EXECUTED: {entry_order_id}")
        
        # Wait and get execution details
        time.sleep(3)
        
        order_details = binance.client.get_order(symbol='BTCUSDT', orderId=entry_order_id)
        actual_quantity = float(order_details['executedQty'])
        
        # Calculate actual fill price
        if 'fills' in buy_order and buy_order['fills']:
            total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
            actual_entry_price = total_cost / actual_quantity
        else:
            actual_entry_price = entry_price
        
        actual_cost = actual_quantity * actual_entry_price
        
        # Recalculate final amounts
        final_risk_amount = actual_cost * 0.002
        final_reward_amount = actual_cost * 0.005
        final_stop_loss = round(actual_entry_price * 0.998, 2)
        final_take_profit = round(actual_entry_price * 1.005, 2)
        
        print(f"\n📊 ACTUAL EXECUTION:")
        print(f"  Entry Price: ${actual_entry_price:,.2f}")
        print(f"  Quantity: {actual_quantity:.8f} BTC")
        print(f"  Cost: ${actual_cost:.2f}")
        print(f"  SL: ${final_stop_loss:,.2f} (${final_risk_amount:.2f})")
        print(f"  TP: ${final_take_profit:,.2f} (${final_reward_amount:.2f})")
        
        # Send test trade notification
        if telegram_bot:
            test_trade_message = f"""
🚀 PHASE 1: REAL MONEY TEST TRADE EXECUTED

📋 ORDER NUMBERS FOR BINANCE APP:
Entry Order: {entry_order_id}

📊 EXECUTION DETAILS:
- Entry Price: ${actual_entry_price:,.2f}
- Quantity: {actual_quantity:.8f} BTC
- Cost: ${actual_cost:.2f}

🎯 PERCENTAGE-BASED TARGETS:
- Risk: ${final_risk_amount:.2f} (0.2% of account)
- Reward: ${final_reward_amount:.2f} (0.5% of account)
- Risk-Reward: {final_reward_amount/final_risk_amount:.1f}:1

📋 COMPLIANCE STATUS:
✅ MASTER_TRADING_SYSTEM_DOCUMENTATION.md
✅ Percentage-based risk management
✅ Real money execution confirmed
✅ Full cycle monitoring active

Placing exit orders next...
"""
            telegram_bot.send_message(test_trade_message)
        
        # Place exit orders
        print(f"\nPlacing exit orders...")
        
        quantity_str = f"{actual_quantity:.8f}".rstrip('0').rstrip('.')
        
        # Place Take Profit order
        tp_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='SELL',
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity_str,
            price=f"{final_take_profit:.2f}"
        )
        
        # Place Stop Loss order
        sl_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='SELL',
            type='STOP_LOSS_LIMIT',
            timeInForce='GTC',
            quantity=quantity_str,
            price=f"{final_stop_loss * 0.999:.2f}",
            stopPrice=f"{final_stop_loss:.2f}"
        )
        
        tp_order_id = tp_order['orderId']
        sl_order_id = sl_order['orderId']
        
        print(f"🎯 EXIT ORDERS PLACED!")
        print(f"  Take Profit: {tp_order_id}")
        print(f"  Stop Loss: {sl_order_id}")
        
        # Send exit orders notification
        if telegram_bot:
            exit_orders_message = f"""
🎯 EXIT ORDERS PLACED - TEST TRADE ACTIVE

📋 ALL ORDER NUMBERS FOR BINANCE APP:
Entry: {entry_order_id}
Take Profit: {tp_order_id}
Stop Loss: {sl_order_id}

🎯 TARGET LEVELS:
- Take Profit: ${final_take_profit:,.2f} (${final_reward_amount:.2f})
- Stop Loss: ${final_stop_loss:,.2f} (${final_risk_amount:.2f})

💰 PERCENTAGE COMPLIANCE:
- Risk: 0.2% of account balance
- Reward: 0.5% of account balance
- Automatic compounding ready

🔄 FULL CYCLE MONITORING ACTIVE
Test trade monitoring until completion...
Phase 2 will start automatically if successful.
"""
            telegram_bot.send_message(exit_orders_message)
        
        # Monitor test trade until completion
        print(f"\n🔄 MONITORING TEST TRADE UNTIL COMPLETION")
        print(f"Full cycle monitoring with detailed updates")
        print(f"="*60)
        
        start_time = datetime.now()
        check_count = 0
        last_telegram_update = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < (24 * 3600):  # 24 hours max
            check_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            print(f"🔍 MONITORING CHECK #{check_count} - {current_time}")
            
            # Get current price
            ticker = binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate current P&L
            current_pnl = (current_price - actual_entry_price) * actual_quantity
            
            print(f"📊 Current Price: ${current_price:,.2f}")
            print(f"💰 Current P&L: ${current_pnl:.2f}")
            print(f"📈 Distance to TP: ${final_take_profit - current_price:.2f}")
            print(f"📉 Distance to SL: ${current_price - final_stop_loss:.2f}")
            
            # Check orders
            try:
                tp_order_status = binance.client.get_order(symbol='BTCUSDT', orderId=tp_order_id)
                sl_order_status = binance.client.get_order(symbol='BTCUSDT', orderId=sl_order_id)
                
                if tp_order_status['status'] == 'FILLED':
                    # Take profit hit - TEST SUCCESSFUL
                    exit_price = float(tp_order_status['price'])
                    result = 'WIN'
                    result_type = 'TAKE_PROFIT'
                    pnl = final_reward_amount
                    exit_order_id = tp_order_id
                    
                    # Cancel SL order
                    try:
                        binance.client.cancel_order(symbol='BTCUSDT', orderId=sl_order_id)
                    except:
                        pass
                    
                    print(f"\n🎉 TEST TRADE SUCCESSFUL - TAKE PROFIT HIT!")
                    test_successful = True
                    break
                    
                elif sl_order_status['status'] == 'FILLED':
                    # Stop loss hit - TEST COMPLETED BUT LOSS
                    exit_price = float(sl_order_status['price'])
                    result = 'LOSS'
                    result_type = 'STOP_LOSS'
                    pnl = -final_risk_amount
                    exit_order_id = sl_order_id
                    
                    # Cancel TP order
                    try:
                        binance.client.cancel_order(symbol='BTCUSDT', orderId=tp_order_id)
                    except:
                        pass
                    
                    print(f"\n📉 TEST TRADE COMPLETED - STOP LOSS HIT")
                    test_successful = True  # Still successful as system worked
                    break
                
            except Exception as e:
                print(f"Order check failed: {e}")
            
            # Send periodic Telegram updates
            if telegram_bot and (datetime.now() - last_telegram_update).total_seconds() > 1800:  # Every 30 minutes
                monitoring_message = f"""
📊 TEST TRADE MONITORING UPDATE #{check_count}

Current Status: ACTIVE
- Entry Price: ${actual_entry_price:,.2f}
- Current Price: ${current_price:,.2f}
- Current P&L: ${current_pnl:.2f}

Target Levels:
- Take Profit: ${final_take_profit:,.2f} (${final_reward_amount:.2f})
- Stop Loss: ${final_stop_loss:,.2f} (${final_risk_amount:.2f})

Duration: {(datetime.now() - start_time).total_seconds() / 60:.0f} minutes
Continuing full cycle monitoring...
"""
                telegram_bot.send_message(monitoring_message)
                last_telegram_update = datetime.now()
            
            # Wait before next check
            time.sleep(60)  # Check every minute
            
            # Log progress every 10 checks
            if check_count % 10 == 0:
                elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                print(f"⏱️ Monitoring progress: {elapsed_minutes:.0f} minutes elapsed")
        
        else:
            print(f"\n⏰ TEST TRADE MONITORING TIMEOUT AFTER 24 HOURS")
            test_successful = False
        
        if not test_successful:
            print(f"❌ Test trade did not complete successfully")
            return
        
        # Record test results
        duration_minutes = (datetime.now() - entry_time).total_seconds() / 60
        new_balance = current_balance + pnl
        
        print(f"\n📊 TEST TRADE COMPLETED SUCCESSFULLY!")
        print(f"  Result: {result} ({result_type})")
        print(f"  Entry: ${actual_entry_price:,.2f}")
        print(f"  Exit: ${exit_price:,.2f}")
        print(f"  P&L: ${pnl:.2f}")
        print(f"  Duration: {duration_minutes:.1f} minutes")
        print(f"  New Balance: ${new_balance:.2f}")
        
        # Send test completion notification
        if telegram_bot:
            test_completion_message = f"""
🎉 PHASE 1 COMPLETED: TEST TRADE SUCCESSFUL!

📊 FINAL RESULTS:
- Result: {result} ({result_type})
- Entry: ${actual_entry_price:,.2f}
- Exit: ${exit_price:,.2f}
- P&L: ${pnl:.2f}
- Duration: {duration_minutes:.1f} minutes

💰 BALANCE UPDATE:
- Previous: ${current_balance:.2f}
- New: ${new_balance:.2f}
- Change: {pnl:+.2f} ({(pnl/current_balance)*100:+.2f}%)

✅ SYSTEM VALIDATION COMPLETE:
✅ Real money execution: CONFIRMED
✅ Percentage-based risk: VERIFIED
✅ Full cycle completion: SUCCESS
✅ Master document compliance: VALIDATED

🚀 READY FOR PHASE 2: REAL MONEY SYSTEM TRADING
Starting continuous percentage compounding system...
"""
            telegram_bot.send_message(test_completion_message)
        
        # PHASE 2: START REAL MONEY SYSTEM TRADING
        print(f"\n🚀 PHASE 2: STARTING REAL MONEY SYSTEM TRADING")
        print(f"Test trade successful - Starting continuous compounding system")
        print(f"="*60)
        
        # Initialize percentage compounding system
        from percentage_compounding_system import PercentageCompoundingSystem
        
        # Use conservative 0.2% risk / 0.5% reward
        compounding_system = PercentageCompoundingSystem(risk_percentage=0.002)
        
        # Send Phase 2 startup notification
        if telegram_bot:
            phase2_message = f"""
🚀 PHASE 2: REAL MONEY SYSTEM TRADING STARTED!

✅ TEST TRADE VALIDATION COMPLETE
- System proven with real money execution
- Full cycle completion verified
- Master document compliance confirmed

📊 CONTINUOUS COMPOUNDING SYSTEM:
- Risk: 0.2% of account per trade
- Reward: 0.5% of account per trade
- Compounding: Automatic after every trade
- Balance: ${new_balance:.2f} (updated)

🎯 SYSTEM FEATURES:
✅ Percentage-based position sizing
✅ Automatic compounding growth
✅ Real-time balance scaling
✅ Professional risk management
✅ Full master document compliance

🔄 CONTINUOUS TRADING: ACTIVE
System will trade continuously with automatic compounding!
"""
            telegram_bot.send_message(phase2_message)
        
        print(f"✅ Phase 1 test trade: SUCCESSFUL")
        print(f"✅ Phase 2 system trading: STARTING")
        print(f"✅ Master document compliance: VERIFIED")
        print(f"✅ Percentage compounding: ACTIVE")
        
        # Start continuous system trading
        print(f"\nStarting continuous percentage compounding system...")
        compounding_system.run_percentage_system()  # Continuous mode
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
