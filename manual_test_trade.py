#!/usr/bin/env python3
"""
MANUAL TEST TRADE EXECUTION
Verify system is fully operational before going live

REQUIREMENTS:
- Execute 1 real test trade
- Verify Binance connection and order execution
- Confirm TP/SL functionality
- Validate all systems before live deployment
"""

import os
import sys
import time
import json
import logging
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('manual_test_trade.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ManualTestTrade:
    """Manual test trade execution to verify system"""
    
    def __init__(self):
        self.test_status = "INITIALIZING"
        
        # PROVEN MODEL TEST PARAMETERS
        self.stop_loss_percent = 0.001      # 0.1% SL
        self.take_profit_percent = 0.0025   # 0.25% TP
        self.risk_reward_ratio = 2.5        # 2.5:1 RR
        self.test_risk_amount = 5.0         # $5 test risk
        
        logger.info("🧪 MANUAL TEST TRADE INITIALIZED")
        logger.info("📊 Using PROVEN parameters: SL=0.1%, TP=0.25%, RR=2.5:1")
    
    def verify_binance_connection(self):
        """Verify Binance connection and account access"""
        try:
            logger.info("🔄 Verifying Binance connection...")
            
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test basic connection
            account_info = self.binance.get_account_info()
            if not account_info:
                logger.error("❌ Binance account connection failed")
                return False
            
            logger.info("✅ Binance account connection verified")
            
            # Test isolated margin access
            margin_balance = self.binance.get_isolated_margin_balance()
            if not margin_balance:
                logger.error("❌ Isolated margin access failed")
                return False
            
            self.current_balance = margin_balance['total_usdt_value']
            logger.info(f"✅ Isolated margin verified: ${self.current_balance:.2f}")
            
            # Test market data access
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            logger.info(f"✅ Market data access verified: BTC=${current_price:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Binance connection verification failed: {e}")
            return False
    
    def execute_manual_test_trade(self):
        """Execute a manual test trade to verify system"""
        try:
            logger.info("🚀 EXECUTING MANUAL TEST TRADE")
            logger.info("="*60)
            
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate test position size for $5 risk
            position_size_usdt = self.test_risk_amount / self.stop_loss_percent  # $5000 position
            btc_quantity = position_size_usdt / current_price
            
            # For test, we'll use a BUY order
            action = 'BUY'
            entry_price = current_price
            stop_loss_price = entry_price * (1 - self.stop_loss_percent)  # -0.1%
            take_profit_price = entry_price * (1 + self.take_profit_percent)  # +0.25%
            
            logger.info(f"📊 TEST TRADE PARAMETERS:")
            logger.info(f"   Action: {action}")
            logger.info(f"   Entry Price: ${entry_price:.2f}")
            logger.info(f"   Stop Loss: ${stop_loss_price:.2f} (-0.1%)")
            logger.info(f"   Take Profit: ${take_profit_price:.2f} (+0.25%)")
            logger.info(f"   Position Size: ${position_size_usdt:.2f}")
            logger.info(f"   BTC Quantity: {btc_quantity:.6f}")
            logger.info(f"   Risk Amount: ${self.test_risk_amount:.2f}")
            
            # Send Telegram notification
            self.send_test_notification("STARTING")
            
            # Execute BUY market order
            logger.info("🔄 Placing BUY market order...")
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=round(btc_quantity, 6)
            )
            
            logger.info(f"✅ BUY order executed: {buy_order['orderId']}")
            
            # Get actual fill price
            order_details = self.binance.client.get_order(
                symbol='BTCUSDT',
                orderId=buy_order['orderId']
            )
            
            actual_entry_price = float(order_details['price']) if order_details['price'] != '0.00000000' else current_price
            actual_quantity = float(order_details['executedQty'])
            
            # Recalculate SL and TP based on actual entry
            actual_stop_loss = actual_entry_price * (1 - self.stop_loss_percent)
            actual_take_profit = actual_entry_price * (1 + self.take_profit_percent)
            
            logger.info(f"📊 ACTUAL EXECUTION:")
            logger.info(f"   Entry Price: ${actual_entry_price:.2f}")
            logger.info(f"   Quantity: {actual_quantity:.6f} BTC")
            logger.info(f"   Stop Loss: ${actual_stop_loss:.2f}")
            logger.info(f"   Take Profit: ${actual_take_profit:.2f}")
            
            # Place OCO order (Take Profit + Stop Loss)
            logger.info("🔄 Placing OCO order (TP + SL)...")
            oco_order = self.binance.client.create_oco_order(
                symbol='BTCUSDT',
                side='SELL',
                quantity=round(actual_quantity, 6),
                price=f"{actual_take_profit:.2f}",
                stopPrice=f"{actual_stop_loss:.2f}",
                stopLimitPrice=f"{actual_stop_loss * 0.999:.2f}",
                stopLimitTimeInForce='GTC'
            )
            
            logger.info(f"✅ OCO order placed: {oco_order['orderListId']}")
            
            # Store test trade data
            self.test_trade_data = {
                'entry_time': datetime.now(),
                'entry_price': actual_entry_price,
                'stop_loss_price': actual_stop_loss,
                'take_profit_price': actual_take_profit,
                'quantity': actual_quantity,
                'entry_order_id': buy_order['orderId'],
                'oco_order_id': oco_order['orderListId'],
                'risk_amount': self.test_risk_amount
            }
            
            # Send execution notification
            self.send_test_notification("EXECUTED")
            
            logger.info("🚀 TEST TRADE EXECUTED SUCCESSFULLY")
            logger.info("⏳ Monitoring until TP or SL is hit...")
            
            # Monitor the trade
            result = self.monitor_test_trade()
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Manual test trade execution failed: {e}")
            self.send_test_notification("FAILED")
            return False
    
    def monitor_test_trade(self, max_wait_hours=4):
        """Monitor test trade until completion"""
        try:
            logger.info(f"🔄 Monitoring test trade (max {max_wait_hours} hours)...")
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < (max_wait_hours * 3600):
                # Check OCO order status
                oco_order_id = self.test_trade_data['oco_order_id']
                
                try:
                    oco_status = self.binance.client.get_order_list(orderListId=oco_order_id)
                    
                    if oco_status['listStatusType'] == 'ALL_DONE':
                        # Trade completed
                        for order in oco_status['orders']:
                            if order['status'] == 'FILLED':
                                exit_price = float(order['price'])
                                entry_price = self.test_trade_data['entry_price']
                                
                                # Determine result
                                if abs(exit_price - self.test_trade_data['take_profit_price']) < abs(exit_price - self.test_trade_data['stop_loss_price']):
                                    result = 'WIN'
                                    pnl = self.test_risk_amount * 2.5  # 2.5:1 RR
                                    logger.info("🎉 TEST TRADE WON - TAKE PROFIT HIT!")
                                else:
                                    result = 'LOSS'
                                    pnl = -self.test_risk_amount
                                    logger.info("📉 TEST TRADE LOST - STOP LOSS HIT")
                                
                                # Update test data
                                self.test_trade_data.update({
                                    'exit_time': datetime.now(),
                                    'exit_price': exit_price,
                                    'result': result,
                                    'pnl': pnl
                                })
                                
                                # Send completion notification
                                self.send_test_notification("COMPLETED")
                                
                                # Generate test results
                                self.generate_test_results()
                                
                                logger.info("✅ TEST TRADE MONITORING COMPLETED")
                                self.test_status = "COMPLETED"
                                return True
                                
                except Exception as e:
                    logger.warning(f"⚠️ Error checking OCO status: {e}")
                
                # Wait 30 seconds before next check
                time.sleep(30)
                
                # Log progress every 5 minutes
                elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                if elapsed_minutes % 5 == 0:
                    logger.info(f"⏳ Test trade monitoring: {elapsed_minutes:.0f} minutes elapsed")
            
            logger.warning(f"⚠️ Test trade monitoring timeout after {max_wait_hours} hours")
            return False
            
        except Exception as e:
            logger.error(f"❌ Test trade monitoring failed: {e}")
            return False
    
    def send_test_notification(self, event_type):
        """Send test trade notification via Telegram"""
        try:
            from telegram_trading_bot import ComprehensiveTelegramTradingBot
            bot = ComprehensiveTelegramTradingBot()
            
            if event_type == "STARTING":
                message = """
🧪 <b>MANUAL TEST TRADE STARTING</b>

🔄 <b>SYSTEM VERIFICATION:</b>
• Binance Connection: ✅ VERIFIED
• Isolated Margin: ✅ ACTIVE
• Market Data: ✅ ACCESSIBLE
• Order Execution: 🔄 TESTING

⏳ <b>Executing test trade...</b>
"""
            
            elif event_type == "EXECUTED":
                data = self.test_trade_data
                message = f"""
🧪 <b>TEST TRADE EXECUTED</b>

📊 <b>TRADE DETAILS:</b>
• Action: BUY
• Entry: ${data['entry_price']:.2f}
• Stop Loss: ${data['stop_loss_price']:.2f} (-0.1%)
• Take Profit: ${data['take_profit_price']:.2f} (+0.25%)
• Quantity: {data['quantity']:.6f} BTC
• Risk: ${data['risk_amount']:.2f}

⏳ <b>MONITORING UNTIL TP/SL HIT...</b>
Risk-Reward: 2.5:1 (Proven Parameters)
"""
            
            elif event_type == "COMPLETED":
                data = self.test_trade_data
                result_emoji = "🎉" if data['result'] == 'WIN' else "📉"
                message = f"""
{result_emoji} <b>TEST TRADE COMPLETED</b>

📊 <b>RESULT: {data['result']}</b>
• Entry: ${data['entry_price']:.2f}
• Exit: ${data['exit_price']:.2f}
• P&L: ${data['pnl']:.2f}
• Duration: {(data['exit_time'] - data['entry_time']).total_seconds()/60:.0f} minutes

✅ <b>SYSTEM VALIDATION COMPLETE:</b>
• Binance Connection: ✅ WORKING
• Order Execution: ✅ PERFECT
• TP/SL Functionality: ✅ CONFIRMED
• Risk Management: ✅ VALIDATED

🚀 <b>SYSTEM READY FOR LIVE TRADING!</b>
"""
            
            elif event_type == "FAILED":
                message = """
❌ <b>TEST TRADE FAILED</b>

🔧 <b>SYSTEM CHECK REQUIRED:</b>
• Check Binance connection
• Verify API permissions
• Review account balance
• Check network connectivity

⚠️ <b>LIVE TRADING BLOCKED UNTIL TEST PASSES</b>
"""
            
            bot.send_message(message)
            logger.info(f"✅ Test notification sent: {event_type}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send test notification: {e}")
    
    def generate_test_results(self):
        """Generate comprehensive test results"""
        try:
            results = {
                'test_type': 'Manual System Verification Test',
                'test_status': self.test_status,
                'binance_connection': 'VERIFIED',
                'isolated_margin_access': 'VERIFIED',
                'order_execution': 'VERIFIED',
                'tp_sl_functionality': 'VERIFIED',
                'trade_data': self.test_trade_data,
                'system_ready_for_live': True,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save results
            with open('manual_test_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info("✅ Test results saved to manual_test_results.json")
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to generate test results: {e}")
            return None
    
    def run_complete_test(self):
        """Run complete manual test"""
        try:
            logger.info("🧪 STARTING COMPLETE MANUAL TEST")
            logger.info("📊 Verifying system before live deployment")
            logger.info("="*80)
            
            # Step 1: Verify Binance connection
            if not self.verify_binance_connection():
                logger.error("❌ Binance connection verification failed")
                return False
            
            # Step 2: Execute test trade
            if not self.execute_manual_test_trade():
                logger.error("❌ Manual test trade failed")
                return False
            
            # Success!
            logger.info("🎉 MANUAL TEST COMPLETED SUCCESSFULLY!")
            logger.info("✅ All systems verified and operational")
            logger.info("🚀 SYSTEM IS READY FOR LIVE DEPLOYMENT!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Complete manual test failed: {e}")
            return False

def main():
    """Main execution"""
    print("🧪 MANUAL TEST TRADE EXECUTION")
    print("✅ System verification before live deployment")
    print("📊 Testing: Connection, Orders, TP/SL, Risk Management")
    print("🎯 Using proven parameters: SL=0.1%, TP=0.25%, RR=2.5:1")
    print("="*80)
    
    try:
        # Initialize manual test
        test = ManualTestTrade()
        
        # Run complete test
        if test.run_complete_test():
            print("\n🎉 MANUAL TEST COMPLETED SUCCESSFULLY!")
            print("✅ All systems verified and operational")
            print("🚀 SYSTEM IS READY FOR LIVE DEPLOYMENT!")
            
            if hasattr(test, 'test_trade_data'):
                result = test.test_trade_data.get('result', 'Unknown')
                pnl = test.test_trade_data.get('pnl', 0)
                print(f"\n📊 TEST RESULTS:")
                print(f"   Result: {result}")
                print(f"   P&L: ${pnl:.2f}")
                print(f"   System Status: FULLY OPERATIONAL")
        else:
            print("\n❌ MANUAL TEST FAILED")
            print("System is NOT ready for live deployment")
            print("Check manual_test_trade.log for details")
            
    except Exception as e:
        print(f"\n🚨 TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
