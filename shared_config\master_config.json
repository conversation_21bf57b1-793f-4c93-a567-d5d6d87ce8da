{"system_info": {"name": "Enhanced Grid-Aware TCN-CNN-PPO Trading System", "version": "1.0.0", "master_document_version": "1.0.0", "compliance_level": "100%"}, "grid_system": {"grid_spacing": 0.0025, "grid_only_trading": true, "execution_method": "LIMIT_ORDERS_EXACT_GRID", "note": "Grid tolerance removed - limit orders execute at exact calculated grid levels automatically."}, "performance_targets": {"win_rate_target": 0.6, "trades_per_day_target": 8.0, "composite_score_target": 0.8, "confidence_threshold": 0.75}, "risk_management": {"risk_per_trade": 0.01, "risk_reward_ratio": 2.5, "max_daily_trades": 8, "max_daily_risk": 0.08, "max_drawdown": 0.1}, "model_architecture": {"tcn_features": 64, "cnn_features": 64, "grid_features": 7, "total_features": 135, "signal_persistence": 300}, "trading_config": {"symbol": "BTCUSDT", "margin_type": "ISOLATED", "leverage": 10, "interval": "30m"}, "security_settings": {"pre_execution_scan_required": true, "code_integrity_check_required": true, "authorization_required": true, "emergency_protocols_enabled": true}, "system_ports": {"signal_generator": 5000, "trading_engine": 5001}, "modules": {"binance_connector": {"enabled": true, "path": "01_binance_connector", "main_file": "binance_real_money_connector.py"}, "signal_generator": {"enabled": true, "path": "02_signal_generator", "main_file": "enhanced_grid_aware_signal_generator.py"}, "compliance_system": {"enabled": true, "path": "03_compliance_system", "main_file": "guardrails_compliance_check.py"}, "security_system": {"enabled": true, "path": "04_security_system", "main_file": "protected_core_system.py"}, "trading_engine": {"enabled": true, "path": "05_trading_engine", "main_file": "automated_trading_engine.py"}, "telegram_system": {"enabled": true, "path": "06_telegram_system", "main_file": "telegram_trading_bot.py"}, "performance_system": {"enabled": true, "path": "07_performance_system", "main_file": "enhanced_performance_metrics.py"}, "logging_system": {"enabled": true, "path": "08_logging_system", "main_file": "system_logger.py"}}, "logging": {"log_level": "INFO", "log_rotation": true, "max_log_size": "10MB", "backup_count": 5, "logs_directory": "08_logging_system/logs"}, "compliance": {"check_interval": 60, "violation_threshold": 3, "monitoring_enabled": true}, "performance_monitoring": {"update_interval": 30, "tracking_enabled": true, "real_time_monitoring": true}}