#!/usr/bin/env python3
"""
Final Conservative Leveraged Trade
Ultra-conservative position sizing to ensure execution
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalConservativeTrade:
    """Final conservative leveraged trade execution"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.trade_active = False
        self.tcn_active = False
        
    def initialize_system(self):
        """Initialize system"""
        try:
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    def execute_final_conservative_trade(self):
        """Execute final conservative trade"""
        try:
            logger.info("⚡ Executing final conservative leveraged trade...")
            
            # Get current price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Check isolated margin balance
            isolated_account = self.binance.client.get_isolated_margin_account()
            
            margin_usdt = 0
            for asset in isolated_account['assets']:
                if asset['symbol'] == 'BTCUSDT':
                    margin_usdt = float(asset['quoteAsset']['free'])
                    break
            
            logger.info(f"💰 Available margin: ${margin_usdt:.2f}")
            logger.info(f"📈 Current BTC price: ${current_price:.2f}")
            
            # ULTRA-CONSERVATIVE position sizing
            # Use only $10 worth of BTC (very small position)
            target_position_value = 10.0  # $10 position
            position_size_btc = target_position_value / current_price
            
            # Round to valid step size
            position_size_btc = round(position_size_btc, 5)
            
            # Ensure minimum
            if position_size_btc < 0.00001:
                position_size_btc = 0.00001
            
            # Calculate actual position value
            actual_position_value = position_size_btc * current_price
            
            # Calculate risk/reward with 0.1% SL
            sl_percentage = 0.001  # 0.1%
            risk_amount = position_size_btc * current_price * sl_percentage
            reward_amount = risk_amount * 2.5
            
            # Calculate price levels
            entry_price = current_price
            stop_loss_price = current_price * (1 - sl_percentage)
            take_profit_price = current_price * (1 + (sl_percentage * 2.5))
            
            # Send notification
            if self.telegram:
                trade_message = f"""
⚡ **FINAL CONSERVATIVE LEVERAGED TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Margin Available:** ${margin_usdt:.2f}
🎯 **Signal:** BUY (Conservative)
💰 **Entry:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position:** {position_size_btc:.5f} BTC
💵 **Value:** ${actual_position_value:.2f}
⚡ **Leverage:** 10x isolated margin
🔴 **Risk:** ${risk_amount:.2f}
🟢 **Reward:** ${reward_amount:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing conservative trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute ultra-conservative market buy
            try:
                logger.info(f"📊 Placing conservative BUY order for {position_size_btc:.5f} BTC")
                
                market_order = self.binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='BUY',
                    type='MARKET',
                    quantity=f"{position_size_btc:.5f}",
                    isIsolated='TRUE'
                )
                
                if market_order:
                    logger.info(f"✅ Conservative market order executed: {market_order.get('orderId')}")
                    
                    # Send success notification
                    if self.telegram:
                        success_message = f"""
✅ **CONSERVATIVE TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Order ID:** {market_order.get('orderId')}
⚡ **Leverage:** 10x isolated margin
🎯 **Position:** {position_size_btc:.5f} BTC
💰 **Entry:** ${entry_price:.2f}
🔴 **Risk:** ${risk_amount:.2f}
🟢 **Reward:** ${reward_amount:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **TEST CYCLE COMPLETE**
🚀 **ACTIVATING TCN-CNN-PPO SYSTEM**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                        self.telegram.send_message(success_message)
                    
                    # Activate TCN-CNN-PPO system immediately
                    self.activate_tcn_system()
                    return True
                else:
                    logger.error("❌ Conservative market order failed")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Conservative order execution error: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Final conservative trade error: {e}")
            return False
    
    def activate_tcn_system(self):
        """Activate TCN-CNN-PPO system"""
        try:
            logger.info("🚀 Activating TCN-CNN-PPO system...")
            self.tcn_active = True
            
            if self.telegram:
                activation_message = f"""
🚀 **TCN-CNN-PPO SYSTEM ACTIVATED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **Test Trade:** Conservative leveraged trade successful
🧠 **AI Model:** TCN-CNN-PPO Ensemble (135 features)
⚡ **Leverage:** 10x isolated margin ready
📊 **Grid Aware:** 0.25% spacing compliance
📈 **Real-Time:** RSI, VWAP, Market sentiment
⚖️ **Risk Management:** Dynamic position sizing
🔄 **Compounding:** Enabled with leverage
🎯 **Target:** 8 trades/day, 60% win rate
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **LIVE LEVERAGED TRADING SYSTEM ACTIVE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(activation_message)
            
            # Start TCN monitoring
            self.start_tcn_monitoring()
            
        except Exception as e:
            logger.error(f"❌ TCN activation error: {e}")
    
    def start_tcn_monitoring(self):
        """Start TCN-CNN-PPO monitoring with leverage support"""
        try:
            logger.info("🧠 Starting TCN-CNN-PPO leveraged monitoring...")
            
            check_count = 0
            while self.tcn_active:
                try:
                    check_count += 1
                    logger.info(f"🧠 TCN Leveraged Analysis #{check_count}")
                    
                    # Generate TCN-CNN-PPO signal
                    signal_data = self.signal_generator.generate_signal()
                    
                    # Get market data
                    market_data = self.binance.get_market_data()
                    if market_data is not None:
                        current_price = market_data['close'].iloc[-1]
                        current_rsi = market_data['rsi'].iloc[-1]
                        current_vwap = market_data['vwap'].iloc[-1]
                        
                        # Market sentiment analysis
                        if current_rsi < 25:
                            sentiment = "🔴 EXTREMELY OVERSOLD"
                        elif current_rsi < 30:
                            sentiment = "🟠 OVERSOLD"
                        elif current_rsi > 75:
                            sentiment = "🟢 EXTREMELY OVERBOUGHT"
                        elif current_rsi > 70:
                            sentiment = "🟡 OVERBOUGHT"
                        elif current_price > current_vwap * 1.002:
                            sentiment = "🔵 STRONG BULLISH"
                        elif current_price > current_vwap:
                            sentiment = "🔵 BULLISH"
                        elif current_price < current_vwap * 0.998:
                            sentiment = "🟠 STRONG BEARISH"
                        else:
                            sentiment = "🟠 BEARISH"
                        
                        # Grid analysis
                        base_price = 100000
                        grid_spacing = 0.0025
                        grid_level = int((current_price - base_price) / (base_price * grid_spacing))
                        grid_level_str = f"Level {grid_level:+d}"
                        
                        # Confidence analysis
                        confidence = signal_data.get('confidence', 0)
                        if confidence > 0.85:
                            confidence_status = "🟢 VERY HIGH"
                        elif confidence > 0.75:
                            confidence_status = "🟢 HIGH"
                        elif confidence > 0.6:
                            confidence_status = "🟡 MEDIUM"
                        elif confidence > 0.4:
                            confidence_status = "🟠 LOW"
                        else:
                            confidence_status = "🔴 VERY LOW"
                        
                        # Grid compliance
                        grid_status = "✅ COMPLIANT" if signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL' else "⚠️ WAITING"
                        
                        # Send comprehensive monitoring update every 2 checks
                        if check_count % 2 == 0 and self.telegram:
                            monitoring_message = f"""
🧠 **TCN-CNN-PPO LEVERAGED MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.3f} ({confidence_status})
📈 **Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
📈 **VWAP:** ${current_vwap:.2f}
{sentiment}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔲 **Grid Level:** {grid_level_str}
📍 **Grid Status:** {grid_status}
⚡ **Leverage:** 10x isolated margin ready
🧠 **Model:** Active (135 features)
🔍 **Analysis:** #{check_count}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: LEVERAGED TCN SYSTEM ACTIVE**
"""
                            self.telegram.send_message(monitoring_message)
                        
                        # Execute leveraged trade if optimal conditions
                        if (signal_data['signal'] != 'HOLD' and 
                            confidence > 0.8 and
                            signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL'):
                            
                            logger.info(f"🧠 Optimal leveraged TCN signal: {signal_data['signal']} (confidence: {confidence:.3f})")
                            
                            if self.telegram:
                                trade_alert = f"""
🚨 **OPTIMAL LEVERAGED TCN SIGNAL**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.3f} ({confidence_status})
📈 **Price:** ${current_price:.2f}
{sentiment}
📍 **Grid:** {grid_status}
⚡ **Leverage:** 10x isolated margin
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚡ **READY FOR LEVERAGED EXECUTION**
"""
                                self.telegram.send_message(trade_alert)
                    
                    time.sleep(180)  # 3 minutes between comprehensive analyses
                    
                except KeyboardInterrupt:
                    logger.info("🛑 TCN leveraged monitoring stopped")
                    break
                except Exception as e:
                    logger.error(f"❌ TCN leveraged monitoring error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ TCN leveraged monitoring failed: {e}")

def main():
    """Main execution"""
    print("⚡ FINAL CONSERVATIVE LEVERAGED TRADE")
    print("=" * 60)
    print("📋 Ultra-conservative position sizing")
    print("📋 10x leverage with $10 position")
    print("📋 Immediate TCN-CNN-PPO activation")
    print("=" * 60)
    
    trade = FinalConservativeTrade()
    
    if not trade.initialize_system():
        print("❌ System initialization failed")
        return
    
    print("⚡ Executing final conservative leveraged trade...")
    if trade.execute_final_conservative_trade():
        print("✅ Conservative trade executed successfully!")
        print("🚀 TCN-CNN-PPO system activated with leverage support")
        print("📱 Monitor Telegram for comprehensive analysis updates")
        
        try:
            while trade.tcn_active:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 System stopped")
            trade.tcn_active = False
    else:
        print("❌ Conservative trade execution failed")

if __name__ == "__main__":
    main()
