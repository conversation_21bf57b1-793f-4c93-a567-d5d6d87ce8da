#!/usr/bin/env python3
"""
Test Optimized Master Document Compliant Model
Test the hyperparameter-optimized model for improved performance
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import logging
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedGridAwareTCNCNNPPOEnsemble(nn.Module):
    """Optimized Grid-Aware TCN-CNN-PPO Ensemble (matches optimized model)"""
    
    def __init__(self, config):
        super(OptimizedGridAwareTCNCNNPPOEnsemble, self).__init__()
        
        # Hyperparameters from config
        self.tcn_hidden = config['tcn_hidden_dim']
        self.cnn_hidden = config['cnn_hidden_dim']
        self.tcn_features = config['tcn_features']
        self.cnn_features = config['cnn_features']
        self.dropout_rate = config['dropout_rate']
        self.tcn_kernel = config['tcn_kernel_size']
        self.cnn_kernel = config['cnn_kernel_size']
        
        # TCN Component (Optimized)
        self.tcn = nn.Sequential(
            nn.Conv1d(7, self.tcn_hidden, self.tcn_kernel, padding=self.tcn_kernel//2),
            nn.ReLU(),
            nn.Dropout(self.dropout_rate),
            nn.Conv1d(self.tcn_hidden, self.tcn_hidden, 3, padding=1),
            nn.ReLU(),
            nn.Dropout(self.dropout_rate),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(self.tcn_hidden, self.tcn_features)
        )
        
        # CNN Component (Optimized)
        self.cnn = nn.Sequential(
            nn.Conv1d(7, self.cnn_hidden, self.cnn_kernel, padding=self.cnn_kernel//2),
            nn.ReLU(),
            nn.Dropout(self.dropout_rate),
            nn.Conv1d(self.cnn_hidden, self.cnn_hidden, 3, padding=1),
            nn.ReLU(),
            nn.Dropout(self.dropout_rate),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(self.cnn_hidden, self.cnn_features)
        )
        
        # PPO Component (Optimized)
        ppo_input_size = self.tcn_features + self.cnn_features + 7  # Grid features
        self.ppo_actor = nn.Sequential(
            nn.Linear(ppo_input_size, config['ppo_hidden_dim']),
            nn.ReLU(),
            nn.Dropout(config['ppo_dropout']),
            nn.Linear(config['ppo_hidden_dim'], config['ppo_hidden_dim']//2),
            nn.ReLU(),
            nn.Dropout(config['ppo_dropout']),
            nn.Linear(config['ppo_hidden_dim']//2, 3)  # BUY, SELL, HOLD
        )
        
        # Individual classifiers
        self.tcn_classifier = nn.Linear(self.tcn_features, 3)
        self.cnn_classifier = nn.Linear(self.cnn_features, 3)
        
        # Ensemble weights (learnable)
        self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
        
        logger.info(f"🏗️ Optimized Ensemble Loaded: TCN={self.tcn_features}, CNN={self.cnn_features}, Total={ppo_input_size}")
    
    def forward(self, x, grid_features):
        """Forward pass with optimized processing"""
        x_transposed = x.transpose(1, 2)
        
        # Component processing
        tcn_features = self.tcn(x_transposed)
        cnn_features = self.cnn(x_transposed)
        
        # PPO state vector
        ppo_state = torch.cat([tcn_features, cnn_features, grid_features], dim=1)
        
        # Individual predictions
        tcn_pred = torch.softmax(self.tcn_classifier(tcn_features), dim=1)
        cnn_pred = torch.softmax(self.cnn_classifier(cnn_features), dim=1)
        ppo_pred = torch.softmax(self.ppo_actor(ppo_state), dim=1)
        
        # Ensemble combination
        weights = torch.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = weights[0] * tcn_pred + weights[1] * cnn_pred + weights[2] * ppo_pred
        
        return ensemble_pred, {
            'tcn_pred': tcn_pred,
            'cnn_pred': cnn_pred,
            'ppo_pred': ppo_pred,
            'weights': weights,
            'confidence': torch.max(ensemble_pred, dim=1)[0]
        }

class OptimizedModelTester:
    """Test the optimized model"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # EXACT MASTER DOCUMENT PARAMETERS
        self.grid_spacing = 0.0025          # EXACTLY 0.25%
        self.grid_tolerance = 0.001         # EXACTLY 0.1%
        self.sequence_length = 30           # Match training
        
        logger.info("🔍 Optimized Model Tester Initialized")
        logger.info(f"🖥️  Device: {self.device}")
    
    def load_test_data(self):
        """Load test data"""
        try:
            logger.info("📊 Loading test Bitcoin data...")
            
            # Load recent data for testing
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').tail(200).reset_index(drop=True)
            
            # Add ATR indicator
            df = self.add_real_atr_indicator(df)
            
            logger.info(f"📊 Test data loaded: {len(df)} samples")
            return df
            
        except Exception as e:
            logger.error(f"❌ Test data loading failed: {e}")
            return None
    
    def add_real_atr_indicator(self, df):
        """Add REAL ATR indicator"""
        try:
            tr_list = []
            for i in range(len(df)):
                if i == 0:
                    tr = df.iloc[i]['high'] - df.iloc[i]['low']
                else:
                    tr1 = df.iloc[i]['high'] - df.iloc[i]['low']
                    tr2 = abs(df.iloc[i]['high'] - df.iloc[i-1]['close'])
                    tr3 = abs(df.iloc[i]['low'] - df.iloc[i-1]['close'])
                    tr = max(tr1, tr2, tr3)
                tr_list.append(tr)
            
            df['atr'] = pd.Series(tr_list).rolling(14, min_periods=1).mean().fillna(0)
            return df
            
        except Exception as e:
            logger.error(f"❌ ATR calculation failed: {e}")
            return df
    
    def calculate_grid_levels(self, price):
        """Calculate grid levels per master document"""
        base_level = round(price / (1 + self.grid_spacing)) * (1 + self.grid_spacing)
        grid_level = base_level
        grid_distance = abs(price - grid_level) / grid_level
        
        return grid_level, grid_distance
    
    def prepare_input(self, data, index):
        """Prepare model input"""
        try:
            if index < self.sequence_length:
                return None, None
            
            # Market data sequence
            sequence = data.iloc[index-self.sequence_length:index][
                ['open', 'high', 'low', 'close', 'rsi', 'vwap', 'atr']
            ].values
            
            if np.any(np.isnan(sequence)) or np.any(np.isinf(sequence)):
                return None, None
            
            # Grid features (7 features per master document)
            current_row = data.iloc[index]
            current_price = float(current_row['close'])
            grid_level, grid_distance = self.calculate_grid_levels(current_price)
            
            # EXACT MASTER DOCUMENT GRID FEATURES
            grid_features = [
                grid_level,                                    # Current grid level
                grid_distance,                                 # Distance to grid
                self.grid_tolerance,                          # Grid tolerance
                grid_level * (1 + self.grid_spacing),        # Next grid up
                grid_level * (1 - self.grid_spacing),        # Next grid down
                self.grid_spacing,                            # Grid spacing (0.25%)
                1.0 if grid_distance <= self.grid_tolerance else 0.0  # Grid compliance
            ]
            
            X = torch.FloatTensor(sequence).unsqueeze(0)
            grid_tensor = torch.FloatTensor(grid_features).unsqueeze(0)
            
            return X, grid_tensor
            
        except Exception as e:
            return None, None
    
    def test_optimized_model(self):
        """Test the optimized model with multiple confidence thresholds"""
        logger.info("🚀 Testing Optimized Master Document Compliant Model")
        logger.info("="*80)
        
        # Load test data
        data = self.load_test_data()
        if data is None:
            logger.error("❌ Test data loading failed")
            return None
        
        # Load optimized model
        try:
            logger.info("🔍 Loading optimized model...")
            
            checkpoint = torch.load('optimized_master_compliant_model.pth', map_location=self.device, weights_only=False)
            config = checkpoint['best_config']
            
            model = OptimizedGridAwareTCNCNNPPOEnsemble(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            logger.info("✅ Optimized model loaded successfully")
            logger.info(f"📊 Model info: {checkpoint.get('optimization_type', 'Unknown')}")
            logger.info(f"🎯 Training win rate: {checkpoint.get('win_rate', 'Unknown'):.1f}%")
            logger.info(f"🎯 Training confidence: {checkpoint.get('avg_confidence', 'Unknown'):.3f}")
            
        except Exception as e:
            logger.error(f"❌ Optimized model loading failed: {e}")
            return None
        
        # Test with multiple confidence thresholds
        confidence_thresholds = [0.30, 0.40, 0.50, 0.60, 0.70]
        threshold_results = {}
        
        # Generate signals for all test samples
        all_signals = []
        
        logger.info("🔄 Generating signals for all test samples...")
        
        for i in range(self.sequence_length, min(self.sequence_length + 50, len(data))):
            # Prepare input
            X, grid_tensor = self.prepare_input(data, i)
            if X is None or grid_tensor is None:
                continue
            
            # Get prediction
            X, grid_tensor = X.to(self.device), grid_tensor.to(self.device)
            
            with torch.no_grad():
                prediction, components = model(X, grid_tensor)
                probabilities = torch.softmax(prediction, dim=1)[0]
                signal = torch.argmax(prediction, dim=1).item()
                confidence = torch.max(probabilities).item()
            
            signal_record = {
                'index': i,
                'price': float(data.iloc[i]['close']),
                'signal': signal,
                'signal_name': ['BUY', 'SELL', 'HOLD'][signal],
                'confidence': confidence,
                'probabilities': {
                    'BUY': float(probabilities[0]),
                    'SELL': float(probabilities[1]),
                    'HOLD': float(probabilities[2])
                },
                'components': {
                    'tcn_weight': components['weights'][0].item(),
                    'cnn_weight': components['weights'][1].item(),
                    'ppo_weight': components['weights'][2].item()
                }
            }
            
            all_signals.append(signal_record)
        
        # Analyze results for each confidence threshold
        for threshold in confidence_thresholds:
            qualified_signals = [s for s in all_signals if s['confidence'] >= threshold]
            
            total_signals = len(all_signals)
            buy_signals = sum(1 for s in qualified_signals if s['signal'] == 0)
            sell_signals = sum(1 for s in qualified_signals if s['signal'] == 1)
            hold_signals = sum(1 for s in qualified_signals if s['signal'] == 2)
            qualified_count = len(qualified_signals)
            
            avg_confidence = np.mean([s['confidence'] for s in qualified_signals]) if qualified_signals else 0
            
            threshold_results[threshold] = {
                'total_signals': total_signals,
                'qualified_signals': qualified_count,
                'qualification_rate': (qualified_count / total_signals * 100) if total_signals > 0 else 0,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'hold_signals': hold_signals,
                'avg_confidence': avg_confidence,
                'signal_distribution': {
                    'BUY': (buy_signals / qualified_count * 100) if qualified_count > 0 else 0,
                    'SELL': (sell_signals / qualified_count * 100) if qualified_count > 0 else 0,
                    'HOLD': (hold_signals / qualified_count * 100) if qualified_count > 0 else 0
                }
            }
        
        # Generate comprehensive analysis
        logger.info(f"\n📊 OPTIMIZED MODEL SIGNAL ANALYSIS:")
        logger.info(f"   Total Test Samples: {len(all_signals)}")
        
        overall_avg_confidence = np.mean([s['confidence'] for s in all_signals])
        logger.info(f"   Overall Average Confidence: {overall_avg_confidence:.3f}")
        
        logger.info(f"\n🎯 CONFIDENCE THRESHOLD ANALYSIS:")
        for threshold, results in threshold_results.items():
            logger.info(f"   Threshold {threshold:.0%}:")
            logger.info(f"     Qualified Signals: {results['qualified_signals']}/{results['total_signals']} ({results['qualification_rate']:.1f}%)")
            logger.info(f"     Signal Mix: BUY={results['signal_distribution']['BUY']:.1f}%, SELL={results['signal_distribution']['SELL']:.1f}%, HOLD={results['signal_distribution']['HOLD']:.1f}%")
            logger.info(f"     Avg Confidence: {results['avg_confidence']:.3f}")
        
        # Save comprehensive results
        test_results = {
            'test_type': 'Optimized Master Document Compliant Model Test',
            'model_file': 'optimized_master_compliant_model.pth',
            'model_config': config,
            'overall_avg_confidence': overall_avg_confidence,
            'threshold_analysis': threshold_results,
            'all_signals': all_signals,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('optimized_model_test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
        
        logger.info("💾 Test results saved to: optimized_model_test_results.json")
        
        return test_results

def main():
    """Main testing execution"""
    print("🔍 OPTIMIZED MASTER DOCUMENT COMPLIANT MODEL TEST")
    print("✅ Testing Hyperparameter-Optimized Model")
    print("🎯 Multi-Threshold Signal Analysis")
    print("📊 Performance Comparison")
    print("="*80)
    
    try:
        # Initialize tester
        tester = OptimizedModelTester()
        
        # Test optimized model
        results = tester.test_optimized_model()
        
        if results:
            print("\n🎉 OPTIMIZED MODEL TEST COMPLETED!")
            print(f"📊 Overall Confidence: {results['overall_avg_confidence']:.3f}")
            
            # Show best threshold results
            best_threshold = None
            best_qualified = 0
            
            for threshold, data in results['threshold_analysis'].items():
                if data['qualified_signals'] > best_qualified:
                    best_qualified = data['qualified_signals']
                    best_threshold = threshold
            
            if best_threshold:
                best_data = results['threshold_analysis'][best_threshold]
                print(f"🏆 Best Threshold: {best_threshold:.0%}")
                print(f"📈 Qualified Signals: {best_data['qualified_signals']}")
                print(f"🎯 Average Confidence: {best_data['avg_confidence']:.3f}")
            
            print("📊 Check optimized_model_test_results.json for details")
        else:
            print("\n❌ Optimized model test failed")
            
    except Exception as e:
        print(f"\n🚨 TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
