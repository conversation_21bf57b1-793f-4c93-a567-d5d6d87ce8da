#!/usr/bin/env python3
"""
COMPOUNDING MONEY MANAGEMENT ENGINE
Manages position sizing with compounding for 1% SL and 2.5% TP
Starting balance equivalent: $100
"""

import sys
import json
import logging
from datetime import datetime
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP

sys.path.append('01_binance_connector')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('compounding_money_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompoundingMoneyManager:
    """Advanced money management with compounding"""
    
    def __init__(self):
        # COMPOUNDING PARAMETERS
        self.starting_balance_equivalent = 100.0  # $100 equivalent
        self.risk_percent = 0.01                  # 1% risk per trade
        self.reward_percent = 0.025               # 2.5% reward per trade
        self.risk_reward_ratio = 2.5              # 2.5:1 RR
        
        # COMPOUNDING SETTINGS
        self.compounding_enabled = True
        self.min_balance_threshold = 50.0         # Minimum $50 equivalent
        self.max_position_percent = 0.8           # Max 80% of balance per trade
        
        # TRADE TRACKING
        self.trade_history = []
        self.current_balance = None
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        logger.info("COMPOUNDING MONEY MANAGEMENT ENGINE INITIALIZED")
        logger.info(f"Starting Balance Equivalent: ${self.starting_balance_equivalent:.2f}")
        logger.info(f"Risk per Trade: {self.risk_percent:.1%} (${self.starting_balance_equivalent * self.risk_percent:.2f})")
        logger.info(f"Reward per Trade: {self.reward_percent:.1%} (${self.starting_balance_equivalent * self.reward_percent:.2f})")
        logger.info(f"Risk-Reward Ratio: {self.risk_reward_ratio:.1f}:1")
        logger.info(f"Compounding: {'ENABLED' if self.compounding_enabled else 'DISABLED'}")
    
    def initialize_balance_tracking(self):
        """Initialize balance tracking with current account status"""
        try:
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Get current balance
            balance_info = self.binance.get_isolated_margin_balance()
            self.current_balance = balance_info['total_usdt_value']
            
            logger.info("BALANCE TRACKING INITIALIZED")
            logger.info(f"Current Account Balance: ${self.current_balance:.2f}")
            logger.info(f"Starting Balance Equivalent: ${self.starting_balance_equivalent:.2f}")
            
            # Calculate scaling factor if needed
            if self.current_balance < self.starting_balance_equivalent:
                self.balance_scale_factor = self.current_balance / self.starting_balance_equivalent
                logger.info(f"Balance Scale Factor: {self.balance_scale_factor:.3f}")
                logger.info(f"Scaled Risk Amount: ${self.current_balance * self.risk_percent:.2f}")
                logger.info(f"Scaled Reward Amount: ${self.current_balance * self.reward_percent:.2f}")
            else:
                self.balance_scale_factor = 1.0
                logger.info("Using full $100 equivalent parameters")
            
            return True
            
        except Exception as e:
            logger.error(f"Balance tracking initialization failed: {e}")
            return False
    
    def calculate_position_size(self, current_price=None):
        """Calculate optimal position size with compounding"""
        try:
            if not current_price:
                ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
            
            # Get current balance for compounding
            balance_info = self.binance.get_isolated_margin_balance()
            current_balance = balance_info['total_usdt_value']
            
            # Update balance tracking
            self.current_balance = current_balance
            
            # Calculate risk amount based on current balance (compounding)
            if self.compounding_enabled:
                risk_amount = current_balance * self.risk_percent
                reward_amount = current_balance * self.reward_percent
                base_balance = current_balance
            else:
                risk_amount = self.starting_balance_equivalent * self.risk_percent
                reward_amount = self.starting_balance_equivalent * self.reward_percent
                base_balance = self.starting_balance_equivalent
            
            # Calculate position size needed to achieve risk amount
            # Position Size = Risk Amount / Risk Percentage
            required_position_size = risk_amount / self.risk_percent
            
            # Ensure position doesn't exceed maximum percentage of balance
            max_position_size = current_balance * self.max_position_percent
            position_size = min(required_position_size, max_position_size)
            
            # Calculate BTC quantity
            btc_quantity = position_size / current_price
            
            # Get symbol requirements for proper formatting
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            step_size = None
            min_qty = None
            min_notional = None
            tick_size = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                    min_qty = float(filter_item['minQty'])
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    min_notional = float(filter_item['minNotional'])
                elif filter_item['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_item['tickSize'])
            
            # Round BTC quantity to step size
            Decimal.getcontext().rounding = ROUND_DOWN
            qty_decimal = Decimal(str(btc_quantity))
            step_decimal = Decimal(str(step_size))
            btc_quantity = float(qty_decimal.quantize(step_decimal))
            
            # Ensure minimum quantity and notional requirements
            btc_quantity = max(btc_quantity, min_qty)
            
            # Check minimum notional
            if btc_quantity * current_price < min_notional:
                btc_quantity = min_notional / current_price
                btc_quantity = float(Decimal(str(btc_quantity)).quantize(step_decimal, rounding=ROUND_HALF_UP))
            
            # Recalculate actual position size
            actual_position_size = btc_quantity * current_price
            
            # Calculate actual risk and reward amounts
            actual_risk_amount = actual_position_size * self.risk_percent
            actual_reward_amount = actual_position_size * self.reward_percent
            
            # Calculate SL and TP prices
            Decimal.getcontext().rounding = ROUND_HALF_UP
            price_decimal = Decimal(str(current_price))
            tick_decimal = Decimal(str(tick_size))
            
            entry_price = float(price_decimal.quantize(tick_decimal))
            stop_loss_price = float(Decimal(str(entry_price * (1 - self.risk_percent))).quantize(tick_decimal))
            take_profit_price = float(Decimal(str(entry_price * (1 + self.reward_percent))).quantize(tick_decimal))
            
            position_data = {
                'current_balance': current_balance,
                'base_balance': base_balance,
                'compounding_enabled': self.compounding_enabled,
                'entry_price': entry_price,
                'btc_quantity': btc_quantity,
                'position_size': actual_position_size,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'risk_amount': actual_risk_amount,
                'reward_amount': actual_reward_amount,
                'risk_percent': self.risk_percent,
                'reward_percent': self.reward_percent,
                'risk_reward_ratio': actual_reward_amount / actual_risk_amount,
                'balance_utilization': (actual_position_size / current_balance) * 100,
                'step_size': step_size,
                'tick_size': tick_size,
                'min_notional': min_notional
            }
            
            logger.info("POSITION SIZE CALCULATION COMPLETE")
            logger.info(f"Current Balance: ${current_balance:.2f}")
            logger.info(f"Compounding: {'ENABLED' if self.compounding_enabled else 'DISABLED'}")
            logger.info(f"Entry Price: ${entry_price:.2f}")
            logger.info(f"BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"Position Size: ${actual_position_size:.2f}")
            logger.info(f"Stop Loss: ${stop_loss_price:.2f} (${actual_risk_amount:.2f})")
            logger.info(f"Take Profit: ${take_profit_price:.2f} (${actual_reward_amount:.2f})")
            logger.info(f"Risk-Reward: {actual_reward_amount/actual_risk_amount:.1f}:1")
            logger.info(f"Balance Utilization: {(actual_position_size/current_balance)*100:.1f}%")
            
            return position_data
            
        except Exception as e:
            logger.error(f"Position size calculation failed: {e}")
            return None
    
    def validate_position_safety(self, position_data):
        """Validate position meets safety requirements"""
        try:
            current_balance = position_data['current_balance']
            position_size = position_data['position_size']
            risk_amount = position_data['risk_amount']
            
            # Safety checks
            safety_checks = {
                'sufficient_balance': current_balance >= self.min_balance_threshold,
                'position_size_ok': position_size <= (current_balance * self.max_position_percent),
                'risk_amount_reasonable': risk_amount <= (current_balance * 0.02),  # Max 2% risk
                'min_notional_met': position_size >= position_data['min_notional'],
                'risk_reward_ratio_ok': position_data['risk_reward_ratio'] >= 2.0
            }
            
            all_safe = all(safety_checks.values())
            
            logger.info("POSITION SAFETY VALIDATION")
            for check, result in safety_checks.items():
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"  {check}: {status}")
            
            if all_safe:
                logger.info("✅ POSITION SAFETY VALIDATION: PASSED")
            else:
                logger.warning("⚠️ POSITION SAFETY VALIDATION: FAILED")
            
            return all_safe, safety_checks
            
        except Exception as e:
            logger.error(f"Position safety validation failed: {e}")
            return False, {}
    
    def record_trade_result(self, trade_result):
        """Record trade result for compounding tracking"""
        try:
            self.total_trades += 1
            
            if trade_result['result'] == 'WIN':
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Update balance tracking
            balance_info = self.binance.get_isolated_margin_balance()
            new_balance = balance_info['total_usdt_value']
            balance_change = new_balance - self.current_balance
            
            trade_record = {
                'trade_number': self.total_trades,
                'timestamp': datetime.now().isoformat(),
                'result': trade_result['result'],
                'entry_price': trade_result.get('entry_price', 0),
                'exit_price': trade_result.get('exit_price', 0),
                'pnl': trade_result.get('pnl', 0),
                'balance_before': self.current_balance,
                'balance_after': new_balance,
                'balance_change': balance_change,
                'win_rate': (self.winning_trades / self.total_trades) * 100,
                'compounding_factor': new_balance / self.starting_balance_equivalent
            }
            
            self.trade_history.append(trade_record)
            self.current_balance = new_balance
            
            logger.info("TRADE RESULT RECORDED")
            logger.info(f"Trade #{self.total_trades}: {trade_result['result']}")
            logger.info(f"P&L: ${trade_result.get('pnl', 0):.2f}")
            logger.info(f"Balance: ${self.current_balance:.2f} → ${new_balance:.2f}")
            logger.info(f"Win Rate: {(self.winning_trades/self.total_trades)*100:.1f}%")
            logger.info(f"Compounding Factor: {new_balance/self.starting_balance_equivalent:.3f}x")
            
            # Save trade history
            self.save_trade_history()
            
            return trade_record
            
        except Exception as e:
            logger.error(f"Trade result recording failed: {e}")
            return None
    
    def save_trade_history(self):
        """Save trade history to file"""
        try:
            history_data = {
                'money_manager_config': {
                    'starting_balance_equivalent': self.starting_balance_equivalent,
                    'risk_percent': self.risk_percent,
                    'reward_percent': self.reward_percent,
                    'risk_reward_ratio': self.risk_reward_ratio,
                    'compounding_enabled': self.compounding_enabled
                },
                'current_status': {
                    'current_balance': self.current_balance,
                    'total_trades': self.total_trades,
                    'winning_trades': self.winning_trades,
                    'losing_trades': self.losing_trades,
                    'win_rate': (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                    'compounding_factor': self.current_balance / self.starting_balance_equivalent if self.current_balance else 1.0
                },
                'trade_history': self.trade_history,
                'last_updated': datetime.now().isoformat()
            }
            
            filename = f'compounding_trade_history_{datetime.now().strftime("%Y%m%d")}.json'
            with open(filename, 'w') as f:
                json.dump(history_data, f, indent=2, default=str)
            
            logger.info(f"Trade history saved to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save trade history: {e}")
    
    def get_money_management_status(self):
        """Get current money management status"""
        try:
            balance_info = self.binance.get_isolated_margin_balance()
            current_balance = balance_info['total_usdt_value']
            
            status = {
                'current_balance': current_balance,
                'starting_balance_equivalent': self.starting_balance_equivalent,
                'compounding_factor': current_balance / self.starting_balance_equivalent,
                'risk_per_trade': current_balance * self.risk_percent if self.compounding_enabled else self.starting_balance_equivalent * self.risk_percent,
                'reward_per_trade': current_balance * self.reward_percent if self.compounding_enabled else self.starting_balance_equivalent * self.reward_percent,
                'total_trades': self.total_trades,
                'win_rate': (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                'compounding_enabled': self.compounding_enabled,
                'balance_growth': ((current_balance - self.starting_balance_equivalent) / self.starting_balance_equivalent * 100) if self.starting_balance_equivalent > 0 else 0
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get money management status: {e}")
            return None

def main():
    """Test money management engine"""
    print("🚀 COMPOUNDING MONEY MANAGEMENT ENGINE")
    print("Starting Balance Equivalent: $100")
    print("Risk: 1% ($1) | Reward: 2.5% ($2.5) | RR: 2.5:1")
    print("="*70)
    
    try:
        # Initialize money manager
        money_manager = CompoundingMoneyManager()
        
        # Initialize balance tracking
        if not money_manager.initialize_balance_tracking():
            print("❌ Failed to initialize balance tracking")
            return
        
        # Calculate position size
        position_data = money_manager.calculate_position_size()
        if not position_data:
            print("❌ Failed to calculate position size")
            return
        
        # Validate position safety
        is_safe, safety_checks = money_manager.validate_position_safety(position_data)
        
        print(f"\n📊 MONEY MANAGEMENT STATUS:")
        print(f"  Current Balance: ${position_data['current_balance']:.2f}")
        print(f"  Compounding: {'ENABLED' if position_data['compounding_enabled'] else 'DISABLED'}")
        print(f"  Position Size: ${position_data['position_size']:.2f}")
        print(f"  Risk Amount: ${position_data['risk_amount']:.2f}")
        print(f"  Reward Amount: ${position_data['reward_amount']:.2f}")
        print(f"  Risk-Reward: {position_data['risk_reward_ratio']:.1f}:1")
        print(f"  Balance Utilization: {position_data['balance_utilization']:.1f}%")
        
        print(f"\n✅ MONEY MANAGEMENT ENGINE READY!")
        print(f"✅ Compounding system: OPERATIONAL")
        print(f"✅ Position sizing: CALCULATED")
        print(f"✅ Safety validation: {'PASSED' if is_safe else 'FAILED'}")
        print(f"✅ Risk management: ACTIVE")
        
        if is_safe:
            print(f"\n🚀 READY FOR LIVE TRADING WITH COMPOUNDING!")
        else:
            print(f"\n⚠️ SAFETY CHECKS FAILED - REVIEW REQUIRED")
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
