#!/usr/bin/env python3
"""
Security & Compliance Validator - MANDATORY Gate Before Results
NO results presented without passing ALL security and compliance checks
Prevents fake results, saves time and money
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import logging
import json
import hashlib
import inspect
from datetime import datetime
from typing import Dict, List, Tuple, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecurityModule:
    """Security validation to prevent fake/simulated results"""
    
    def __init__(self):
        self.security_violations = []
        
    def check_for_simulation_code(self, code_content: str) -> bool:
        """Check for simulation/fake code patterns"""
        logger.info("🔒 SECURITY: Checking for simulation code...")
        
        # Prohibited simulation patterns
        prohibited_patterns = [
            'random.random',
            'np.random',
            'torch.rand',
            'fake_',
            'simulate_',
            'mock_',
            'dummy_',
            'test_profit',
            'artificial_',
            'generated_pnl',
            'hardcoded_win_rate',
            'fixed_profit'
        ]
        
        violations = []
        for pattern in prohibited_patterns:
            if pattern in code_content.lower():
                violations.append(f"Prohibited simulation pattern: {pattern}")
                
        if violations:
            self.security_violations.extend(violations)
            logger.error(f"🚨 SECURITY VIOLATION: Simulation code detected")
            for violation in violations:
                logger.error(f"   • {violation}")
            return False
            
        logger.info("✅ SECURITY: No simulation code detected")
        return True
    
    def validate_data_authenticity(self, data_path: str) -> bool:
        """Validate that data is real market data"""
        logger.info("🔒 SECURITY: Validating data authenticity...")
        
        if not os.path.exists(data_path):
            self.security_violations.append(f"Data file not found: {data_path}")
            return False
            
        try:
            # Load and check data structure
            df = pd.read_json(data_path, orient='records')
            
            # Check for required real market data fields
            required_fields = ['datetime', 'open', 'high', 'low', 'close', 'volume']
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                self.security_violations.append(f"Missing required market data fields: {missing_fields}")
                return False
            
            # Check for realistic market data patterns
            price_changes = df['close'].pct_change().dropna()
            
            # Real Bitcoin should have reasonable volatility
            daily_volatility = price_changes.std()
            if daily_volatility < 0.001 or daily_volatility > 0.5:  # 0.1% to 50%
                self.security_violations.append(f"Unrealistic volatility: {daily_volatility:.4f}")
                return False
            
            # Check for data continuity (no gaps > 2 hours for hourly data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            time_gaps = df['datetime'].diff().dt.total_seconds() / 3600  # Hours
            max_gap = time_gaps.max()
            
            if max_gap > 3:  # More than 3 hours gap
                logger.warning(f"⚠️ Data gap detected: {max_gap:.1f} hours")
            
            logger.info("✅ SECURITY: Data authenticity validated")
            return True
            
        except Exception as e:
            self.security_violations.append(f"Data validation error: {str(e)}")
            return False
    
    def check_model_integrity(self, model_path: str) -> bool:
        """Check model file integrity"""
        logger.info("🔒 SECURITY: Checking model integrity...")
        
        if not os.path.exists(model_path):
            self.security_violations.append(f"Model file not found: {model_path}")
            return False
            
        try:
            # Load model and check structure
            checkpoint = torch.load(model_path, map_location='cpu')
            
            required_keys = ['model_state_dict', 'model_config']
            missing_keys = [key for key in required_keys if key not in checkpoint]
            
            if missing_keys:
                self.security_violations.append(f"Missing model keys: {missing_keys}")
                return False
            
            # Check for suspicious model modifications
            config = checkpoint['model_config']
            if 'training_date' not in config:
                self.security_violations.append("Model missing training timestamp")
                return False
            
            logger.info("✅ SECURITY: Model integrity validated")
            return True
            
        except Exception as e:
            self.security_violations.append(f"Model integrity error: {str(e)}")
            return False
    
    def get_security_report(self) -> Dict[str, Any]:
        """Get security validation report"""
        return {
            'security_passed': len(self.security_violations) == 0,
            'violations': self.security_violations,
            'timestamp': datetime.now().isoformat()
        }

    def validate_mathematical_consistency(self, results: Dict[str, Any]) -> bool:
        """SECURITY: Detect mathematically impossible results (fake data)"""
        logger.info("🔒 SECURITY: Validating mathematical consistency...")

        math_violations = []

        for phase, result in results.items():
            if not result:
                continue

            win_rate = result.get('win_rate', 0)
            net_profit = result.get('net_profit', 0)
            total_trades = result.get('total_trades', 0)
            winning_trades = result.get('winning_trades', 0)

            # CRITICAL CHECK 1: Win rate vs profit consistency (2.5:1 risk-reward)
            if total_trades > 0:
                # With 2.5:1 risk-reward ratio, calculate expected profit
                expected_profit_per_trade = (win_rate/100 * 2.5) - ((100-win_rate)/100 * 1.0)

                # 50% win rate should give 75% profit with 2.5:1 R:R
                if win_rate >= 40 and net_profit <= 0:
                    math_violations.append(f"{phase}: {win_rate:.1f}% win rate with {net_profit:.2f} profit (IMPOSSIBLE with 2.5:1 R:R)")

                # Check breakeven point (40% win rate should break even)
                if win_rate >= 45 and net_profit < 0:
                    math_violations.append(f"{phase}: {win_rate:.1f}% win rate with negative profit (IMPOSSIBLE with 2.5:1 R:R)")

            # CRITICAL CHECK 2: Perfect scores are fake
            if win_rate == 100.0:
                math_violations.append(f"{phase}: Perfect 100% win rate is FAKE")

            if result.get('composite_score', 0) == 1.0 and win_rate < 90:
                math_violations.append(f"{phase}: Perfect composite score with {win_rate:.1f}% win rate is FAKE")

            # CRITICAL CHECK 3: Unrealistic performance
            if win_rate > 95:
                math_violations.append(f"{phase}: Win rate {win_rate:.1f}% is unrealistically high (FAKE)")

            trades_per_day = result.get('trades_per_day', 0)
            if trades_per_day > 100:
                math_violations.append(f"{phase}: {trades_per_day:.1f} trades/day is unrealistic (FAKE)")

            # CRITICAL CHECK 4: Composite score consistency
            composite_score = result.get('composite_score', 0)
            if composite_score > 0.9 and (win_rate < 60 or net_profit <= 0):
                math_violations.append(f"{phase}: High composite score {composite_score:.3f} inconsistent with performance (FAKE)")

        if math_violations:
            logger.error("❌ SECURITY: Mathematical inconsistencies detected - FAKE RESULTS BLOCKED:")
            for violation in math_violations:
                logger.error(f"   🚨 {violation}")
                self.security_violations.append(f"Mathematical inconsistency: {violation}")
            return False

        logger.info("✅ SECURITY: Mathematical consistency validated")
        return True

class ComplianceModule:
    """Master document compliance validation"""
    
    def __init__(self):
        self.compliance_violations = []
        self.master_doc_path = "MASTER_TRADING_SYSTEM_DOCUMENTATION.md"
        
    def load_master_document_requirements(self) -> Dict[str, Any]:
        """Load requirements from master document"""
        logger.info("📋 COMPLIANCE: Loading master document requirements...")
        
        # EXACT requirements from master document
        requirements = {
            'grid_spacing': 0.0025,  # EXACTLY 0.25%
            'grid_tolerance_max': 0.01,  # MAX 1.0% (corrected)
            'risk_reward_ratio': 2.5,  # EXACTLY 2.5:1
            'risk_per_trade_max': 0.01,  # MAX 1%
            'win_rate_target': 60.0,  # EXACTLY 60%
            'trades_per_day_target': 8.0,  # EXACTLY 8 trades/day
            'composite_score_target': 0.8,  # EXACTLY 0.8
            'data_split': {
                'training_years': [2022, 2023],  # 2 years
                'out_of_sample_years': [2024],   # 1 year
                'backtest_years': [2021]         # 1 year
            },
            'hierarchy_requirement': 'backtest > out_of_sample > training',
            'indicators_required': ['price', 'rsi', 'vwap', 'volume'],  # EXACTLY 4
            'model_architecture': 'TCN-CNN-PPO',
            'deployment_gate': '100% compliance required'
        }
        
        logger.info("✅ COMPLIANCE: Master document requirements loaded")
        return requirements
    
    def validate_parameters(self, params: Dict[str, Any]) -> bool:
        """Validate system parameters against master document"""
        logger.info("📋 COMPLIANCE: Validating parameters...")
        
        requirements = self.load_master_document_requirements()
        
        # Check grid spacing
        if abs(params.get('grid_spacing', 0) - requirements['grid_spacing']) > 1e-6:
            self.compliance_violations.append(f"Grid spacing must be exactly {requirements['grid_spacing']}")
        
        # Check grid tolerance
        if params.get('grid_tolerance', 0) > requirements['grid_tolerance_max']:
            self.compliance_violations.append(f"Grid tolerance exceeds maximum {requirements['grid_tolerance_max']}")
        
        # Check risk-reward ratio
        if abs(params.get('risk_reward_ratio', 0) - requirements['risk_reward_ratio']) > 0.1:
            self.compliance_violations.append(f"Risk-reward ratio must be exactly {requirements['risk_reward_ratio']}")
        
        # Check risk per trade
        if params.get('risk_per_trade', 0) > requirements['risk_per_trade_max']:
            self.compliance_violations.append(f"Risk per trade exceeds maximum {requirements['risk_per_trade_max']}")
        
        if self.compliance_violations:
            logger.error("❌ COMPLIANCE: Parameter validation failed")
            for violation in self.compliance_violations:
                logger.error(f"   • {violation}")
            return False
        
        logger.info("✅ COMPLIANCE: Parameters validated")
        return True

    def validate_ensemble_architecture(self, model_path):
        """COMPLIANCE: Validate ensemble TCN-CNN-PPO architecture"""
        logger.info("📋 COMPLIANCE: Validating ensemble architecture...")

        try:
            # Load model config
            checkpoint = torch.load(model_path, map_location='cpu')
            model_config = checkpoint.get('model_config', {})

            # Check ensemble requirements per master document
            ensemble_checks = {
                'architecture_name': 'ensemble' in model_config.get('architecture', '').lower(),
                'tcn_ensemble': model_config.get('ensemble_components', {}).get('tcn_models', 0) >= 3,
                'cnn_ensemble': model_config.get('ensemble_components', {}).get('cnn_models', 0) >= 3,
                'ppo_ensemble': model_config.get('ensemble_components', {}).get('ppo_models', 0) >= 3,
                'compliance_validated': model_config.get('compliance_validated', False)
            }

            ensemble_passed = all(ensemble_checks.values())

            logger.info(f"📋 COMPLIANCE: Ensemble Architecture Validation:")
            for check, passed in ensemble_checks.items():
                logger.info(f"   {check}: {'✅ PASSED' if passed else '❌ FAILED'}")

            if not ensemble_passed:
                self.compliance_violations.append("Ensemble architecture requirements not met")
                logger.error("❌ COMPLIANCE: Ensemble architecture validation failed")
            else:
                logger.info("✅ COMPLIANCE: Ensemble architecture validated")

            return ensemble_passed

        except Exception as e:
            self.compliance_violations.append(f"Ensemble validation error: {e}")
            logger.error(f"❌ COMPLIANCE: Ensemble validation error: {e}")
            return False
    
    def validate_performance_targets(self, results: Dict[str, Any]) -> bool:
        """Validate performance against master document targets"""
        logger.info("📋 COMPLIANCE: Validating performance targets...")
        
        requirements = self.load_master_document_requirements()
        
        for phase_name, phase_data in results.items():
            if phase_data is None:
                continue
                
            # Check win rate
            win_rate = phase_data.get('win_rate', 0)
            if win_rate < requirements['win_rate_target']:
                self.compliance_violations.append(f"{phase_name}: Win rate {win_rate:.1f}% < target {requirements['win_rate_target']:.1f}%")
            
            # Check trades per day
            trades_per_day = phase_data.get('trades_per_day', 0)
            if trades_per_day < requirements['trades_per_day_target']:
                self.compliance_violations.append(f"{phase_name}: Trades/day {trades_per_day:.1f} < target {requirements['trades_per_day_target']:.1f}")
            
            # Check composite score
            composite_score = phase_data.get('composite_score', 0)
            if composite_score < requirements['composite_score_target']:
                self.compliance_violations.append(f"{phase_name}: Composite score {composite_score:.3f} < target {requirements['composite_score_target']:.1f}")
        
        if self.compliance_violations:
            logger.error("❌ COMPLIANCE: Performance validation failed")
            for violation in self.compliance_violations:
                logger.error(f"   • {violation}")
            return False
        
        logger.info("✅ COMPLIANCE: Performance targets validated")
        return True
    
    def validate_hierarchy_requirement(self, results: Dict[str, Any]) -> bool:
        """Validate performance hierarchy requirement"""
        logger.info("📋 COMPLIANCE: Validating hierarchy requirement...")
        
        if 'training' not in results or 'out_of_sample' not in results or 'backtest' not in results:
            self.compliance_violations.append("Missing required phases for hierarchy validation")
            return False
        
        training_reward = results['training'].get('corrected_reward', 0)
        out_of_sample_reward = results['out_of_sample'].get('corrected_reward', 0)
        backtest_reward = results['backtest'].get('corrected_reward', 0)
        
        # MASTER DOCUMENT: backtest > out_of_sample > training
        hierarchy_correct = (backtest_reward > out_of_sample_reward and 
                           backtest_reward > training_reward and
                           out_of_sample_reward > training_reward)
        
        if not hierarchy_correct:
            self.compliance_violations.append(f"Hierarchy violation: Backtest ({backtest_reward:.2f}) must exceed Out-of-Sample ({out_of_sample_reward:.2f}) and Training ({training_reward:.2f})")
            logger.error("❌ COMPLIANCE: Hierarchy requirement not met")
            return False
        
        logger.info("✅ COMPLIANCE: Hierarchy requirement validated")
        return True
    
    def validate_mathematical_consistency(self, results: Dict[str, Any]) -> bool:
        """Validate mathematical consistency of results"""
        logger.info("📋 COMPLIANCE: Validating mathematical consistency...")
        
        for phase_name, phase_data in results.items():
            if phase_data is None:
                continue
            
            # Validate composite score calculation
            win_rate = phase_data.get('win_rate', 0)
            trades_per_day = phase_data.get('trades_per_day', 0)
            composite_score = phase_data.get('composite_score', 0)
            
            # Calculate expected composite score
            win_rate_component = min(1.0, win_rate / 60.0) * 0.15
            frequency_component = min(1.0, trades_per_day / 8.0) * 0.05
            minimum_expected = win_rate_component + frequency_component + 0.10  # + drawdown component
            
            if composite_score < minimum_expected * 0.8:  # Allow 20% tolerance
                self.compliance_violations.append(f"{phase_name}: Composite score {composite_score:.3f} inconsistent with win rate {win_rate:.1f}%")
            
            # Validate reward calculation
            net_profit = phase_data.get('net_profit', 0)
            corrected_reward = phase_data.get('corrected_reward', 0)
            expected_reward = composite_score * max(0, net_profit)
            
            if abs(corrected_reward - expected_reward) > 0.01:
                self.compliance_violations.append(f"{phase_name}: Reward calculation inconsistent")
        
        if self.compliance_violations:
            logger.error("❌ COMPLIANCE: Mathematical consistency failed")
            for violation in self.compliance_violations:
                logger.error(f"   • {violation}")
            return False
        
        logger.info("✅ COMPLIANCE: Mathematical consistency validated")
        return True
    
    def get_compliance_report(self) -> Dict[str, Any]:
        """Get compliance validation report"""
        return {
            'compliance_passed': len(self.compliance_violations) == 0,
            'violations': self.compliance_violations,
            'master_document': self.master_doc_path,
            'timestamp': datetime.now().isoformat()
        }

class ValidationGate:
    """Main validation gate - NO results without passing ALL checks"""
    
    def __init__(self):
        self.security_module = SecurityModule()
        self.compliance_module = ComplianceModule()
        
    def validate_before_training(self, code_files: List[str], data_path: str, params: Dict[str, Any]) -> bool:
        """Validate before training starts"""
        logger.info("🛡️ VALIDATION GATE: Pre-training validation...")
        
        # Security checks
        for code_file in code_files:
            if os.path.exists(code_file):
                with open(code_file, 'r', encoding='utf-8') as f:
                    code_content = f.read()
                if not self.security_module.check_for_simulation_code(code_content):
                    return False
        
        if not self.security_module.validate_data_authenticity(data_path):
            return False
        
        # Compliance checks
        if not self.compliance_module.validate_parameters(params):
            return False
        
        logger.info("✅ VALIDATION GATE: Pre-training validation passed")
        return True
    
    def validate_before_results(self, results: Dict[str, Any], model_path: str) -> bool:
        """Validate authenticity before presenting results - SHOW REAL RESULTS"""
        logger.info("🛡️ VALIDATION GATE: Authenticity validation (not performance blocking)...")

        # SECURITY: Block only fake/impossible results
        if not self.security_module.check_model_integrity(model_path):
            logger.error("🚨 SECURITY: Model integrity failed - BLOCKING")
            return False

        # CRITICAL: Block only mathematically impossible results (fake data)
        if not self.security_module.validate_mathematical_consistency(results):
            logger.error("🚨 SECURITY: FAKE RESULTS DETECTED - BLOCKING PRESENTATION")
            return False

        # ARCHITECTURE: Validate ensemble architecture (not performance)
        if not self.compliance_module.validate_ensemble_architecture(model_path):
            logger.error("🚨 COMPLIANCE: Architecture validation failed - BLOCKING")
            return False

        logger.info("✅ VALIDATION GATE: Authenticity validation passed - REAL RESULTS AUTHORIZED")
        logger.info("📊 Results will be presented with compliance status (not blocked)")
        return True

    def evaluate_compliance_status(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate compliance status WITHOUT blocking results"""
        logger.info("📋 COMPLIANCE: Evaluating performance status (not blocking)...")

        compliance_status = {
            'performance_targets_met': self.compliance_module.validate_performance_targets(results),
            'hierarchy_correct': self.compliance_module.validate_hierarchy_requirement(results),
            'mathematical_consistency': self.compliance_module.validate_mathematical_consistency(results),
            'deployment_ready': False
        }

        # Deployment authorization requires ALL compliance checks
        compliance_status['deployment_ready'] = all([
            compliance_status['performance_targets_met'],
            compliance_status['hierarchy_correct'],
            compliance_status['mathematical_consistency']
        ])

        return compliance_status
    
    def generate_validation_report(self, results: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate comprehensive validation report with compliance status"""
        security_report = self.security_module.get_security_report()
        compliance_report = self.compliance_module.get_compliance_report()

        # Get compliance status if results provided
        compliance_status = {}
        if results:
            compliance_status = self.evaluate_compliance_status(results)

        overall_passed = security_report['security_passed'] and compliance_report['compliance_passed']

        return {
            'overall_validation_passed': overall_passed,
            'security_report': security_report,
            'compliance_report': compliance_report,
            'compliance_status': compliance_status,
            'results_authentic': security_report['security_passed'],
            'deployment_authorized': compliance_status.get('deployment_ready', False),
            'timestamp': datetime.now().isoformat()
        }

def main():
    """Test validation system"""
    print("🛡️ SECURITY & COMPLIANCE VALIDATOR")
    print("=" * 60)
    print("🔒 Security Module: Prevents fake/simulated results")
    print("📋 Compliance Module: Enforces master document requirements")
    print("🚨 NO RESULTS WITHOUT PASSING ALL CHECKS")
    print("=" * 60)
    
    # Test validation
    validator = ValidationGate()
    
    # Test parameters
    test_params = {
        'grid_spacing': 0.0025,
        'grid_tolerance': 0.01,
        'risk_reward_ratio': 2.5,
        'risk_per_trade': 0.01
    }
    
    # Test pre-training validation
    code_files = ['simple_corrected_trainer.py']
    data_path = 'real_bitcoin_4year_data.json'
    
    if validator.validate_before_training(code_files, data_path, test_params):
        print("✅ Pre-training validation passed")
    else:
        print("❌ Pre-training validation failed")
    
    # Generate report
    report = validator.generate_validation_report()
    print(f"\n📊 Validation Status: {'✅ PASSED' if report['overall_validation_passed'] else '❌ FAILED'}")
    
    return report['overall_validation_passed']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
