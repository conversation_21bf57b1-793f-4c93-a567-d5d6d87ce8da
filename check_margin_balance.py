#!/usr/bin/env python3
"""Check isolated margin balance and calculate proper position sizing"""

import sys
sys.path.append('01_binance_connector')

from binance_real_money_connector import BinanceRealMoneyConnector

def main():
    try:
        connector = BinanceRealMoneyConnector()
        balance = connector.get_account_balance()
        
        if balance:
            print("🏦 ISOLATED MARGIN ACCOUNT STATUS")
            print("=" * 50)
            print(f"💰 Total Balance: ${balance['total_usdt_value']:.2f}")
            print(f"📊 BTC Balance: {balance['btc']['netAsset']:.6f} BTC")
            print(f"💵 USDT Balance: {balance['usdt']['netAsset']:.2f} USDT")
            print(f"📈 BTC Price: ${balance['current_btc_price']:.2f}")
            print(f"⚖️ Margin Level: {balance['margin_level']:.2f}")
            print(f"📊 Margin Ratio: {balance['margin_ratio']:.4f}")
            
            # Calculate what we can actually trade with
            total_balance = balance['total_usdt_value']
            current_price = balance['current_btc_price']
            
            print("\n💡 LEVERAGED POSITION CALCULATIONS")
            print("=" * 50)
            
            # For $1 SL with 0.1% distance
            sl_percentage = 0.001  # 0.1%
            target_sl = 1.00
            
            # Position size needed for $1 SL
            position_size_btc = target_sl / (current_price * sl_percentage)
            position_value = position_size_btc * current_price
            
            print(f"🎯 Target SL: ${target_sl:.2f}")
            print(f"📊 SL Distance: {sl_percentage*100:.1f}%")
            print(f"📊 Position Size Needed: {position_size_btc:.6f} BTC")
            print(f"💵 Position Value: ${position_value:.2f}")
            
            # With 10x leverage
            leverage = 10
            margin_needed = position_value / leverage
            print(f"⚡ With {leverage}x Leverage:")
            print(f"💰 Margin Needed: ${margin_needed:.2f}")
            print(f"📊 Available Balance: ${total_balance:.2f}")
            print(f"✅ Feasible: {'YES' if margin_needed <= total_balance * 0.8 else 'NO'}")
            
            if margin_needed > total_balance * 0.8:
                # Calculate maximum feasible position
                max_margin = total_balance * 0.5  # Use 50% of balance as margin
                max_position_value = max_margin * leverage
                max_position_size = max_position_value / current_price
                actual_sl = max_position_size * current_price * sl_percentage
                
                print(f"\n🔧 ADJUSTED CALCULATION:")
                print(f"💰 Max Margin (50%): ${max_margin:.2f}")
                print(f"💵 Max Position Value: ${max_position_value:.2f}")
                print(f"📊 Max Position Size: {max_position_size:.6f} BTC")
                print(f"🔴 Actual SL: ${actual_sl:.2f}")
                print(f"🟢 Actual TP: ${actual_sl * 2.5:.2f}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
