#!/usr/bin/env python3
"""
VERIFY TRADING PERMISSIONS AND ISOLATED MARGIN
Comprehensive verification of trading setup
"""

import os
import sys
import time
import json
import logging
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_permissions_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingPermissionsVerifier:
    """Verify trading permissions and isolated margin setup"""
    
    def __init__(self):
        logger.info("TRADING PERMISSIONS VERIFIER INITIALIZED")
    
    def test_basic_connection(self):
        """Test basic Binance connection"""
        try:
            logger.info("Testing basic Binance connection...")
            
            from binance_real_money_connector import BinanceRealMoneyConnector
            connector = BinanceRealMoneyConnector()
            
            # Test basic connection
            account_info = connector.get_account_info()
            if account_info:
                logger.info("SUCCESS: Basic connection working")
                logger.info(f"Account Type: {account_info.get('account_type', 'Unknown')}")
                logger.info(f"Can Trade: {account_info.get('can_trade', False)}")
                logger.info(f"Can Withdraw: {account_info.get('can_withdraw', False)}")
                logger.info(f"Can Deposit: {account_info.get('can_deposit', False)}")
                logger.info(f"Permissions: {account_info.get('permissions', [])}")
                return True, account_info
            else:
                logger.error("FAILED: Basic connection failed")
                return False, None
                
        except Exception as e:
            logger.error(f"FAILED: Basic connection error: {e}")
            return False, None
    
    def test_isolated_margin_access(self):
        """Test isolated margin account access"""
        try:
            logger.info("Testing isolated margin access...")
            
            from binance_real_money_connector import BinanceRealMoneyConnector
            connector = BinanceRealMoneyConnector()
            
            # Test isolated margin balance
            balance_info = connector.get_isolated_margin_balance()
            if balance_info:
                logger.info("SUCCESS: Isolated margin access working")
                logger.info(f"USDT Balance: {balance_info['usdt_balance']:.2f}")
                logger.info(f"BTC Balance: {balance_info['btc_balance']:.8f}")
                logger.info(f"Total USD Value: ${balance_info['total_usdt_value']:.2f}")
                logger.info(f"Current BTC Price: ${balance_info['btc_price']:.2f}")
                return True, balance_info
            else:
                logger.error("FAILED: Isolated margin access failed")
                return False, None
                
        except Exception as e:
            logger.error(f"FAILED: Isolated margin error: {e}")
            return False, None
    
    def test_order_permissions_detailed(self):
        """Test order permissions with detailed analysis"""
        try:
            logger.info("Testing order permissions (detailed)...")
            
            from binance.client import Client
            from dotenv import load_dotenv
            
            # Load environment
            load_dotenv()
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            if not api_key or not api_secret:
                logger.error("FAILED: API credentials not found")
                return False, None
            
            # Create direct client
            client = Client(api_key, api_secret)
            
            # Get account info directly
            account = client.get_account()
            
            logger.info("DETAILED ACCOUNT INFORMATION:")
            logger.info(f"Account Type: {account.get('accountType', 'Unknown')}")
            logger.info(f"Can Trade: {account.get('canTrade', False)}")
            logger.info(f"Can Withdraw: {account.get('canWithdraw', False)}")
            logger.info(f"Can Deposit: {account.get('canDeposit', False)}")
            logger.info(f"Permissions: {account.get('permissions', [])}")
            logger.info(f"Update Time: {account.get('updateTime', 0)}")
            
            # Test if we can get order book (requires no special permissions)
            try:
                order_book = client.get_order_book(symbol='BTCUSDT', limit=5)
                logger.info("SUCCESS: Market data access confirmed")
            except Exception as e:
                logger.warning(f"WARNING: Market data access issue: {e}")
            
            # Test if we can get account trades (requires trading permissions)
            try:
                # This will fail if no trading permissions
                trades = client.get_my_trades(symbol='BTCUSDT', limit=1)
                logger.info("SUCCESS: Trading history access confirmed (trading permissions likely enabled)")
                return True, account
            except Exception as e:
                if 'API-key format invalid' in str(e):
                    logger.error("FAILED: API key format invalid")
                elif 'Signature for this request is not valid' in str(e):
                    logger.error("FAILED: API signature invalid")
                elif 'Invalid API-key, IP, or permissions for action' in str(e):
                    logger.error("FAILED: Trading permissions not enabled or IP not whitelisted")
                else:
                    logger.warning(f"WARNING: Trading history access issue: {e}")
                    # This might still work for trading even if history access fails
                    return account.get('canTrade', False), account
            
            return account.get('canTrade', False), account
            
        except Exception as e:
            logger.error(f"FAILED: Order permissions test error: {e}")
            return False, None
    
    def test_small_order_simulation(self):
        """Test if we can simulate a small order (without executing)"""
        try:
            logger.info("Testing small order simulation...")
            
            from binance.client import Client
            from dotenv import load_dotenv
            
            # Load environment
            load_dotenv()
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            client = Client(api_key, api_secret)
            
            # Get current price
            ticker = client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate minimum order size
            symbol_info = client.get_symbol_info('BTCUSDT')
            min_qty = None
            min_notional = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    min_qty = float(filter_item['minQty'])
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    min_notional = float(filter_item['minNotional'])
            
            logger.info(f"Current BTC Price: ${current_price:.2f}")
            logger.info(f"Minimum Quantity: {min_qty}")
            logger.info(f"Minimum Notional: ${min_notional}")
            
            # Calculate test order size (just above minimum)
            if min_qty and min_notional:
                test_qty = max(min_qty, min_notional / current_price) * 1.1  # 10% above minimum
                test_notional = test_qty * current_price
                
                logger.info(f"Test Order Quantity: {test_qty:.8f} BTC")
                logger.info(f"Test Order Value: ${test_notional:.2f}")
                
                # Test order validation (TEST mode - no actual order)
                try:
                    test_order = client.create_test_order(
                        symbol='BTCUSDT',
                        side='BUY',
                        type='MARKET',
                        quantity=f"{test_qty:.8f}"
                    )
                    logger.info("SUCCESS: Test order validation passed - trading permissions confirmed")
                    return True, {
                        'test_qty': test_qty,
                        'test_notional': test_notional,
                        'current_price': current_price
                    }
                except Exception as e:
                    logger.error(f"FAILED: Test order validation failed: {e}")
                    return False, None
            else:
                logger.error("FAILED: Could not determine minimum order requirements")
                return False, None
                
        except Exception as e:
            logger.error(f"FAILED: Order simulation error: {e}")
            return False, None
    
    def run_complete_verification(self):
        """Run complete trading permissions verification"""
        try:
            logger.info("STARTING COMPLETE TRADING PERMISSIONS VERIFICATION")
            logger.info("="*80)
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'tests': {}
            }
            
            # Test 1: Basic Connection
            logger.info("TEST 1: Basic Connection")
            success, data = self.test_basic_connection()
            results['tests']['basic_connection'] = {
                'success': success,
                'data': data
            }
            
            if not success:
                logger.error("CRITICAL: Basic connection failed - cannot proceed")
                return False, results
            
            # Test 2: Isolated Margin Access
            logger.info("TEST 2: Isolated Margin Access")
            success, data = self.test_isolated_margin_access()
            results['tests']['isolated_margin'] = {
                'success': success,
                'data': data
            }
            
            # Test 3: Order Permissions (Detailed)
            logger.info("TEST 3: Order Permissions (Detailed)")
            success, data = self.test_order_permissions_detailed()
            results['tests']['order_permissions'] = {
                'success': success,
                'data': data
            }
            
            # Test 4: Small Order Simulation
            logger.info("TEST 4: Small Order Simulation")
            success, data = self.test_small_order_simulation()
            results['tests']['order_simulation'] = {
                'success': success,
                'data': data
            }
            
            # Overall assessment
            critical_tests = ['basic_connection', 'isolated_margin', 'order_permissions']
            all_critical_passed = all(results['tests'][test]['success'] for test in critical_tests)
            
            results['overall_success'] = all_critical_passed
            results['ready_for_trading'] = all_critical_passed and results['tests']['order_simulation']['success']
            
            # Save results
            with open('trading_permissions_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info("="*80)
            if results['ready_for_trading']:
                logger.info("SUCCESS: ALL TESTS PASSED - READY FOR LIVE TRADING")
            elif all_critical_passed:
                logger.info("PARTIAL SUCCESS: Critical tests passed, order simulation failed")
                logger.info("System may still work for trading")
            else:
                logger.error("FAILED: Critical tests failed - NOT ready for trading")
            
            return results['ready_for_trading'], results
            
        except Exception as e:
            logger.error(f"FAILED: Complete verification error: {e}")
            return False, None

def main():
    """Main execution"""
    print("TRADING PERMISSIONS VERIFICATION")
    print("Comprehensive test of trading setup")
    print("="*80)
    
    try:
        # Initialize verifier
        verifier = TradingPermissionsVerifier()
        
        # Run complete verification
        ready, results = verifier.run_complete_verification()
        
        if ready:
            print("\nSUCCESS: SYSTEM IS READY FOR LIVE TRADING!")
            print("All permissions verified and isolated margin confirmed")
            
            # Display key information
            if results and 'tests' in results:
                margin_data = results['tests'].get('isolated_margin', {}).get('data')
                if margin_data:
                    print(f"\nISOLATED MARGIN ACCOUNT:")
                    print(f"  Total Balance: ${margin_data['total_usdt_value']:.2f}")
                    print(f"  USDT: {margin_data['usdt_balance']:.2f}")
                    print(f"  BTC: {margin_data['btc_balance']:.8f}")
                    print(f"  Current BTC Price: ${margin_data['btc_price']:.2f}")
                
                order_data = results['tests'].get('order_simulation', {}).get('data')
                if order_data:
                    print(f"\nORDER TESTING:")
                    print(f"  Test Order Size: {order_data['test_qty']:.8f} BTC")
                    print(f"  Test Order Value: ${order_data['test_notional']:.2f}")
                    print(f"  Order Validation: PASSED")
            
            print("\nSYSTEM IS READY FOR LIVE DEPLOYMENT!")
            
        else:
            print("\nFAILED: System is NOT ready for live trading")
            print("Check trading_permissions_results.json for details")
            
            if results and 'tests' in results:
                print("\nTEST RESULTS:")
                for test_name, test_result in results['tests'].items():
                    status = "PASSED" if test_result['success'] else "FAILED"
                    print(f"  {test_name}: {status}")
            
    except Exception as e:
        print(f"\nVERIFICATION ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
