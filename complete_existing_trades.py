#!/usr/bin/env python3
"""
COMPLETE EXISTING TRADES
Use existing BTC positions to complete full cycle test trades

EXISTING POSITIONS:
- Entry Order 1: 46196674337 (0.00008 BTC @ $119,733.06)
- Entry Order 2: 46196764679 (0.00008 BTC @ $119,706.84)

GOAL: Place OCO orders and monitor until TP or SL hit
"""

import os
import sys
import time
import json
import logging
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_existing_trades.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteExistingTrades:
    """Complete existing trades with OCO orders and monitoring"""
    
    def __init__(self):
        # EXISTING TRADE DATA
        self.existing_trades = [
            {
                'entry_order_id': 46196674337,
                'entry_price': 119733.06,
                'quantity': 0.00008000,
                'cost': 9.58
            },
            {
                'entry_order_id': 46196764679,
                'entry_price': 119706.84,
                'quantity': 0.00008000,
                'cost': 9.58
            }
        ]
        
        # PROVEN MODEL PARAMETERS
        self.stop_loss_percent = 0.001      # 0.1% SL
        self.take_profit_percent = 0.0025   # 0.25% TP
        
        # Active trades tracking
        self.active_trades = []
        
        logger.info("COMPLETE EXISTING TRADES EXECUTOR INITIALIZED")
        logger.info(f"Found {len(self.existing_trades)} existing positions to complete")
    
    def initialize_connections(self):
        """Initialize connections"""
        try:
            logger.info("Initializing connections...")
            
            # Initialize Binance connector
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Test connection
            account_info = self.binance.get_account_info()
            if not account_info:
                raise Exception("Binance connection failed")
            
            logger.info("Binance connection: SUCCESS")
            
            # Initialize Telegram bot
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                logger.info("Telegram bot: SUCCESS")
            except Exception as e:
                logger.warning(f"Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            logger.error(f"Connection initialization failed: {e}")
            return False
    
    def verify_existing_positions(self):
        """Verify existing BTC positions"""
        try:
            logger.info("Verifying existing BTC positions...")
            
            # Get current balance
            balance_info = self.binance.get_isolated_margin_balance()
            current_btc = balance_info['btc_balance']
            
            logger.info(f"Current BTC Balance: {current_btc:.8f}")
            
            # Calculate expected BTC from our trades
            expected_btc = sum(trade['quantity'] for trade in self.existing_trades)
            logger.info(f"Expected BTC from trades: {expected_btc:.8f}")
            
            # Check if we have sufficient BTC for exit orders
            if current_btc >= expected_btc:
                logger.info("✅ Sufficient BTC balance for exit orders")
                return True
            else:
                logger.warning(f"⚠️ Insufficient BTC: Have {current_btc:.8f}, Need {expected_btc:.8f}")
                return False
            
        except Exception as e:
            logger.error(f"Position verification failed: {e}")
            return False
    
    def place_oco_for_trade(self, trade_data):
        """Place OCO order for a specific trade"""
        try:
            logger.info(f"Placing OCO for trade {trade_data['entry_order_id']}...")
            
            # Calculate SL and TP prices
            entry_price = trade_data['entry_price']
            stop_loss_price = entry_price * (1 - self.stop_loss_percent)
            take_profit_price = entry_price * (1 + self.take_profit_percent)
            
            # Get symbol requirements for proper formatting
            symbol_info = self.binance.client.get_symbol_info('BTCUSDT')
            tick_size = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_item['tickSize'])
                    break
            
            # Round prices to tick size
            import decimal
            decimal.getcontext().rounding = decimal.ROUND_HALF_UP
            tick_decimal = decimal.Decimal(str(tick_size))
            
            stop_loss_price = float(decimal.Decimal(str(stop_loss_price)).quantize(tick_decimal))
            take_profit_price = float(decimal.Decimal(str(take_profit_price)).quantize(tick_decimal))
            
            # Format parameters
            quantity_str = f"{trade_data['quantity']:.8f}".rstrip('0').rstrip('.')
            tp_price_str = f"{take_profit_price:.2f}"
            sl_price_str = f"{stop_loss_price:.2f}"
            sl_limit_price = stop_loss_price * 0.999
            sl_limit_str = f"{sl_limit_price:.2f}"
            
            logger.info(f"📋 OCO ORDER DETAILS:")
            logger.info(f"  Entry Order: {trade_data['entry_order_id']}")
            logger.info(f"  Entry Price: ${entry_price:.2f}")
            logger.info(f"  Quantity: {quantity_str}")
            logger.info(f"  Take Profit: {tp_price_str}")
            logger.info(f"  Stop Loss: {sl_price_str}")
            logger.info(f"  Stop Limit: {sl_limit_str}")
            
            # Place OCO order with proper error handling
            try:
                oco_order = self.binance.client.create_oco_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    quantity=quantity_str,
                    price=tp_price_str,
                    stopPrice=sl_price_str,
                    stopLimitPrice=sl_limit_str,
                    stopLimitTimeInForce='GTC'
                )
                
                # Extract order IDs safely
                oco_order_id = oco_order['orderListId']
                take_profit_order_id = None
                stop_loss_order_id = None
                
                if 'orders' in oco_order:
                    for order in oco_order['orders']:
                        if 'type' in order:
                            if order['type'] == 'LIMIT_MAKER':
                                take_profit_order_id = order['orderId']
                            elif order['type'] == 'STOP_LOSS_LIMIT':
                                stop_loss_order_id = order['orderId']
                
                # Store active trade data
                active_trade = {
                    'entry_order_id': trade_data['entry_order_id'],
                    'entry_price': entry_price,
                    'quantity': trade_data['quantity'],
                    'cost': trade_data['cost'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'oco_order_id': oco_order_id,
                    'take_profit_order_id': take_profit_order_id,
                    'stop_loss_order_id': stop_loss_order_id,
                    'start_time': datetime.now()
                }
                
                self.active_trades.append(active_trade)
                
                logger.info(f"🎉 OCO ORDER PLACED SUCCESSFULLY!")
                logger.info(f"📋 OCO Order ID: {oco_order_id}")
                logger.info(f"📋 Take Profit Order: {take_profit_order_id}")
                logger.info(f"📋 Stop Loss Order: {stop_loss_order_id}")
                
                return True
                
            except Exception as oco_error:
                logger.error(f"OCO order placement failed: {oco_error}")
                
                # Try alternative: separate limit and stop orders
                logger.info("Attempting alternative: separate TP and SL orders...")
                
                try:
                    # Place Take Profit order
                    tp_order = self.binance.client.create_order(
                        symbol='BTCUSDT',
                        side='SELL',
                        type='LIMIT',
                        timeInForce='GTC',
                        quantity=quantity_str,
                        price=tp_price_str
                    )
                    
                    # Place Stop Loss order
                    sl_order = self.binance.client.create_order(
                        symbol='BTCUSDT',
                        side='SELL',
                        type='STOP_LOSS_LIMIT',
                        timeInForce='GTC',
                        quantity=quantity_str,
                        price=sl_limit_str,
                        stopPrice=sl_price_str
                    )
                    
                    # Store active trade data
                    active_trade = {
                        'entry_order_id': trade_data['entry_order_id'],
                        'entry_price': entry_price,
                        'quantity': trade_data['quantity'],
                        'cost': trade_data['cost'],
                        'stop_loss_price': stop_loss_price,
                        'take_profit_price': take_profit_price,
                        'take_profit_order_id': tp_order['orderId'],
                        'stop_loss_order_id': sl_order['orderId'],
                        'start_time': datetime.now(),
                        'separate_orders': True
                    }
                    
                    self.active_trades.append(active_trade)
                    
                    logger.info(f"🎉 SEPARATE ORDERS PLACED SUCCESSFULLY!")
                    logger.info(f"📋 Take Profit Order: {tp_order['orderId']}")
                    logger.info(f"📋 Stop Loss Order: {sl_order['orderId']}")
                    
                    return True
                    
                except Exception as alt_error:
                    logger.error(f"Alternative order placement failed: {alt_error}")
                    return False
            
        except Exception as e:
            logger.error(f"OCO placement for trade failed: {e}")
            return False
    
    def place_all_oco_orders(self):
        """Place OCO orders for all existing trades"""
        try:
            logger.info("PLACING OCO ORDERS FOR ALL EXISTING TRADES")
            logger.info("="*60)
            
            success_count = 0
            
            for i, trade in enumerate(self.existing_trades, 1):
                logger.info(f"🎯 PROCESSING TRADE {i}/{len(self.existing_trades)}")
                
                if self.place_oco_for_trade(trade):
                    success_count += 1
                    logger.info(f"✅ Trade {i} OCO placed successfully")
                else:
                    logger.error(f"❌ Trade {i} OCO placement failed")
                
                # Small delay between orders
                time.sleep(2)
            
            logger.info(f"📊 OCO PLACEMENT SUMMARY:")
            logger.info(f"  Total Trades: {len(self.existing_trades)}")
            logger.info(f"  Successful: {success_count}")
            logger.info(f"  Failed: {len(self.existing_trades) - success_count}")
            logger.info(f"  Active Trades: {len(self.active_trades)}")
            
            if success_count > 0:
                # Send Telegram notification
                self.send_telegram_notification("OCO_PLACED")
                return True
            else:
                return False
            
        except Exception as e:
            logger.error(f"OCO placement failed: {e}")
            return False

    def monitor_all_trades(self, max_hours=24):
        """Monitor all active trades until completion"""
        try:
            logger.info("🔄 MONITORING ALL ACTIVE TRADES UNTIL COMPLETION")
            logger.info(f"Active trades: {len(self.active_trades)}")
            logger.info(f"Maximum monitoring time: {max_hours} hours")
            logger.info("="*80)

            start_time = datetime.now()
            check_count = 0
            completed_trades = []

            while (datetime.now() - start_time).total_seconds() < (max_hours * 3600):
                if len(completed_trades) >= len(self.active_trades):
                    logger.info("🎉 ALL TRADES COMPLETED!")
                    break

                check_count += 1
                current_time = datetime.now().strftime('%H:%M:%S')

                logger.info(f"🔍 CHECK #{check_count} - {current_time}")

                # Get current price
                ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
                logger.info(f"📊 Current BTC Price: ${current_price:.2f}")

                # Check each active trade
                for trade in self.active_trades:
                    if trade['entry_order_id'] in [t['entry_order_id'] for t in completed_trades]:
                        continue  # Skip already completed trades

                    logger.info(f"📋 Checking Trade {trade['entry_order_id']}:")
                    logger.info(f"  Entry: ${trade['entry_price']:.2f}")
                    logger.info(f"  Current: ${current_price:.2f}")
                    logger.info(f"  TP: ${trade['take_profit_price']:.2f}")
                    logger.info(f"  SL: ${trade['stop_loss_price']:.2f}")

                    # Check if trade completed
                    trade_completed = False

                    if 'oco_order_id' in trade:
                        # Check OCO order
                        try:
                            oco_order = self.binance.client.get_oco_order(orderListId=trade['oco_order_id'])
                            if oco_order['listStatusType'] == 'ALL_DONE':
                                # Find filled order
                                for order in oco_order['orders']:
                                    if order['status'] == 'FILLED':
                                        exit_price = float(order['price'])
                                        trade_completed = True

                                        # Determine result
                                        if abs(exit_price - trade['take_profit_price']) < abs(exit_price - trade['stop_loss_price']):
                                            result = 'WIN'
                                            result_type = 'TAKE PROFIT'
                                            pnl = trade['cost'] * self.take_profit_percent
                                        else:
                                            result = 'LOSS'
                                            result_type = 'STOP LOSS'
                                            pnl = -trade['cost'] * self.stop_loss_percent

                                        # Update trade data
                                        trade.update({
                                            'exit_time': datetime.now(),
                                            'exit_price': exit_price,
                                            'result': result,
                                            'result_type': result_type,
                                            'pnl': pnl,
                                            'exit_order_id': order['orderId'],
                                            'duration_minutes': (datetime.now() - trade['start_time']).total_seconds() / 60
                                        })

                                        completed_trades.append(trade)

                                        logger.info(f"🎉 TRADE {trade['entry_order_id']} COMPLETED!")
                                        logger.info(f"  Result: {result} ({result_type})")
                                        logger.info(f"  Exit Price: ${exit_price:.2f}")
                                        logger.info(f"  P&L: ${pnl:.4f}")
                                        logger.info(f"  Duration: {trade['duration_minutes']:.1f} minutes")

                                        break
                        except Exception as e:
                            logger.warning(f"OCO check failed for trade {trade['entry_order_id']}: {e}")

                    elif 'separate_orders' in trade:
                        # Check separate TP and SL orders
                        try:
                            # Check TP order
                            tp_order = self.binance.client.get_order(
                                symbol='BTCUSDT',
                                orderId=trade['take_profit_order_id']
                            )

                            # Check SL order
                            sl_order = self.binance.client.get_order(
                                symbol='BTCUSDT',
                                orderId=trade['stop_loss_order_id']
                            )

                            if tp_order['status'] == 'FILLED':
                                exit_price = float(tp_order['price'])
                                result = 'WIN'
                                result_type = 'TAKE PROFIT'
                                pnl = trade['cost'] * self.take_profit_percent
                                trade_completed = True

                                # Cancel SL order
                                try:
                                    self.binance.client.cancel_order(
                                        symbol='BTCUSDT',
                                        orderId=trade['stop_loss_order_id']
                                    )
                                except:
                                    pass

                            elif sl_order['status'] == 'FILLED':
                                exit_price = float(sl_order['price'])
                                result = 'LOSS'
                                result_type = 'STOP LOSS'
                                pnl = -trade['cost'] * self.stop_loss_percent
                                trade_completed = True

                                # Cancel TP order
                                try:
                                    self.binance.client.cancel_order(
                                        symbol='BTCUSDT',
                                        orderId=trade['take_profit_order_id']
                                    )
                                except:
                                    pass

                            if trade_completed:
                                # Update trade data
                                trade.update({
                                    'exit_time': datetime.now(),
                                    'exit_price': exit_price,
                                    'result': result,
                                    'result_type': result_type,
                                    'pnl': pnl,
                                    'duration_minutes': (datetime.now() - trade['start_time']).total_seconds() / 60
                                })

                                completed_trades.append(trade)

                                logger.info(f"🎉 TRADE {trade['entry_order_id']} COMPLETED!")
                                logger.info(f"  Result: {result} ({result_type})")
                                logger.info(f"  Exit Price: ${exit_price:.2f}")
                                logger.info(f"  P&L: ${pnl:.4f}")
                                logger.info(f"  Duration: {trade['duration_minutes']:.1f} minutes")

                        except Exception as e:
                            logger.warning(f"Separate orders check failed for trade {trade['entry_order_id']}: {e}")

                # Send periodic updates
                if check_count % 10 == 0:
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    logger.info(f"⏱️ Monitoring progress: {elapsed_minutes:.0f} minutes elapsed")
                    logger.info(f"📊 Completed: {len(completed_trades)}/{len(self.active_trades)} trades")

                    if len(completed_trades) > 0:
                        self.send_telegram_notification("MONITORING_UPDATE")

                # Wait before next check
                time.sleep(60)  # Check every minute

            # Final summary
            logger.info("📊 FINAL MONITORING SUMMARY:")
            logger.info(f"  Total Trades: {len(self.active_trades)}")
            logger.info(f"  Completed: {len(completed_trades)}")
            logger.info(f"  Wins: {len([t for t in completed_trades if t.get('result') == 'WIN'])}")
            logger.info(f"  Losses: {len([t for t in completed_trades if t.get('result') == 'LOSS'])}")

            if len(completed_trades) > 0:
                total_pnl = sum(t.get('pnl', 0) for t in completed_trades)
                logger.info(f"  Total P&L: ${total_pnl:.4f}")

                # Send completion notification
                self.send_telegram_notification("ALL_COMPLETED")

                # Save results
                self.save_completion_results(completed_trades)

                return True
            else:
                logger.warning("No trades completed during monitoring period")
                return False

        except Exception as e:
            logger.error(f"Trade monitoring failed: {e}")
            return False

    def send_telegram_notification(self, event_type):
        """Send Telegram notifications"""
        try:
            if not self.telegram_bot:
                return

            if event_type == "OCO_PLACED":
                message = f"""
🎯 OCO ORDERS PLACED FOR EXISTING TRADES

📋 ACTIVE TRADES WITH ORDER NUMBERS:
"""
                for i, trade in enumerate(self.active_trades, 1):
                    message += f"""
Trade {i}:
- Entry Order: {trade['entry_order_id']}
- Entry Price: ${trade['entry_price']:.2f}
- Quantity: {trade['quantity']:.8f} BTC
- Take Profit: ${trade['take_profit_price']:.2f}
- Stop Loss: ${trade['stop_loss_price']:.2f}
"""
                    if 'oco_order_id' in trade:
                        message += f"- OCO Order: {trade['oco_order_id']}\n"
                    else:
                        message += f"- TP Order: {trade.get('take_profit_order_id', 'N/A')}\n"
                        message += f"- SL Order: {trade.get('stop_loss_order_id', 'N/A')}\n"

                message += f"""
🔄 FULL CYCLE MONITORING ACTIVE
Monitoring until all TP or SL hit...
Real money test trades active! 💰
"""

            elif event_type == "MONITORING_UPDATE":
                completed = len([t for t in self.active_trades if 'result' in t])
                total = len(self.active_trades)

                message = f"""
📊 MONITORING UPDATE

Progress: {completed}/{total} trades completed
"""

                if completed > 0:
                    wins = len([t for t in self.active_trades if t.get('result') == 'WIN'])
                    losses = len([t for t in self.active_trades if t.get('result') == 'LOSS'])
                    total_pnl = sum(t.get('pnl', 0) for t in self.active_trades if 'pnl' in t)

                    message += f"""
Results so far:
- Wins: {wins}
- Losses: {losses}
- Total P&L: ${total_pnl:.4f}
"""

                message += "\nContinuing to monitor remaining trades..."

            elif event_type == "ALL_COMPLETED":
                completed_trades = [t for t in self.active_trades if 'result' in t]
                wins = len([t for t in completed_trades if t.get('result') == 'WIN'])
                losses = len([t for t in completed_trades if t.get('result') == 'LOSS'])
                total_pnl = sum(t.get('pnl', 0) for t in completed_trades)

                message = f"""
🎉 ALL TRADES COMPLETED!

📊 FINAL RESULTS:
- Total Trades: {len(completed_trades)}
- Wins: {wins}
- Losses: {losses}
- Win Rate: {(wins/len(completed_trades)*100):.1f}%
- Total P&L: ${total_pnl:.4f}

🎯 SYSTEM VALIDATION COMPLETE!
✅ Real money execution: CONFIRMED
✅ Full cycle completion: VERIFIED
✅ TP/SL functionality: VALIDATED

🚀 READY FOR LIVE DEPLOYMENT!
"""

            self.telegram_bot.send_message(message)
            logger.info(f"📱 Telegram notification sent: {event_type}")

        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")

    def save_completion_results(self, completed_trades):
        """Save completion results"""
        try:
            results = {
                'test_type': 'Complete Existing Trades Full Cycle',
                'timestamp': datetime.now().isoformat(),
                'total_trades': len(completed_trades),
                'wins': len([t for t in completed_trades if t.get('result') == 'WIN']),
                'losses': len([t for t in completed_trades if t.get('result') == 'LOSS']),
                'total_pnl': sum(t.get('pnl', 0) for t in completed_trades),
                'completed_trades': completed_trades,
                'system_validation': {
                    'real_money_execution': 'CONFIRMED',
                    'full_cycle_completion': 'VERIFIED',
                    'tp_sl_functionality': 'VALIDATED',
                    'order_management': 'OPERATIONAL',
                    'monitoring_system': 'COMPLETE'
                },
                'ready_for_live_trading': True
            }

            filename = f'completed_trades_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"📄 Results saved to {filename}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")

    def run_completion(self):
        """Run complete existing trades to completion"""
        try:
            logger.info("🚀 STARTING EXISTING TRADES COMPLETION")
            logger.info("Using existing BTC positions for full cycle completion")
            logger.info("="*80)

            # Step 1: Initialize connections
            if not self.initialize_connections():
                logger.error("❌ Connection initialization failed")
                return False

            # Step 2: Verify existing positions
            if not self.verify_existing_positions():
                logger.error("❌ Position verification failed")
                return False

            # Step 3: Place OCO orders for all trades
            if not self.place_all_oco_orders():
                logger.error("❌ OCO order placement failed")
                return False

            # Step 4: Monitor until all trades complete
            if not self.monitor_all_trades():
                logger.error("❌ Trade monitoring failed or timeout")
                return False

            # Success!
            logger.info("🎉 ALL EXISTING TRADES COMPLETED SUCCESSFULLY!")
            return True

        except Exception as e:
            logger.error(f"Completion execution failed: {e}")
            return False

def main():
    """Main execution"""
    print("🎯 COMPLETE EXISTING TRADES TO FULL CYCLE")
    print("Using existing BTC positions from previous entry orders")
    print("Place OCO orders and monitor until TP or SL hit")
    print("="*80)
    print("📋 EXISTING POSITIONS:")
    print("  Entry Order 1: 46196674337 (0.00008 BTC @ $119,733.06)")
    print("  Entry Order 2: 46196764679 (0.00008 BTC @ $119,706.84)")
    print("="*80)
    print("⚠️  This will place REAL exit orders and monitor completion!")
    print("📋 All order numbers will be provided for Binance app verification")
    print("="*80)

    try:
        # Initialize completion executor
        executor = CompleteExistingTrades()

        # Run completion
        if executor.run_completion():
            print("\n🎉 SUCCESS: ALL EXISTING TRADES COMPLETED!")
            print("Full cycle validation complete - ready for live deployment")

            # Display results
            completed_trades = [t for t in executor.active_trades if 'result' in t]
            if completed_trades:
                wins = len([t for t in completed_trades if t.get('result') == 'WIN'])
                losses = len([t for t in completed_trades if t.get('result') == 'LOSS'])
                total_pnl = sum(t.get('pnl', 0) for t in completed_trades)

                print(f"\n📊 FINAL RESULTS:")
                print(f"  Total Trades: {len(completed_trades)}")
                print(f"  Wins: {wins}")
                print(f"  Losses: {losses}")
                print(f"  Win Rate: {(wins/len(completed_trades)*100):.1f}%")
                print(f"  Total P&L: ${total_pnl:.4f}")

                print(f"\n📋 ALL ORDER NUMBERS:")
                for i, trade in enumerate(completed_trades, 1):
                    print(f"  Trade {i}:")
                    print(f"    Entry Order: {trade['entry_order_id']}")
                    if 'oco_order_id' in trade:
                        print(f"    OCO Order: {trade['oco_order_id']}")
                    else:
                        print(f"    TP Order: {trade.get('take_profit_order_id', 'N/A')}")
                        print(f"    SL Order: {trade.get('stop_loss_order_id', 'N/A')}")
                    print(f"    Exit Order: {trade.get('exit_order_id', 'N/A')}")
                    print(f"    Result: {trade.get('result', 'Unknown')} ({trade.get('result_type', 'Unknown')})")

                print("\n✅ SYSTEM VALIDATION COMPLETE:")
                print("✅ Real money execution: CONFIRMED")
                print("✅ Full cycle completion: VERIFIED")
                print("✅ TP/SL functionality: VALIDATED")
                print("✅ Order management: OPERATIONAL")
                print("✅ Monitoring system: COMPLETE")

                print("\n🚀 SYSTEM IS READY FOR LIVE TRADING DEPLOYMENT!")

        else:
            print("\n❌ FAILED: Could not complete existing trades")
            print("Check complete_existing_trades.log for details")

    except Exception as e:
        print(f"\nCRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
