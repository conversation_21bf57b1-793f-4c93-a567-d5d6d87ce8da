#!/usr/bin/env python3
"""
Test TCN-CNN-PPO Model Loading
Verify the model loads correctly before backtesting
"""

import sys
import os
import logging

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_loading():
    """Test if TCN-CNN-PPO model loads correctly"""
    try:
        logger.info("🧪 Testing TCN-CNN-PPO model loading...")
        
        # Validate credentials
        if not validate_all_credentials():
            logger.error("❌ Credential validation failed")
            return False
        
        # Initialize signal generator (this loads the model)
        signal_generator = GridAwareSignalGenerator()
        
        # Check if model loaded successfully
        if hasattr(signal_generator, 'model_loaded') and signal_generator.model_loaded:
            logger.info("✅ TCN-CNN-PPO model loaded successfully")
            
            # Test signal generation
            logger.info("🧪 Testing signal generation...")
            signal_data = signal_generator.generate_signal()
            
            logger.info(f"📊 Test Signal: {signal_data['signal']}")
            logger.info(f"🎯 Test Confidence: {signal_data.get('confidence', 0):.3f}")
            logger.info(f"📋 Test Reason: {signal_data.get('reason', 'N/A')}")
            
            if signal_data.get('confidence', 0) > 0:
                logger.info("✅ Model is generating valid signals with confidence")
                return True
            else:
                logger.warning("⚠️ Model loaded but generating zero confidence")
                return False
        else:
            logger.error("❌ TCN-CNN-PPO model failed to load")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model loading test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TCN-CNN-PPO MODEL LOADING TEST")
    print("=" * 50)
    
    if test_model_loading():
        print("✅ Model loading test PASSED")
        print("🚀 Ready for 24-hour backtest")
        return True
    else:
        print("❌ Model loading test FAILED")
        print("🔧 Model needs retraining or fixing")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
