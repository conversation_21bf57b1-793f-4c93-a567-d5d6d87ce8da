#!/usr/bin/env python3
"""
Quick Hyperparameter Tuning for Ensemble TCN-CNN-PPO
Optimize for: Composite Score × Net Profit (User Preference)
Reduced epochs for faster results
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime
from torch.utils.data import DataLoader, TensorDataset
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickHyperparameterTuner:
    """Quick hyperparameter tuning with reduced search space"""
    
    def __init__(self):
        self.best_reward = 0.0
        self.best_config = None
        self.results_history = []
        
        # Reduced search space for faster tuning
        self.search_space = {
            'tcn_channels': [[32, 64], [64, 128], [128, 256]],
            'cnn_channels': [[16, 32], [32, 64], [64, 128]],
            'ppo_hidden_dims': [[128], [256], [512]],
            'learning_rates': {
                'tcn': [0.001, 0.01],
                'cnn': [0.001, 0.01], 
                'ppo': [0.0003, 0.001],
                'ensemble': [0.01, 0.1]
            },
            'dropout_rates': [0.1, 0.2],
            'batch_sizes': [32, 64],
            'sequence_lengths': [30, 60],
            'epochs': [10, 20]  # Reduced for speed
        }
        
        logger.info("🎯 Quick Hyperparameter Tuner Initialized")
        logger.info("🎯 Optimization Target: Composite Score × Net Profit")
    
    def generate_configs(self, num_trials=5):
        """Generate random configurations"""
        configs = []
        
        for trial in range(num_trials):
            config = {
                'tcn_channels': self.search_space['tcn_channels'][np.random.randint(len(self.search_space['tcn_channels']))],
                'cnn_channels': self.search_space['cnn_channels'][np.random.randint(len(self.search_space['cnn_channels']))],
                'ppo_hidden_dims': self.search_space['ppo_hidden_dims'][np.random.randint(len(self.search_space['ppo_hidden_dims']))],
                'learning_rates': {
                    'tcn': self.search_space['learning_rates']['tcn'][np.random.randint(len(self.search_space['learning_rates']['tcn']))],
                    'cnn': self.search_space['learning_rates']['cnn'][np.random.randint(len(self.search_space['learning_rates']['cnn']))],
                    'ppo': self.search_space['learning_rates']['ppo'][np.random.randint(len(self.search_space['learning_rates']['ppo']))],
                    'ensemble': self.search_space['learning_rates']['ensemble'][np.random.randint(len(self.search_space['learning_rates']['ensemble']))]
                },
                'dropout_rate': self.search_space['dropout_rates'][np.random.randint(len(self.search_space['dropout_rates']))],
                'batch_size': self.search_space['batch_sizes'][np.random.randint(len(self.search_space['batch_sizes']))],
                'sequence_length': self.search_space['sequence_lengths'][np.random.randint(len(self.search_space['sequence_lengths']))],
                'epochs': self.search_space['epochs'][np.random.randint(len(self.search_space['epochs']))],
                'trial_id': trial
            }
            configs.append(config)
        
        return configs
    
    def calculate_reward(self, accuracy, net_profit):
        """Calculate simplified reward: accuracy × net_profit"""
        # Simplified composite score approximation
        composite_score = min(accuracy / 0.60, 1.0)  # Normalize to 60% target
        reward = composite_score * net_profit
        return reward, composite_score
    
    def load_data(self):
        """Load data with backward split"""
        try:
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['year'] = df['datetime'].dt.year
            
            # Backward split - reduced data for speed
            train_data = df[df['year'].isin([2021, 2022])].iloc[::10].copy()  # Every 10th sample
            val_data = df[df['year'].isin([2023])].iloc[::10].copy()
            test_data = df[df['year'].isin([2024])].iloc[::10].copy()
            
            logger.info(f"📊 Quick Training: {len(train_data)} samples")
            logger.info(f"📊 Quick Validation: {len(val_data)} samples")
            logger.info(f"📊 Quick Backtest: {len(test_data)} samples")
            
            return train_data, val_data, test_data
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            return None, None, None
    
    def build_quick_model(self, config):
        """Build simplified ensemble model"""
        class QuickEnsembleModel(nn.Module):
            def __init__(self, config):
                super(QuickEnsembleModel, self).__init__()
                
                # Simplified TCN
                self.tcn = nn.Sequential(
                    nn.Conv1d(4, config['tcn_channels'][0], 3, padding=1),
                    nn.ReLU(),
                    nn.Dropout(config['dropout_rate']),
                    nn.Conv1d(config['tcn_channels'][0], config['tcn_channels'][1], 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(config['tcn_channels'][1], 3)
                )
                
                # Simplified CNN
                self.cnn = nn.Sequential(
                    nn.Conv1d(4, config['cnn_channels'][0], 5, padding=2),
                    nn.ReLU(),
                    nn.Dropout(config['dropout_rate']),
                    nn.Conv1d(config['cnn_channels'][0], config['cnn_channels'][1], 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(config['cnn_channels'][1], 3)
                )
                
                # Simplified PPO
                self.ppo = nn.Sequential(
                    nn.Linear(4, config['ppo_hidden_dims'][0]),
                    nn.ReLU(),
                    nn.Dropout(config['dropout_rate']),
                    nn.Linear(config['ppo_hidden_dims'][0], 3)
                )
                
                # Ensemble weights
                self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
            
            def forward(self, x):
                x_transposed = x.transpose(1, 2)
                
                tcn_pred = torch.softmax(self.tcn(x_transposed), dim=1)
                cnn_pred = torch.softmax(self.cnn(x_transposed), dim=1)
                ppo_pred = torch.softmax(self.ppo(x[:, -1, :]), dim=1)
                
                normalized_weights = torch.softmax(self.ensemble_weights, dim=0)
                ensemble_pred = (normalized_weights[0] * tcn_pred + 
                               normalized_weights[1] * cnn_pred + 
                               normalized_weights[2] * ppo_pred)
                
                return ensemble_pred
        
        return QuickEnsembleModel(config)
    
    def prepare_data_loader(self, data, config):
        """Prepare data loader"""
        sequences = []
        targets = []
        
        seq_len = config['sequence_length']
        
        for i in range(seq_len, len(data)):
            sequence = data.iloc[i-seq_len:i][['open', 'high', 'low', 'close']].values
            
            if i < len(data) - 1:
                current_grid = data.iloc[i]['grid_level']
                next_grid = data.iloc[i+1]['grid_level']
                
                if next_grid > current_grid:
                    target = 0  # UP
                elif next_grid < current_grid:
                    target = 1  # DOWN
                else:
                    target = 2  # HOLD
            else:
                target = 2
            
            sequences.append(sequence)
            targets.append(target)
        
        X = torch.FloatTensor(np.array(sequences))
        y = torch.LongTensor(np.array(targets))
        
        dataset = TensorDataset(X, y)
        return DataLoader(dataset, batch_size=config['batch_size'], shuffle=True)
    
    def train_and_evaluate(self, config, train_data, val_data, test_data):
        """Quick train and evaluate"""
        try:
            logger.info(f"🔍 Trial {config['trial_id']}: Quick training...")
            
            # Build model
            model = self.build_quick_model(config)
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model.to(device)
            
            # Prepare data
            train_loader = self.prepare_data_loader(train_data, config)
            val_loader = self.prepare_data_loader(val_data, config)
            test_loader = self.prepare_data_loader(test_data, config)
            
            # Quick training
            optimizer = optim.Adam(model.parameters(), lr=config['learning_rates']['ensemble'])
            criterion = nn.CrossEntropyLoss()
            
            for epoch in range(config['epochs']):
                model.train()
                for data, targets in train_loader:
                    data, targets = data.to(device), targets.to(device)
                    optimizer.zero_grad()
                    outputs = model(data)
                    loss = criterion(outputs, targets)
                    loss.backward()
                    optimizer.step()
            
            # Evaluate
            model.eval()
            correct = 0
            total = 0
            
            with torch.no_grad():
                for data, targets in test_loader:
                    data, targets = data.to(device), targets.to(device)
                    outputs = model(data)
                    _, predicted = torch.max(outputs.data, 1)
                    total += targets.size(0)
                    correct += (predicted == targets).sum().item()
            
            accuracy = correct / total if total > 0 else 0
            
            # Simulate net profit (simplified)
            net_profit = accuracy * 100 - (1 - accuracy) * 40  # 2.5:1 risk-reward approximation
            
            # Calculate reward
            reward, composite_score = self.calculate_reward(accuracy, net_profit)
            
            result = {
                'trial_id': config['trial_id'],
                'config': config,
                'accuracy': accuracy,
                'net_profit': net_profit,
                'composite_score': composite_score,
                'reward': reward
            }
            
            logger.info(f"✅ Trial {config['trial_id']} completed:")
            logger.info(f"   Accuracy: {accuracy:.4f}")
            logger.info(f"   Net Profit: {net_profit:.2f}")
            logger.info(f"   Reward: {reward:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Trial {config['trial_id']} failed: {e}")
            return {'trial_id': config['trial_id'], 'reward': 0.0, 'error': str(e)}
    
    def run_quick_tuning(self, num_trials=5):
        """Run quick hyperparameter tuning"""
        logger.info("🚀 Starting Quick Hyperparameter Tuning")
        logger.info(f"🔍 Number of Trials: {num_trials}")
        
        # Load data
        train_data, val_data, test_data = self.load_data()
        if train_data is None:
            return None, 0.0
        
        # Generate configs
        configs = self.generate_configs(num_trials)
        
        # Run trials
        for config in configs:
            result = self.train_and_evaluate(config, train_data, val_data, test_data)
            self.results_history.append(result)
            
            if result['reward'] > self.best_reward:
                self.best_reward = result['reward']
                self.best_config = config
                logger.info(f"🎉 NEW BEST REWARD: {self.best_reward:.4f}")
        
        # Results
        logger.info("\n" + "="*60)
        logger.info("📊 QUICK HYPERPARAMETER TUNING RESULTS")
        logger.info("="*60)
        
        sorted_results = sorted(self.results_history, key=lambda x: x.get('reward', 0), reverse=True)
        
        for i, result in enumerate(sorted_results[:3]):
            logger.info(f"#{i+1} Trial {result['trial_id']}: Reward={result.get('reward', 0):.4f}")
            if 'accuracy' in result:
                logger.info(f"    Accuracy: {result['accuracy']:.4f}")
                logger.info(f"    Net Profit: {result['net_profit']:.2f}")
        
        logger.info(f"\n🏆 BEST CONFIGURATION:")
        if self.best_config:
            for key, value in self.best_config.items():
                if key != 'trial_id':
                    logger.info(f"  {key}: {value}")
        
        # Save best config
        with open('quick_best_config.json', 'w') as f:
            json.dump({
                'best_reward': self.best_reward,
                'best_config': self.best_config,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, default=str)
        
        logger.info("="*60)
        
        return self.best_config, self.best_reward

def main():
    """Main execution"""
    print("🎯 QUICK ENSEMBLE HYPERPARAMETER TUNING")
    print("🎯 Optimization Target: Composite Score × Net Profit")
    print("="*60)
    
    tuner = QuickHyperparameterTuner()
    best_config, best_reward = tuner.run_quick_tuning(num_trials=5)
    
    if best_config:
        print(f"\n🎉 QUICK TUNING COMPLETED")
        print(f"🏆 Best Reward: {best_reward:.4f}")
        print(f"💾 Configuration saved to: quick_best_config.json")
    else:
        print("❌ Quick tuning failed")

if __name__ == "__main__":
    main()
