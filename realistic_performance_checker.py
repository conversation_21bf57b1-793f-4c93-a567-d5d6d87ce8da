#!/usr/bin/env python3
"""
Realistic Performance Checker - Verify if results are fake
Check for mathematical consistency and realistic trading patterns
"""

import numpy as np
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_composite_score_calculation(win_rate, trades_per_day, profit_factor=2.0):
    """Verify composite score calculation with given parameters"""
    
    logger.info("🧮 VERIFYING COMPOSITE SCORE CALCULATION...")
    
    # EXACT master document formula
    sortino_component = min(1.0, profit_factor / 3.0) * 0.28
    calmar_component = min(1.0, profit_factor / 3.0) * 0.22  
    profit_factor_component = min(1.0, profit_factor / 1.5) * 0.20
    win_rate_component = min(1.0, win_rate / 60.0) * 0.15
    drawdown_component = 0.10  # Fixed
    frequency_component = min(1.0, trades_per_day / 8.0) * 0.05
    
    composite_score = (sortino_component + calmar_component + profit_factor_component + 
                      win_rate_component + drawdown_component + frequency_component)
    
    logger.info(f"📊 COMPOSITE SCORE BREAKDOWN:")
    logger.info(f"   Win Rate: {win_rate:.1f}% → Component: {win_rate_component:.3f}")
    logger.info(f"   Trades/Day: {trades_per_day:.1f} → Component: {frequency_component:.3f}")
    logger.info(f"   Profit Factor: {profit_factor:.1f} → Sortino: {sortino_component:.3f}")
    logger.info(f"   Profit Factor: {profit_factor:.1f} → Calmar: {calmar_component:.3f}")
    logger.info(f"   Profit Factor: {profit_factor:.1f} → PF Component: {profit_factor_component:.3f}")
    logger.info(f"   Drawdown: Fixed → Component: {drawdown_component:.3f}")
    logger.info(f"   TOTAL COMPOSITE SCORE: {composite_score:.3f}")
    
    return composite_score

def check_trading_frequency_realism(trades_per_day, data_frequency="1H"):
    """Check if trading frequency is realistic"""
    
    logger.info("⏰ CHECKING TRADING FREQUENCY REALISM...")
    
    if data_frequency == "1H":
        max_possible_trades_per_day = 24  # 24 hours
        theoretical_max = max_possible_trades_per_day * 0.5  # 50% of hours trading
        
        logger.info(f"📊 FREQUENCY ANALYSIS:")
        logger.info(f"   Data Frequency: {data_frequency}")
        logger.info(f"   Max Possible Trades/Day: {max_possible_trades_per_day}")
        logger.info(f"   Theoretical Realistic Max: {theoretical_max}")
        logger.info(f"   Reported Trades/Day: {trades_per_day:.1f}")
        
        if trades_per_day > theoretical_max:
            logger.warning(f"🚨 UNREALISTIC FREQUENCY: {trades_per_day:.1f} > {theoretical_max}")
            return False, "UNREALISTIC_HIGH_FREQUENCY"
        elif trades_per_day > max_possible_trades_per_day:
            logger.warning(f"🚨 IMPOSSIBLE FREQUENCY: {trades_per_day:.1f} > {max_possible_trades_per_day}")
            return False, "IMPOSSIBLE_FREQUENCY"
        else:
            logger.info(f"✅ REALISTIC FREQUENCY: {trades_per_day:.1f} ≤ {theoretical_max}")
            return True, "REALISTIC"
    
    return False, "UNKNOWN_DATA_FREQUENCY"

def check_grid_tolerance_realism(grid_tolerance, expected_signals_per_day):
    """Check if grid tolerance produces realistic signal frequency"""
    
    logger.info("📐 CHECKING GRID TOLERANCE REALISM...")
    
    # With 0.25% grid spacing and 1% tolerance, almost every price is "at grid"
    grid_spacing = 0.0025  # 0.25%
    tolerance_ratio = grid_tolerance / grid_spacing
    
    logger.info(f"📊 GRID ANALYSIS:")
    logger.info(f"   Grid Spacing: {grid_spacing:.4f} ({grid_spacing*100:.2f}%)")
    logger.info(f"   Grid Tolerance: {grid_tolerance:.4f} ({grid_tolerance*100:.2f}%)")
    logger.info(f"   Tolerance/Spacing Ratio: {tolerance_ratio:.1f}x")
    
    if tolerance_ratio >= 4.0:  # 1% tolerance vs 0.25% spacing = 4x
        logger.warning(f"🚨 TOLERANCE TOO HIGH: {tolerance_ratio:.1f}x spacing")
        logger.warning(f"🚨 RESULT: Almost every price appears 'at grid level'")
        return False, "TOLERANCE_TOO_HIGH"
    elif tolerance_ratio >= 2.0:
        logger.warning(f"⚠️ TOLERANCE HIGH: {tolerance_ratio:.1f}x spacing")
        return False, "TOLERANCE_HIGH"
    else:
        logger.info(f"✅ REASONABLE TOLERANCE: {tolerance_ratio:.1f}x spacing")
        return True, "REASONABLE"

def analyze_reported_results():
    """Analyze the reported training results for consistency"""
    
    logger.info("🔍 ANALYZING REPORTED RESULTS...")
    
    # Reported results
    results = {
        'training': {
            'trades': 16158,
            'win_rate': 63.5,
            'trades_per_day': 22.1,
            'composite': 0.481,
            'net_profit': 20.06,
            'corrected_reward': 9.65
        },
        'out_of_sample': {
            'trades': 8633,
            'win_rate': 73.1,
            'trades_per_day': 23.6,
            'composite': 0.411,
            'net_profit': 0.47,
            'corrected_reward': 0.19
        },
        'backtest': {
            'trades': 3988,
            'win_rate': 70.1,
            'trades_per_day': 23.9,
            'composite': 0.425,
            'net_profit': -2.70,
            'corrected_reward': 0.00
        }
    }
    
    inconsistencies = []
    
    for phase_name, data in results.items():
        logger.info(f"\n📊 ANALYZING {phase_name.upper()} PHASE:")
        
        # Check composite score consistency
        expected_composite = verify_composite_score_calculation(
            data['win_rate'], 
            data['trades_per_day']
        )
        
        composite_diff = abs(expected_composite - data['composite'])
        if composite_diff > 0.1:  # More than 0.1 difference
            logger.error(f"❌ COMPOSITE INCONSISTENCY: Expected {expected_composite:.3f}, Got {data['composite']:.3f}")
            inconsistencies.append(f"{phase_name}: Composite score inconsistent")
        else:
            logger.info(f"✅ COMPOSITE CONSISTENT: {data['composite']:.3f} ≈ {expected_composite:.3f}")
        
        # Check trading frequency realism
        freq_realistic, freq_reason = check_trading_frequency_realism(data['trades_per_day'])
        if not freq_realistic:
            logger.error(f"❌ FREQUENCY UNREALISTIC: {freq_reason}")
            inconsistencies.append(f"{phase_name}: {freq_reason}")
        
        # Check reward calculation
        expected_reward = data['composite'] * max(0, data['net_profit'])
        reward_diff = abs(expected_reward - data['corrected_reward'])
        if reward_diff > 0.01:
            logger.error(f"❌ REWARD INCONSISTENCY: Expected {expected_reward:.2f}, Got {data['corrected_reward']:.2f}")
            inconsistencies.append(f"{phase_name}: Reward calculation error")
        else:
            logger.info(f"✅ REWARD CONSISTENT: {data['corrected_reward']:.2f}")
    
    # Check grid tolerance
    grid_realistic, grid_reason = check_grid_tolerance_realism(0.01, 22.0)  # 1% tolerance, ~22 trades/day
    if not grid_realistic:
        logger.error(f"❌ GRID TOLERANCE UNREALISTIC: {grid_reason}")
        inconsistencies.append(f"Grid tolerance: {grid_reason}")
    
    return inconsistencies

def main():
    """Main verification function"""
    print("🔍 REALISTIC PERFORMANCE CHECKER")
    print("=" * 60)
    print("🎯 Checking for fake/inconsistent results...")
    print("=" * 60)
    
    # Analyze reported results
    inconsistencies = analyze_reported_results()
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY:")
    
    if inconsistencies:
        print("❌ INCONSISTENCIES DETECTED:")
        for issue in inconsistencies:
            print(f"   • {issue}")
        print("\n🚨 CONCLUSION: Results appear to be FAKE/INCONSISTENT")
        print("🔧 RECOMMENDATION: Fix simulation logic and recalculate")
        return False
    else:
        print("✅ NO MAJOR INCONSISTENCIES DETECTED")
        print("🎯 CONCLUSION: Results appear mathematically consistent")
        return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
