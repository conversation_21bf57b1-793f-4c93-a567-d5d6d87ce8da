#!/usr/bin/env python3
"""
Security Validation Script
Validates that all credentials are properly secured and no hardcoded secrets exist
"""

import os
import json
import re
import sys
from pathlib import Path

def check_env_file():
    """Check if .env file exists and contains required variables"""
    print("🔍 Checking .env file...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("💡 Copy .env.template to .env and fill in your credentials")
        return False
    
    required_vars = [
        'BINANCE_API_KEY',
        'BINANCE_API_SECRET', 
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    missing_vars = []
    placeholder_vars = []
    
    with open('.env', 'r') as f:
        content = f.read()
        
    for var in required_vars:
        if var not in content:
            missing_vars.append(var)
        elif f'{var}=your_' in content or f'{var}=LOADED_FROM_ENV' in content:
            placeholder_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
        
    if placeholder_vars:
        print(f"⚠️ Placeholder values found for: {', '.join(placeholder_vars)}")
        print("💡 Please set actual values in .env file")
        return False
    
    print("✅ .env file contains all required variables")
    return True

def check_config_files():
    """Check that config files don't contain hardcoded credentials"""
    print("\n🔍 Checking configuration files...")
    
    config_files = [
        'binance_isolated_margin_config.json',
        '06_telegram_system/telegram_config.json'
    ]
    
    issues_found = False
    
    for config_file in config_files:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                content = f.read()
                
            # Check for potential API keys (long alphanumeric strings)
            api_key_pattern = r'[A-Za-z0-9]{50,}'
            matches = re.findall(api_key_pattern, content)
            
            # Filter out "LOADED_FROM_ENV" and other safe strings
            suspicious_matches = [m for m in matches if m not in ['LOADED_FROM_ENV'] and not m.startswith('your_')]
            
            if suspicious_matches:
                print(f"⚠️ Potential hardcoded credentials in {config_file}")
                issues_found = True
            else:
                print(f"✅ {config_file} - No hardcoded credentials found")
        else:
            print(f"⚠️ {config_file} not found")
    
    return not issues_found

def check_python_files():
    """Check Python files for hardcoded credentials"""
    print("\n🔍 Checking Python files for hardcoded credentials...")
    
    # Patterns that might indicate hardcoded credentials
    suspicious_patterns = [
        r'api_key\s*=\s*["\'][A-Za-z0-9]{50,}["\']',
        r'api_secret\s*=\s*["\'][A-Za-z0-9]{50,}["\']',
        r'bot_token\s*=\s*["\'][0-9]+:[A-Za-z0-9_-]+["\']',
        r'["\'][0-9]{10}:[A-Za-z0-9_-]{35}["\']',  # Telegram bot token pattern
    ]
    
    python_files = list(Path('.').rglob('*.py'))
    issues_found = False
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern in suspicious_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"⚠️ Potential hardcoded credential in {py_file}")
                    issues_found = True
                    break
                    
        except Exception as e:
            print(f"⚠️ Could not read {py_file}: {e}")
    
    if not issues_found:
        print("✅ No hardcoded credentials found in Python files")
    
    return not issues_found

def test_credential_loading():
    """Test that credentials can be loaded properly"""
    print("\n🔍 Testing credential loading...")
    
    try:
        sys.path.append('shared_config')
        from secure_credentials import validate_all_credentials
        
        if validate_all_credentials():
            print("✅ All credentials loaded successfully")
            return True
        else:
            print("❌ Credential validation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing credential loading: {e}")
        return False

def check_gitignore():
    """Check that .gitignore properly protects sensitive files"""
    print("\n🔍 Checking .gitignore protection...")
    
    if not os.path.exists('.gitignore'):
        print("❌ .gitignore file not found!")
        return False
    
    with open('.gitignore', 'r') as f:
        gitignore_content = f.read()
    
    required_patterns = ['.env', '*.key', '*.secret']
    missing_patterns = []
    
    for pattern in required_patterns:
        if pattern not in gitignore_content:
            missing_patterns.append(pattern)
    
    if missing_patterns:
        print(f"⚠️ Missing .gitignore patterns: {', '.join(missing_patterns)}")
        return False
    
    print("✅ .gitignore properly protects sensitive files")
    return True

def main():
    """Run all security validation checks"""
    print("🔒 SECURITY VALIDATION REPORT")
    print("=" * 50)
    
    checks = [
        ("Environment File", check_env_file),
        ("Configuration Files", check_config_files),
        ("Python Files", check_python_files),
        ("Credential Loading", test_credential_loading),
        ("Git Protection", check_gitignore)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 50)
    print("📊 SECURITY VALIDATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name:.<30} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎯 ALL SECURITY CHECKS PASSED!")
        print("✅ System is ready for secure deployment")
    else:
        print("🚨 SECURITY ISSUES FOUND!")
        print("❌ Fix issues before deployment")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
