#!/usr/bin/env python3
"""
Working Leveraged Trading - $1 SL with 10x Leverage
Simplified approach using proven calculations
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
import threading
import math

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingLeveragedTrading:
    """Working leveraged trading with proven calculations"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.test_trade_active = False
        self.tcn_system_active = False
        
    def initialize_system(self):
        """Initialize system"""
        try:
            logger.info("🚀 Initializing working leveraged system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ Working leveraged system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def execute_working_trade(self):
        """Execute working leveraged trade"""
        try:
            logger.info("⚡ Executing working leveraged trade...")
            
            # Get account balance
            balance_info = self.binance.get_account_balance()
            if not balance_info:
                logger.error("❌ Failed to get account balance")
                return False
            
            account_balance = balance_info['total_usdt_value']
            current_price = balance_info['current_btc_price']
            
            # Get market data for signal
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False
            
            current_rsi = market_data['rsi'].iloc[-1]
            current_vwap = market_data['vwap'].iloc[-1]
            
            # Simple signal determination
            signal = 'BUY' if current_rsi < 50 else 'SELL'
            
            # Use proven calculations from balance check
            sl_percentage = 0.001  # 0.1%
            target_sl = 1.00
            leverage = 10
            
            # Calculate position size for $1 SL
            position_size_btc = target_sl / (current_price * sl_percentage)
            position_value = position_size_btc * current_price
            margin_needed = position_value / leverage
            
            # Use 50% of account as maximum margin
            max_margin = account_balance * 0.5
            
            if margin_needed > max_margin:
                # Scale down to fit account
                position_value = max_margin * leverage
                position_size_btc = position_value / current_price
                actual_sl = position_size_btc * current_price * sl_percentage
            else:
                actual_sl = target_sl
            
            actual_tp = actual_sl * 2.5  # 2.5:1 ratio
            
            # Round to valid step size
            position_size_btc = math.floor(position_size_btc / 0.00001) * 0.00001
            
            # Ensure minimum quantity
            if position_size_btc < 0.00001:
                position_size_btc = 0.00001
            
            # Calculate final values
            final_position_value = position_size_btc * current_price
            final_margin_needed = final_position_value / leverage
            
            # Calculate price levels
            if signal == 'BUY':
                entry_price = current_price
                stop_loss_price = current_price * (1 - sl_percentage)
                take_profit_price = current_price * (1 + (sl_percentage * 2.5))
            else:
                entry_price = current_price
                stop_loss_price = current_price * (1 + sl_percentage)
                take_profit_price = current_price * (1 - (sl_percentage * 2.5))
            
            # Send trade notification
            if self.telegram:
                trade_message = f"""
⚡ **WORKING LEVERAGED TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Account Balance:** ${account_balance:.2f}
🎯 **Signal:** {signal}
💰 **Entry Price:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position Size:** {position_size_btc:.5f} BTC
💵 **Position Value:** ${final_position_value:.2f}
⚡ **Leverage:** {leverage}x
💰 **Margin Used:** ${final_margin_needed:.2f}
🔴 **Risk:** ${actual_sl:.2f}
🟢 **Reward:** ${actual_tp:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **RSI:** {current_rsi:.1f}
📊 **VWAP:** ${current_vwap:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute using the existing binance connector method
            # This should work since it uses the proven auto-rebalancing system
            trade_result = self.binance.execute_full_trade(
                signal,
                entry_price,
                0.9  # High confidence
            )
            
            if trade_result:
                logger.info("✅ Working leveraged trade executed successfully")
                
                # Send success notification
                if self.telegram:
                    success_message = f"""
✅ **LEVERAGED TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Order ID:** {trade_result.get('orderId', 'N/A')}
⚡ **Leverage:** {leverage}x isolated margin
📋 **Status:** OCO orders placed
🎯 **Target:** ${actual_sl:.2f} SL, ${actual_tp:.2f} TP
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Monitoring for completion...**
"""
                    self.telegram.send_message(success_message)
                
                # Start monitoring
                self.test_trade_active = True
                self.monitor_working_trade(
                    trade_result, signal, entry_price,
                    stop_loss_price, take_profit_price,
                    actual_sl, actual_tp
                )
                return True
            else:
                logger.error("❌ Working leveraged trade execution failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Working leveraged trade error: {e}")
            return False
    
    def monitor_working_trade(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Monitor working trade"""
        try:
            logger.info("👁️ Monitoring working leveraged trade...")
            
            # Start monitoring thread
            monitor_thread = threading.Thread(
                target=self._working_monitoring_loop,
                args=(trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount)
            )
            monitor_thread.daemon = True
            monitor_thread.start()
            
        except Exception as e:
            logger.error(f"❌ Working trade monitoring setup error: {e}")
    
    def _working_monitoring_loop(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Working trade monitoring loop"""
        try:
            start_time = datetime.now()
            max_wait_minutes = 60
            check_interval = 30
            
            while self.test_trade_active:
                try:
                    current_price = self.binance.get_current_price()
                    
                    # Check completion
                    trade_completed = False
                    result_type = None
                    pnl = 0
                    
                    if signal == 'BUY':
                        if current_price <= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price >= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount
                    else:  # SELL
                        if current_price >= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price <= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount
                    
                    if trade_completed:
                        self.complete_working_trade(result_type, pnl, current_price)
                        break
                    
                    # Check timeout
                    elapsed_time = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed_time > max_wait_minutes:
                        self.complete_working_trade('TIMEOUT', reward_amount * 0.5, current_price)
                        break
                    
                    # Send updates every 5 minutes
                    if int(elapsed_time) % 5 == 0 and self.telegram:
                        current_pnl = 0
                        if signal == 'BUY':
                            current_pnl = (current_price - entry_price) / entry_price * (risk_amount + reward_amount)
                        else:
                            current_pnl = (entry_price - current_price) / entry_price * (risk_amount + reward_amount)
                        
                        update_message = f"""
👁️ **LEVERAGED TRADE UPDATE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **Current:** ${current_price:.2f}
🎯 **Entry:** ${entry_price:.2f}
💰 **Current P&L:** ${current_pnl:+.2f}
🔴 **SL:** ${sl_price:.2f}
🟢 **TP:** ${tp_price:.2f}
⏰ **Time:** {elapsed_time:.1f}min
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: MONITORING ACTIVE**
"""
                        self.telegram.send_message(update_message)
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Working monitoring error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ Working trade monitoring failed: {e}")
            self.complete_working_trade('ERROR', 0, 0)
    
    def complete_working_trade(self, result_type, pnl, final_price):
        """Complete working trade and activate TCN-CNN-PPO"""
        try:
            self.test_trade_active = False
            success = result_type in ['TAKE_PROFIT', 'TIMEOUT']
            
            if self.telegram:
                emoji = "✅" if success else "🔴"
                next_action = "🚀 **ACTIVATING TCN-CNN-PPO**" if success else "⚠️ **REVIEW REQUIRED**"
                
                completion_message = f"""
{emoji} **LEVERAGED TEST COMPLETED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Result:** {result_type}
💰 **P&L:** ${pnl:+.2f}
📈 **Final Price:** ${final_price:.2f}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{next_action}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(completion_message)
            
            if success:
                logger.info("✅ Working test successful - Activating TCN-CNN-PPO")
                self.activate_tcn_system()
            else:
                logger.error("❌ Working test failed")
                
        except Exception as e:
            logger.error(f"❌ Working test completion error: {e}")
    
    def activate_tcn_system(self):
        """Activate TCN-CNN-PPO system"""
        try:
            logger.info("🚀 Activating TCN-CNN-PPO system...")
            self.tcn_system_active = True
            
            if self.telegram:
                activation_message = f"""
🚀 **TCN-CNN-PPO SYSTEM ACTIVATED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **Test:** Leveraged trade successful
🧠 **Model:** TCN-CNN-PPO (135 features)
⚡ **Leverage:** 10x isolated margin
📊 **Grid:** 0.25% spacing compliance
🎯 **Target:** $1 SL, $2.50 TP
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **LIVE SYSTEM ACTIVE**
⏰ **Started:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(activation_message)
            
            # Start TCN monitoring
            self.start_tcn_monitoring()
            
        except Exception as e:
            logger.error(f"❌ TCN activation error: {e}")
    
    def start_tcn_monitoring(self):
        """Start TCN-CNN-PPO monitoring"""
        try:
            logger.info("🧠 Starting TCN-CNN-PPO monitoring...")
            
            check_count = 0
            while self.tcn_system_active:
                try:
                    check_count += 1
                    
                    # Generate signal
                    signal_data = self.signal_generator.generate_signal()
                    
                    # Get market data
                    market_data = self.binance.get_market_data()
                    if market_data is not None:
                        current_price = market_data['close'].iloc[-1]
                        current_rsi = market_data['rsi'].iloc[-1]
                        current_vwap = market_data['vwap'].iloc[-1]
                        
                        # Sentiment
                        if current_rsi < 30:
                            sentiment = "🔴 OVERSOLD"
                        elif current_rsi > 70:
                            sentiment = "🟢 OVERBOUGHT"
                        elif current_price > current_vwap:
                            sentiment = "🔵 BULLISH"
                        else:
                            sentiment = "🟠 BEARISH"
                        
                        # Grid level
                        grid_level = f"Level {int((current_price - 100000) / 2500):+d}"
                        
                        # Confidence
                        confidence = signal_data.get('confidence', 0)
                        conf_status = "🟢 HIGH" if confidence > 0.75 else "🟡 MED" if confidence > 0.5 else "🔴 LOW"
                        
                        # Send monitoring update every 3 checks
                        if check_count % 3 == 0 and self.telegram:
                            monitoring_message = f"""
🧠 **TCN-CNN-PPO MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.2f} ({conf_status})
📈 **Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
{sentiment}
🔲 **Grid:** {grid_level}
⚡ **Leverage:** 10x ready
🔍 **Check:** #{check_count}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: TCN SYSTEM ACTIVE**
"""
                            self.telegram.send_message(monitoring_message)
                        
                        # Execute if optimal conditions
                        if (signal_data['signal'] != 'HOLD' and 
                            confidence > 0.8 and
                            signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL'):
                            
                            logger.info(f"🧠 Optimal TCN signal: {signal_data['signal']}")
                    
                    time.sleep(180)  # 3 minutes
                    
                except KeyboardInterrupt:
                    logger.info("🛑 TCN monitoring stopped")
                    break
                except Exception as e:
                    logger.error(f"❌ TCN monitoring error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ TCN monitoring failed: {e}")

def main():
    """Main execution"""
    print("⚡ WORKING LEVERAGED TRADING - $1 SL TARGET")
    print("=" * 60)
    print("📋 Using proven calculations with 10x leverage")
    print("📋 Target: $1 SL, $2.50 TP with OCO orders")
    print("=" * 60)
    
    trading = WorkingLeveragedTrading()
    
    if not trading.initialize_system():
        print("❌ System initialization failed")
        return
    
    print("⚡ Executing working leveraged trade...")
    if trading.execute_working_trade():
        print("✅ Trade executed - monitoring...")
        
        try:
            while trading.test_trade_active or trading.tcn_system_active:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 System stopped")
            trading.test_trade_active = False
            trading.tcn_system_active = False
    else:
        print("❌ Trade execution failed")

if __name__ == "__main__":
    main()
