# Bitcoin Freedom Trading System - Git Ignore File
# This file prevents sensitive data from being committed to version control

# Environment Variables (CRITICAL - NEVER COMMIT)
.env
.env.local
.env.production
.env.staging

# API Keys and Secrets
**/api_keys.txt
**/secrets.txt
**/credentials.json
**/*_secret*
**/*_key*
*.secret
*.key

# Trading Data and Logs
*.log
logs/
trading_data/
backtest_results/
performance_data/

# Database Files
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
.venv/
.env/

# IDE Files
.vscode/
.idea/
*.swp
*.swo
*~

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
*.tmp
*.temp
temp/
tmp/

# Backup Files
*.backup
*.bak
*~
*.orig

# Security Scan Results
security_scan_results.json
rebuff_scan_results.json

# Model Files (if large)
*.h5
*.pkl
*.joblib
models/*.h5
models/*.pkl

# Configuration Overrides
config_override.py
local_config.py

# Test Coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebooks
.ipynb_checkpoints

# Documentation Build
docs/_build/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
# Security - Sensitive files
config.json
*.env
*.key
*.pem
*.p12
*.pfx
*_backup_*
*_security_backup_*
rebuff_scan_results_*.json
security_remediation.log
secrets.json
# Logs that might contain sensitive data
# Python cache
*.pyc
*.pyo

# Security patterns
*.env*
*.backup_*
security_fix.log
# Final Security Patterns
*.backup*
core_security_scan_*.json