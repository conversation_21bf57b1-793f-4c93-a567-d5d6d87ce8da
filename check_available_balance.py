#!/usr/bin/env python3
"""
Check actual available balance for trading
"""

import sys
sys.path.append('01_binance_connector')

def check_available_balance():
    try:
        from binance_real_money_connector import BinanceRealMoneyConnector
        
        connector = BinanceRealMoneyConnector()
        
        # Get detailed balance info
        balance_info = connector.get_isolated_margin_balance()
        print('DETAILED BALANCE BREAKDOWN:')
        print(f'USDT Balance: {balance_info["usdt_balance"]:.2f}')
        print(f'BTC Balance: {balance_info["btc_balance"]:.8f}')
        print(f'Total USD Value: {balance_info["total_usdt_value"]:.2f}')
        
        # Get account details directly
        account = connector.client.get_isolated_margin_account(symbols='BTCUSDT')
        print(f'\nDIRECT ACCOUNT QUERY:')
        for asset in account['assets']:
            if asset['symbol'] == 'BTCUSDT':
                print(f'Base Asset (BTC):')
                print(f'  Free: {asset["baseAsset"]["free"]}')
                print(f'  Locked: {asset["baseAsset"]["locked"]}')
                print(f'  Total: {asset["baseAsset"]["totalAsset"]}')
                print(f'Quote Asset (USDT):')
                print(f'  Free: {asset["quoteAsset"]["free"]}')
                print(f'  Locked: {asset["quoteAsset"]["locked"]}')
                print(f'  Total: {asset["quoteAsset"]["totalAsset"]}')
                
                # Calculate minimum trade size
                free_usdt = float(asset['quoteAsset']['free'])
                print(f'\nAVAILABLE FOR TRADING:')
                print(f'Free USDT: {free_usdt:.2f}')
                print(f'Suggested test size: {free_usdt * 0.1:.2f} (10% of free)')
                
                # Get current BTC price
                ticker = connector.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
                
                # Calculate minimum order requirements
                symbol_info = connector.client.get_symbol_info('BTCUSDT')
                min_notional = 0
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'MIN_NOTIONAL':
                        min_notional = float(filter_item['minNotional'])
                        break
                
                print(f'\nTRADING REQUIREMENTS:')
                print(f'Current BTC Price: ${current_price:.2f}')
                print(f'Minimum Notional: ${min_notional:.2f}')
                print(f'Can Trade: {free_usdt > min_notional}')
                
                if free_usdt > min_notional:
                    # Calculate safe test trade size
                    safe_trade_size = min(free_usdt * 0.05, 10.0)  # 5% of free or $10, whichever is smaller
                    btc_quantity = safe_trade_size / current_price
                    
                    print(f'\nSAFE TEST TRADE:')
                    print(f'Trade Size: ${safe_trade_size:.2f}')
                    print(f'BTC Quantity: {btc_quantity:.8f}')
                    print(f'Remaining USDT: ${free_usdt - safe_trade_size:.2f}')
                else:
                    print(f'\nINSUFFICIENT BALANCE FOR TRADING')
                    print(f'Need at least ${min_notional:.2f}, have ${free_usdt:.2f}')
                
                break
        
    except Exception as e:
        print(f'Error checking balance: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_available_balance()
