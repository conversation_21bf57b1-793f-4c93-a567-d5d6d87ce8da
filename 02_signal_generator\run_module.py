#!/usr/bin/env python3
"""
Signal Generator Module - Independent Execution
Run this module independently for testing signal generation
"""

import sys
import os
import time

# Add paths
sys.path.append('../shared_config')
sys.path.append('../01_binance_connector')

from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from shared_config.shared_utilities import setup_logging

def main():
    """Run signal generator module independently"""
    logger = setup_logging('signal_generator_standalone')
    
    print("🎯 Signal Generator Module - Independent Execution")
    print("=" * 60)
    
    try:
        # Initialize generator
        generator = GridAwareSignalGenerator()
        
        print("🚀 Signal generator initialized")
        print("📊 Generating signals with TCN-CNN-PPO ensemble...")
        print("Press Ctrl+C to stop")
        
        # Generate signals continuously
        while True:
            try:
                signal = generator.generate_signal()
                
                print(f"\n📡 Signal Generated:")
                print(f"   Signal: {signal['signal']}")
                print(f"   Reason: {signal.get('reason', 'N/A')}")
                print(f"   Confidence: {signal.get('confidence', 0):.2f}")
                print(f"   Price: ${signal.get('price', 0):.2f}")
                print(f"   Grid Level: ${signal.get('grid_level', 0):.2f}")
                print(f"   Grid Distance: {signal.get('grid_distance', 0):.6f}")
                
                time.sleep(30)  # Generate signals every 30 seconds
                
            except KeyboardInterrupt:
                print("\n🛑 Signal generation stopped")
                break
            except Exception as e:
                print(f"❌ Signal generation error: {e}")
                time.sleep(60)
        
        print("✅ Signal generator module ready for integration")
        
    except Exception as e:
        print(f"❌ Module execution failed: {e}")
        logger.error(f"Module execution failed: {e}")

if __name__ == "__main__":
    main()
