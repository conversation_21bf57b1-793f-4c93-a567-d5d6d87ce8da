#!/usr/bin/env python3
"""
Direct Model Training - No Dependencies
Train TCN-CNN-PPO model directly without loading existing model
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
# Import only the model class, not the signal generator
import torch.nn.functional as F

class TCNCNNPPOModel(nn.Module):
    """Enhanced TCN-CNN-PPO Model with Grid-Aware Environment"""

    def __init__(self, input_size=135):
        super(TCNCNNPPOModel, self).__init__()

        # TCN Features (64)
        self.tcn_layers = nn.Sequential(
            nn.Conv1d(4, 32, kernel_size=3, padding=1),
            nn.<PERSON>LU(),
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1)
        )

        # CNN Features (64)
        self.cnn_layers = nn.Sequential(
            nn.Conv1d(4, 32, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1)
        )

        # PPO Policy Network (135 features total)
        self.policy_network = nn.Sequential(
            nn.Linear(135, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # BUY, SELL, HOLD
        )

        # Value Network for PPO
        self.value_network = nn.Sequential(
            nn.Linear(135, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )

    def forward(self, market_data, grid_features):
        """Forward pass with 135-feature state vector"""
        # market_data: [batch_size, 4, sequence_length]
        # grid_features: [batch_size, 7]

        # TCN processing
        tcn_out = self.tcn_layers(market_data)  # [batch_size, 64, 1]
        tcn_features = tcn_out.squeeze(-1)  # [batch_size, 64]

        # CNN processing
        cnn_out = self.cnn_layers(market_data)  # [batch_size, 64, 1]
        cnn_features = cnn_out.squeeze(-1)  # [batch_size, 64]

        # Combine all features: 64 TCN + 64 CNN + 7 Grid = 135
        combined_features = torch.cat([tcn_features, cnn_features, grid_features], dim=1)

        # Policy and value outputs
        policy_logits = self.policy_network(combined_features)
        value = self.value_network(combined_features)

        return policy_logits, value

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectModelTrainer:
    """Direct model training without dependencies"""
    
    def __init__(self):
        self.model = TCNCNNPPOModel(input_size=135)
        self.binance = None
        
    def initialize_system(self):
        """Initialize training system"""
        try:
            logger.info("🚀 Initializing direct model training...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            
            logger.info("✅ Direct training system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Direct training initialization failed: {e}")
            return False
    
    def fetch_bitcoin_data(self):
        """Fetch Bitcoin data for training"""
        try:
            logger.info("📊 Fetching Bitcoin data for training...")
            
            # Get 30 days of recent data for quick training
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            klines = self.binance.client.get_historical_klines(
                'BTCUSDT',
                '1h',
                start_time.strftime('%Y-%m-%d'),
                end_time.strftime('%Y-%m-%d')
            )
            
            if not klines:
                logger.error("❌ No Bitcoin data received")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # Calculate indicators
            df['rsi'] = self.calculate_rsi(df['close'])
            df['vwap'] = self.calculate_vwap(df)
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"✅ Fetched {len(df)} data points")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch Bitcoin data: {e}")
            return None
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_vwap(self, df):
        """Calculate VWAP"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap
    
    def prepare_training_data(self, df):
        """Prepare training data"""
        try:
            logger.info("🔧 Preparing training data...")
            
            market_features = []
            grid_features = []
            labels = []
            
            for i in range(len(df) - 4):
                # Market features - 4 channels for TCN/CNN
                price_sequence = df['close'].iloc[i:i+4].values
                rsi_sequence = df['rsi'].iloc[i:i+4].values
                vwap_sequence = df['vwap'].iloc[i:i+4].values
                volume_sequence = df['volume'].iloc[i:i+4].values

                # Create 4-channel input for TCN/CNN (4 features x sequence_length)
                market_vector = np.array([
                    price_sequence,
                    rsi_sequence,
                    vwap_sequence,
                    volume_sequence / np.max(volume_sequence)  # Normalize volume
                ])

                market_features.append(market_vector)
                
                # Grid features (7)
                current_price = df['close'].iloc[i+3]
                grid_vector = np.array([
                    current_price / 1000,  # Normalized price
                    1.0,  # At grid level (simplified)
                    0.001,  # Grid distance
                    current_price * 1.0025,  # Next grid up
                    current_price * 0.9975,  # Next grid down
                    0.0025,  # Grid spacing
                    1.0  # Compliance score
                ])
                
                grid_features.append(grid_vector)
                
                # Label
                current_price = df['close'].iloc[i+3]
                next_price = df['close'].iloc[i+4] if i+4 < len(df) else current_price
                label = 1 if next_price > current_price else 0
                labels.append(label)
            
            market_features = np.array(market_features)
            grid_features = np.array(grid_features)
            labels = np.array(labels)
            
            logger.info(f"✅ Prepared {len(market_features)} training samples")
            return market_features, grid_features, labels
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare training data: {e}")
            return None, None, None
    
    def train_model(self, market_features, grid_features, labels):
        """Train the model"""
        try:
            logger.info("🧠 Training TCN-CNN-PPO model...")
            
            # Convert to tensors
            X_market = torch.FloatTensor(market_features)
            X_grid = torch.FloatTensor(grid_features)
            y = torch.LongTensor(labels)
            
            # Split data
            train_size = int(0.8 * len(X_market))
            X_train_market = X_market[:train_size]
            X_train_grid = X_grid[:train_size]
            y_train = y[:train_size]
            X_val_market = X_market[train_size:]
            X_val_grid = X_grid[train_size:]
            y_val = y[train_size:]
            
            # Setup training
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            
            best_val_acc = 0
            best_model_state = None
            
            # Training loop
            epochs = 100
            for epoch in range(epochs):
                self.model.train()
                
                # Forward pass
                policy_logits, value = self.model(X_train_market, X_train_grid)
                loss = criterion(policy_logits, y_train)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Validation
                if epoch % 10 == 0:
                    self.model.eval()
                    with torch.no_grad():
                        val_policy, val_value = self.model(X_val_market, X_val_grid)
                        val_pred = torch.argmax(val_policy, dim=1)
                        val_acc = (val_pred == y_val).float().mean().item()
                        
                        if val_acc > best_val_acc:
                            best_val_acc = val_acc
                            best_model_state = self.model.state_dict().copy()
                        
                        logger.info(f"Epoch {epoch}: Loss={loss.item():.4f}, Val Acc={val_acc:.4f}")
            
            # Load best model
            if best_model_state:
                self.model.load_state_dict(best_model_state)
            
            logger.info(f"✅ Training completed - Best Val Acc: {best_val_acc:.4f}")
            return best_val_acc > 0.5
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            return False
    
    def save_trained_model(self):
        """Save the trained model"""
        try:
            model_dir = os.path.join('02_signal_generator', 'models')
            os.makedirs(model_dir, exist_ok=True)
            
            model_path = os.path.join(model_dir, 'best_real_3year_trained_model.pth')
            
            # Save with proper format
            checkpoint = {
                'model_state_dict': self.model.state_dict(),
                'model_architecture': 'Enhanced TCN-CNN-PPO with grid-aware environment',
                'training_metadata': {
                    'training_date': datetime.now().isoformat(),
                    'training_type': 'direct_training',
                    'input_features': 135,
                    'architecture': 'Enhanced TCN-CNN-PPO'
                },
                'performance_metrics': {
                    'training_method': 'direct_30_days',
                    'epochs': 100
                }
            }
            
            torch.save(checkpoint, model_path)
            
            logger.info(f"✅ Model saved to: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save model: {e}")
            return False
    
    def run_direct_training(self):
        """Run direct training"""
        try:
            logger.info("🚀 Starting direct TCN-CNN-PPO training...")
            
            # Fetch data
            df = self.fetch_bitcoin_data()
            if df is None:
                return False
            
            # Prepare data
            market_features, grid_features, labels = self.prepare_training_data(df)
            if market_features is None:
                return False
            
            # Train model
            if not self.train_model(market_features, grid_features, labels):
                return False
            
            # Save model
            if not self.save_trained_model():
                return False
            
            logger.info("✅ Direct training completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Direct training failed: {e}")
            return False

def main():
    """Main training function"""
    print("🧠 DIRECT TCN-CNN-PPO MODEL TRAINING")
    print("=" * 60)
    print("📋 Direct training without dependencies")
    print("📋 30 days of Bitcoin data")
    print("📋 100 epochs training")
    print("=" * 60)
    
    trainer = DirectModelTrainer()
    
    if not trainer.initialize_system():
        print("❌ Direct training initialization failed")
        return False
    
    print("🧠 Starting direct training...")
    if trainer.run_direct_training():
        print("✅ Direct training completed successfully!")
        print("📁 Model saved with correct architecture")
        print("🚀 Ready for testing and deployment")
        return True
    else:
        print("❌ Direct training failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
