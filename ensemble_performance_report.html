
<!DOCTYPE html>
<html>
<head>
    <title>Ensemble TCN-CNN-PPO Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .container { max-width: 1400px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }
        .performance { background-color: #e7f3ff; border: 3px solid #007bff; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .excellent { background-color: #d4edda; border: 3px solid #28a745; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .good { background-color: #fff3cd; border: 3px solid #ffc107; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .poor { background-color: #f8d7da; border: 3px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #dee2e6; padding: 12px; text-align: center; }
        th { background-color: #343a40; color: white; }
        .excellent-cell { background-color: #d4edda; color: #155724; font-weight: bold; }
        .good-cell { background-color: #fff3cd; color: #856404; font-weight: bold; }
        .poor-cell { background-color: #f8d7da; color: #721c24; font-weight: bold; }
        .ensemble-info { background-color: #6c757d; color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Ensemble TCN-CNN-PPO Performance Report</h1>
            <p><strong>Generated:</strong> 2025-07-16 21:43:05</p>
            <p><strong>Model:</strong> Recently Retrained Ensemble (3 Models)</p>
            <p><strong>Overall Performance:</strong> 0.0%</p>
        </div>

        <div class="ensemble-info">
            <h2>🧠 Ensemble Architecture</h2>
            <p><strong>Models:</strong> 3 Independent TCN-CNN-PPO Models</p>
            <p><strong>Training:</strong> Recently Completed (Latest)</p>
            <p><strong>Voting:</strong> Confidence-Weighted Ensemble Decisions</p>
            <p><strong>Features:</strong> 135-feature state vector (64 TCN + 64 CNN + 7 Grid)</p>
        </div>

        <div class="poor">
            <h2>🎯 Overall Performance Summary</h2>
            <p><strong>Overall Compliance:</strong> 0.0%</p>
            <p><strong>Hierarchy Correct:</strong> ✅ YES</p>
            <p><strong>Final Best Performance:</strong> ✅ YES</p>
        </div>

        <table>
            <tr>
                <th>Phase</th>
                <th>Total Trades</th>
                <th>Win Rate (%)</th>
                <th>Trades/Day</th>
                <th>Composite Score</th>
                <th>New Reward</th>
                <th>Avg Confidence</th>
                <th>Signal Quality</th>
                <th>Compliance</th>
                <th>Status</th>
            </tr>

            <tr>
                <td>Training</td>
                <td>466</td>
                <td>34.8</td>
                <td>0.6</td>
                <td>0.707</td>
                <td>0.45</td>
                <td>0.498</td>
                <td>11.8%</td>
                <td class="poor-cell">0.0%</td>
                <td class="poor-cell">POOR</td>
            </tr>

            <tr>
                <td>Out Of Sample</td>
                <td>254</td>
                <td>29.5</td>
                <td>0.7</td>
                <td>0.693</td>
                <td>0.48</td>
                <td>0.497</td>
                <td>14.1%</td>
                <td class="poor-cell">0.0%</td>
                <td class="poor-cell">POOR</td>
            </tr>

            <tr>
                <td>Backtest</td>
                <td>146</td>
                <td>28.1</td>
                <td>0.9</td>
                <td>0.697</td>
                <td>0.61</td>
                <td>0.498</td>
                <td>17.0%</td>
                <td class="poor-cell">0.0%</td>
                <td class="poor-cell">POOR</td>
            </tr>

            <tr>
                <td>Final 3Day</td>
                <td>3</td>
                <td>33.3</td>
                <td>1.0</td>
                <td>0.705</td>
                <td>0.70</td>
                <td>0.499</td>
                <td>36.2%</td>
                <td class="poor-cell">0.0%</td>
                <td class="poor-cell">POOR</td>
            </tr>

        </table>

        <div class="performance">
            <h2>📊 Detailed Performance Analysis</h2>
            <p><strong>Ensemble Quality:</strong> Confidence-weighted voting from 3 models</p>
            <p><strong>Signal Generation:</strong> Enhanced grid-aware processing</p>
            <p><strong>Training Data:</strong> 4 years of real Bitcoin market data</p>
            <p><strong>Architecture:</strong> TCN-CNN-PPO with ensemble consensus</p>
        </div>

        <div class="poor">
            <h2>🚀 Deployment Assessment</h2>
            <p><strong>Performance Level:</strong> NEEDS IMPROVEMENT</p>
            <p><strong>Recommendation:</strong> Continue optimization</p>
            <p><strong>Ensemble Advantage:</strong> Moderate ensemble benefit</p>
        </div>
    </div>
</body>
</html>
