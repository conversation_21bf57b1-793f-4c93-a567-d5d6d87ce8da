#!/usr/bin/env python3
"""
Train TCN-CNN-PPO Model with Real Bitcoin Data
Compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import json

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import TCNCNNPPOModel, GridAwareSignalGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TCNCNNPPOTrainer:
    """Train TCN-CNN-PPO model with real Bitcoin data"""
    
    def __init__(self):
        self.model = TCNCNNPPOModel(input_size=135)
        self.binance = None
        self.training_data = None
        self.validation_data = None
        self.test_data = None
        
    def initialize_system(self):
        """Initialize training system"""
        try:
            logger.info("🚀 Initializing TCN-CNN-PPO training system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            
            logger.info("✅ Training system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Training system initialization failed: {e}")
            return False
    
    def fetch_real_training_data(self, days=1095):  # 3 years
        """Fetch real Bitcoin data for training"""
        try:
            logger.info(f"📊 Fetching {days} days of real Bitcoin data...")
            
            # Get historical data from Binance
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # Fetch BTCUSDT 1-hour data
            klines = self.binance.client.get_historical_klines(
                'BTCUSDT',
                '1h',
                start_time.strftime('%Y-%m-%d'),
                end_time.strftime('%Y-%m-%d')
            )
            
            if not klines:
                logger.error("❌ No historical data received")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # Calculate technical indicators
            df['rsi'] = self.calculate_rsi(df['close'])
            df['vwap'] = self.calculate_vwap(df)
            
            # Calculate grid features
            df['grid_level'] = self.calculate_grid_levels(df['close'])
            df['at_grid_level'] = self.check_grid_compliance(df['close'])
            
            # Remove NaN values
            df = df.dropna()
            
            logger.info(f"✅ Fetched {len(df)} data points with indicators")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch training data: {e}")
            return None
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_vwap(self, df):
        """Calculate VWAP indicator"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap
    
    def calculate_grid_levels(self, prices):
        """Calculate grid levels (0.25% spacing)"""
        base_price = 100000  # Reference price
        grid_spacing = 0.0025  # 0.25%
        return ((prices - base_price) / (base_price * grid_spacing)).round().astype(int)
    
    def check_grid_compliance(self, prices):
        """Check if prices are at exact grid levels"""
        base_price = 100000
        grid_spacing = 0.0025
        tolerance = 0.00001  # 0.001%
        
        grid_prices = base_price * (1 + np.round((prices - base_price) / (base_price * grid_spacing)) * grid_spacing)
        return np.abs(prices - grid_prices) / prices <= tolerance
    
    def prepare_training_data(self, df):
        """Prepare data for TCN-CNN-PPO training"""
        try:
            logger.info("🔧 Preparing training data...")
            
            # Create features (135 total)
            features = []
            
            for i in range(len(df) - 4):  # Need 4 previous points for TCN
                # Price features (4 points)
                price_features = df['close'].iloc[i:i+4].values
                
                # Technical indicators (current)
                rsi = df['rsi'].iloc[i+3]
                vwap = df['vwap'].iloc[i+3]
                
                # Grid features (7)
                grid_level = df['grid_level'].iloc[i+3]
                at_grid = df['at_grid_level'].iloc[i+3]
                
                # Create 135-feature vector
                feature_vector = np.zeros(135)
                
                # TCN features (64) - price patterns
                feature_vector[:4] = price_features
                feature_vector[4:64] = np.tile(price_features, 15)  # Repeat for 64 features
                
                # CNN features (64) - technical patterns
                feature_vector[64:66] = [rsi, vwap]
                feature_vector[66:128] = np.tile([rsi, vwap], 31)  # Repeat for 64 features
                
                # Grid features (7)
                feature_vector[128] = grid_level
                feature_vector[129] = float(at_grid)
                feature_vector[130:135] = [grid_level, float(at_grid), rsi, vwap, price_features[-1]]
                
                features.append(feature_vector)
            
            # Create labels (next price movement)
            labels = []
            for i in range(len(features)):
                current_price = df['close'].iloc[i+3]
                next_price = df['close'].iloc[i+4] if i+4 < len(df) else current_price
                
                # Label: 1 for up, 0 for down
                label = 1 if next_price > current_price else 0
                labels.append(label)
            
            features = np.array(features)
            labels = np.array(labels)
            
            logger.info(f"✅ Prepared {len(features)} training samples with 135 features each")
            return features, labels
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare training data: {e}")
            return None, None
    
    def train_model(self, features, labels, epochs=100):
        """Train the TCN-CNN-PPO model"""
        try:
            logger.info(f"🧠 Training TCN-CNN-PPO model for {epochs} epochs...")
            
            # Convert to tensors
            X = torch.FloatTensor(features)
            y = torch.LongTensor(labels)
            
            # Split data
            train_size = int(0.7 * len(X))
            val_size = int(0.2 * len(X))
            
            X_train = X[:train_size]
            y_train = y[:train_size]
            X_val = X[train_size:train_size+val_size]
            y_val = y[train_size:train_size+val_size]
            X_test = X[train_size+val_size:]
            y_test = y[train_size+val_size:]
            
            # Setup training
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            
            best_val_acc = 0
            best_model_state = None
            
            # Training loop
            for epoch in range(epochs):
                self.model.train()
                
                # Forward pass
                policy_logits, value = self.model(X_train.unsqueeze(1))
                loss = criterion(policy_logits, y_train)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Validation
                if epoch % 10 == 0:
                    self.model.eval()
                    with torch.no_grad():
                        val_policy, val_value = self.model(X_val.unsqueeze(1))
                        val_pred = torch.argmax(val_policy, dim=1)
                        val_acc = (val_pred == y_val).float().mean().item()
                        
                        if val_acc > best_val_acc:
                            best_val_acc = val_acc
                            best_model_state = self.model.state_dict().copy()
                        
                        logger.info(f"Epoch {epoch}: Loss={loss.item():.4f}, Val Acc={val_acc:.4f}")
            
            # Load best model
            if best_model_state:
                self.model.load_state_dict(best_model_state)
            
            # Test evaluation
            self.model.eval()
            with torch.no_grad():
                test_policy, test_value = self.model(X_test.unsqueeze(1))
                test_pred = torch.argmax(test_policy, dim=1)
                test_acc = (test_pred == y_test).float().mean().item()
            
            logger.info(f"✅ Training completed - Best Val Acc: {best_val_acc:.4f}, Test Acc: {test_acc:.4f}")
            
            return {
                'train_acc': (torch.argmax(policy_logits, dim=1) == y_train).float().mean().item(),
                'val_acc': best_val_acc,
                'test_acc': test_acc,
                'epochs': epochs
            }
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            return None
    
    def save_model(self):
        """Save trained model"""
        try:
            model_dir = os.path.join('02_signal_generator', 'models')
            os.makedirs(model_dir, exist_ok=True)
            
            model_path = os.path.join(model_dir, 'best_real_3year_trained_model.pth')
            torch.save(self.model.state_dict(), model_path)
            
            logger.info(f"✅ Model saved to: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save model: {e}")
            return False
    
    def run_full_training(self):
        """Run complete training pipeline"""
        try:
            logger.info("🚀 Starting full TCN-CNN-PPO training pipeline...")
            
            # Fetch real data
            df = self.fetch_real_training_data(days=1095)  # 3 years
            if df is None:
                return False
            
            # Prepare training data
            features, labels = self.prepare_training_data(df)
            if features is None:
                return False
            
            # Train model
            results = self.train_model(features, labels, epochs=100)
            if results is None:
                return False
            
            # Save model
            if not self.save_model():
                return False
            
            logger.info("✅ Full training pipeline completed successfully!")
            logger.info(f"📊 Final Results: Train={results['train_acc']:.4f}, Val={results['val_acc']:.4f}, Test={results['test_acc']:.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Full training pipeline failed: {e}")
            return False

def main():
    """Main training function"""
    print("🧠 TCN-CNN-PPO MODEL TRAINING")
    print("=" * 60)
    print("📋 Training with 3 years of real Bitcoin data")
    print("📋 135-feature state vector with grid awareness")
    print("📋 Compliant with master documentation")
    print("=" * 60)
    
    trainer = TCNCNNPPOTrainer()
    
    if not trainer.initialize_system():
        print("❌ Training system initialization failed")
        return
    
    print("🧠 Starting model training...")
    if trainer.run_full_training():
        print("✅ TCN-CNN-PPO model training completed successfully!")
        print("📁 Model saved to: 02_signal_generator/models/best_real_3year_trained_model.pth")
        print("🚀 Ready for backtesting and deployment")
    else:
        print("❌ Model training failed")

if __name__ == "__main__":
    main()
