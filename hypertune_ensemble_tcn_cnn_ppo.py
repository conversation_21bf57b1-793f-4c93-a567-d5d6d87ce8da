#!/usr/bin/env python3
"""
Hyperparameter Tuning for Ensemble TCN-CNN-PPO
Optimize for: Composite Score × Net Profit (User Preference)
Aim for Highest Reward
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime
from torch.utils.data import DataLoader, TensorDataset
import itertools
from concurrent.futures import ProcessPoolExecutor
import json

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HyperparameterTuner:
    """
    Hyperparameter tuning for ensemble TCN-CNN-PPO
    Optimizes for: Composite Score × Net Profit
    """
    
    def __init__(self):
        self.best_reward = 0.0
        self.best_config = None
        self.results_history = []
        
        # Hyperparameter search space
        self.search_space = {
            # TCN Architecture
            'tcn_channels': [[16, 32, 64], [32, 64, 128], [64, 128, 256]],
            'tcn_kernel_sizes': [[3, 3, 3], [3, 5, 7], [5, 7, 9]],
            'tcn_dilations': [[1, 2, 4], [1, 3, 9], [2, 4, 8]],
            
            # CNN Architecture  
            'cnn_channels': [[8, 16, 32], [16, 32, 64], [32, 64, 128]],
            'cnn_kernel_sizes': [[3, 5, 7], [5, 7, 9], [7, 9, 11]],
            
            # PPO Architecture
            'ppo_hidden_dims': [[128, 64], [256, 128], [512, 256]],
            'ppo_activation': ['relu', 'tanh', 'leaky_relu'],
            
            # Training Parameters
            'learning_rates': {
                'tcn': [0.0001, 0.001, 0.01],
                'cnn': [0.0001, 0.001, 0.01], 
                'ppo': [0.0001, 0.0003, 0.001],
                'ensemble': [0.001, 0.01, 0.1]
            },
            
            # Ensemble Configuration
            'ensemble_temperature': [0.5, 1.0, 2.0],
            'dropout_rates': [0.1, 0.2, 0.3],
            
            # Training Configuration
            'batch_sizes': [16, 32, 64],
            'sequence_lengths': [30, 60, 120],
            'epochs': [50, 100, 150]
        }
        
        logger.info("🎯 Hyperparameter Tuner Initialized")
        logger.info("🎯 Optimization Target: Composite Score × Net Profit")
        logger.info(f"🔍 Search Space Size: {self._calculate_search_space_size()}")
    
    def _calculate_search_space_size(self):
        """Calculate total combinations in search space"""
        total = 1
        for key, values in self.search_space.items():
            if key == 'learning_rates':
                lr_combinations = 1
                for lr_key, lr_values in values.items():
                    lr_combinations *= len(lr_values)
                total *= lr_combinations
            else:
                total *= len(values)
        return total
    
    def generate_hyperparameter_configs(self, num_trials=50):
        """Generate random hyperparameter configurations"""
        configs = []
        
        for trial in range(num_trials):
            config = {
                # TCN Configuration
                'tcn_channels': self.search_space['tcn_channels'][np.random.randint(len(self.search_space['tcn_channels']))],
                'tcn_kernel_sizes': self.search_space['tcn_kernel_sizes'][np.random.randint(len(self.search_space['tcn_kernel_sizes']))],
                'tcn_dilations': self.search_space['tcn_dilations'][np.random.randint(len(self.search_space['tcn_dilations']))],

                # CNN Configuration
                'cnn_channels': self.search_space['cnn_channels'][np.random.randint(len(self.search_space['cnn_channels']))],
                'cnn_kernel_sizes': self.search_space['cnn_kernel_sizes'][np.random.randint(len(self.search_space['cnn_kernel_sizes']))],

                # PPO Configuration
                'ppo_hidden_dims': self.search_space['ppo_hidden_dims'][np.random.randint(len(self.search_space['ppo_hidden_dims']))],
                'ppo_activation': self.search_space['ppo_activation'][np.random.randint(len(self.search_space['ppo_activation']))],

                # Learning Rates
                'learning_rates': {
                    'tcn': self.search_space['learning_rates']['tcn'][np.random.randint(len(self.search_space['learning_rates']['tcn']))],
                    'cnn': self.search_space['learning_rates']['cnn'][np.random.randint(len(self.search_space['learning_rates']['cnn']))],
                    'ppo': self.search_space['learning_rates']['ppo'][np.random.randint(len(self.search_space['learning_rates']['ppo']))],
                    'ensemble': self.search_space['learning_rates']['ensemble'][np.random.randint(len(self.search_space['learning_rates']['ensemble']))]
                },

                # Other Parameters
                'ensemble_temperature': self.search_space['ensemble_temperature'][np.random.randint(len(self.search_space['ensemble_temperature']))],
                'dropout_rate': self.search_space['dropout_rates'][np.random.randint(len(self.search_space['dropout_rates']))],
                'batch_size': self.search_space['batch_sizes'][np.random.randint(len(self.search_space['batch_sizes']))],
                'sequence_length': self.search_space['sequence_lengths'][np.random.randint(len(self.search_space['sequence_lengths']))],
                'epochs': self.search_space['epochs'][np.random.randint(len(self.search_space['epochs']))],

                # Trial ID
                'trial_id': trial
            }
            configs.append(config)
        
        return configs
    
    def calculate_composite_score(self, trading_results):
        """
        Calculate composite score according to master document
        6 components weighted formula
        """
        # Calculate individual components
        sortino_ratio = self._calculate_sortino_ratio(trading_results)
        calmar_ratio = self._calculate_calmar_ratio(trading_results)
        profit_factor = self._calculate_profit_factor(trading_results)
        win_rate = trading_results['winning_trades'] / max(trading_results['total_trades'], 1)
        max_drawdown = self._calculate_max_drawdown(trading_results)
        trade_frequency = trading_results['total_trades'] / max(trading_results['trading_days'], 1)
        
        # Normalize components (0-1 scale)
        sortino_ratio_normalized = min(sortino_ratio / 2.0, 1.0)  # Target: >2.0
        calmar_ratio_normalized = min(calmar_ratio / 3.0, 1.0)    # Target: >3.0
        profit_factor_normalized = min(profit_factor / 1.5, 1.0)  # Target: >1.5
        win_rate_normalized = min(win_rate / 0.60, 1.0)           # Target: >60%
        max_drawdown_inverse = max(0, 1.0 - max_drawdown)         # Lower is better
        trade_frequency_normalized = min(trade_frequency / 8.0, 1.0)  # Target: ≥8/day
        
        # Master document weighted composite score
        composite_score = (
            0.28 * sortino_ratio_normalized +      # 28% - Risk-adjusted returns
            0.22 * calmar_ratio_normalized +       # 22% - Return/max drawdown ratio
            0.20 * profit_factor_normalized +      # 20% - Gross profit/gross loss
            0.15 * win_rate_normalized +           # 15% - Win percentage
            0.10 * max_drawdown_inverse +          # 10% - Drawdown minimization
            0.05 * trade_frequency_normalized      # 5% - Trading activity
        )
        
        return composite_score
    
    def calculate_reward(self, trading_results):
        """
        Calculate reward: Composite Score × Net Profit
        User preference for optimization target
        """
        composite_score = self.calculate_composite_score(trading_results)
        net_profit = trading_results['net_profit']
        
        # User preferred reward function
        reward = composite_score * net_profit
        
        return {
            'reward': reward,
            'composite_score': composite_score,
            'net_profit': net_profit,
            'components': {
                'win_rate': trading_results['winning_trades'] / max(trading_results['total_trades'], 1),
                'total_trades': trading_results['total_trades'],
                'trading_days': trading_results['trading_days'],
                'sortino_ratio': self._calculate_sortino_ratio(trading_results),
                'calmar_ratio': self._calculate_calmar_ratio(trading_results),
                'profit_factor': self._calculate_profit_factor(trading_results),
                'max_drawdown': self._calculate_max_drawdown(trading_results)
            }
        }
    
    def _calculate_sortino_ratio(self, results):
        """Calculate Sortino ratio (return/downside deviation)"""
        if 'daily_returns' not in results or len(results['daily_returns']) == 0:
            return 0.0
        
        returns = results['daily_returns']
        mean_return = np.mean(returns)
        downside_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(downside_returns) if downside_returns else 0.01
        return mean_return / downside_deviation if downside_deviation > 0 else 0
    
    def _calculate_calmar_ratio(self, results):
        """Calculate Calmar ratio (annual return/max drawdown)"""
        if results['trading_days'] == 0:
            return 0.0
        
        annual_return = results['net_profit'] * (365 / results['trading_days'])
        max_drawdown = self._calculate_max_drawdown(results)
        return annual_return / max_drawdown if max_drawdown > 0 else 0
    
    def _calculate_profit_factor(self, results):
        """Calculate profit factor (gross profit/gross loss)"""
        if 'trades' not in results or len(results['trades']) == 0:
            return 0.0
        
        gross_profit = sum([t['profit'] for t in results['trades'] if t['profit'] > 0])
        gross_loss = abs(sum([t['profit'] for t in results['trades'] if t['profit'] < 0]))
        return gross_profit / gross_loss if gross_loss > 0 else 0
    
    def _calculate_max_drawdown(self, results):
        """Calculate maximum drawdown from peak"""
        if 'equity_curve' not in results or len(results['equity_curve']) == 0:
            return 0.0
        
        equity_curve = results['equity_curve']
        peak = equity_curve[0]
        max_dd = 0
        
        for value in equity_curve:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak if peak > 0 else 0
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def load_data(self):
        """Load data with backward split"""
        try:
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['year'] = df['datetime'].dt.year
            
            # Backward from today split
            train_data = df[df['year'].isin([2021, 2022])].copy()  # Historical learning
            val_data = df[df['year'].isin([2023])].copy()          # Recent validation
            test_data = df[df['year'].isin([2024])].copy()         # Most recent backtest
            
            logger.info(f"📊 Training: {len(train_data)} samples (2021-2022)")
            logger.info(f"📊 Validation: {len(val_data)} samples (2023)")
            logger.info(f"📊 Backtest: {len(test_data)} samples (2024)")
            
            return train_data, val_data, test_data
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            return None, None, None

    def train_and_evaluate_config(self, config, train_data, val_data, test_data):
        """Train and evaluate a single hyperparameter configuration"""
        try:
            logger.info(f"🔍 Trial {config['trial_id']}: Testing configuration...")

            # Create model with current config
            model = self._build_ensemble_model(config)

            # Prepare data loaders
            train_loader = self._prepare_data_loader(train_data, config)
            val_loader = self._prepare_data_loader(val_data, config)
            test_loader = self._prepare_data_loader(test_data, config)

            # Train model
            trained_model, training_history = self._train_model(model, train_loader, val_loader, config)

            # Evaluate on test set (backtest)
            trading_results = self._evaluate_trading_performance(trained_model, test_loader, test_data)

            # Calculate reward (composite score × net profit)
            reward_metrics = self.calculate_reward(trading_results)

            # Store results
            result = {
                'trial_id': config['trial_id'],
                'config': config,
                'reward': reward_metrics['reward'],
                'composite_score': reward_metrics['composite_score'],
                'net_profit': reward_metrics['net_profit'],
                'components': reward_metrics['components'],
                'training_history': training_history
            }

            logger.info(f"✅ Trial {config['trial_id']} completed:")
            logger.info(f"   Reward: {reward_metrics['reward']:.4f}")
            logger.info(f"   Composite Score: {reward_metrics['composite_score']:.4f}")
            logger.info(f"   Net Profit: {reward_metrics['net_profit']:.2f}")
            logger.info(f"   Win Rate: {reward_metrics['components']['win_rate']:.4f}")

            return result

        except Exception as e:
            logger.error(f"❌ Trial {config['trial_id']} failed: {e}")
            return {
                'trial_id': config['trial_id'],
                'config': config,
                'reward': 0.0,
                'error': str(e)
            }

    def _build_ensemble_model(self, config):
        """Build ensemble model with given configuration"""
        # Simplified model builder for hyperparameter tuning
        class TunableEnsembleModel(nn.Module):
            def __init__(self, config):
                super(TunableEnsembleModel, self).__init__()

                # TCN Component
                tcn_layers = []
                in_channels = 4
                for i, (out_channels, kernel_size, dilation) in enumerate(zip(
                    config['tcn_channels'], config['tcn_kernel_sizes'], config['tcn_dilations']
                )):
                    tcn_layers.extend([
                        nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2, dilation=dilation),
                        nn.ReLU(),
                        nn.Dropout(config['dropout_rate'])
                    ])
                    in_channels = out_channels

                tcn_layers.extend([nn.AdaptiveAvgPool1d(1), nn.Flatten(), nn.Linear(in_channels, 3)])
                self.tcn = nn.Sequential(*tcn_layers)

                # CNN Component
                cnn_layers = []
                in_channels = 4
                for i, (out_channels, kernel_size) in enumerate(zip(
                    config['cnn_channels'], config['cnn_kernel_sizes']
                )):
                    cnn_layers.extend([
                        nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2),
                        nn.ReLU(),
                        nn.Dropout(config['dropout_rate'])
                    ])
                    in_channels = out_channels

                cnn_layers.extend([nn.AdaptiveAvgPool1d(1), nn.Flatten(), nn.Linear(in_channels, 3)])
                self.cnn = nn.Sequential(*cnn_layers)

                # PPO Component
                ppo_layers = []
                in_dim = 4
                for hidden_dim in config['ppo_hidden_dims']:
                    ppo_layers.extend([
                        nn.Linear(in_dim, hidden_dim),
                        self._get_activation(config['ppo_activation']),
                        nn.Dropout(config['dropout_rate'])
                    ])
                    in_dim = hidden_dim

                ppo_layers.append(nn.Linear(in_dim, 3))
                self.ppo = nn.Sequential(*ppo_layers)

                # Ensemble weights
                self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
                self.temperature = config['ensemble_temperature']

            def _get_activation(self, activation):
                if activation == 'relu':
                    return nn.ReLU()
                elif activation == 'tanh':
                    return nn.Tanh()
                elif activation == 'leaky_relu':
                    return nn.LeakyReLU()
                else:
                    return nn.ReLU()

            def forward(self, x):
                # x shape: (batch, sequence, features)
                x_transposed = x.transpose(1, 2)  # (batch, features, sequence)

                # Individual predictions
                tcn_pred = torch.softmax(self.tcn(x_transposed), dim=1)
                cnn_pred = torch.softmax(self.cnn(x_transposed), dim=1)
                ppo_pred = torch.softmax(self.ppo(x[:, -1, :]), dim=1)  # Last timestep for PPO

                # Ensemble combination
                normalized_weights = torch.softmax(self.ensemble_weights / self.temperature, dim=0)
                ensemble_pred = (normalized_weights[0] * tcn_pred +
                               normalized_weights[1] * cnn_pred +
                               normalized_weights[2] * ppo_pred)

                return ensemble_pred

        return TunableEnsembleModel(config)

    def _prepare_data_loader(self, data, config):
        """Prepare data loader with given configuration"""
        sequences = []
        targets = []

        seq_len = config['sequence_length']

        for i in range(seq_len, len(data)):
            # Market data sequence
            sequence = data.iloc[i-seq_len:i][['open', 'high', 'low', 'close']].values

            # Grid-to-grid movement target
            if i < len(data) - 1:
                current_grid = data.iloc[i]['grid_level']
                next_grid = data.iloc[i+1]['grid_level']

                if next_grid > current_grid:
                    target = 0  # UP
                elif next_grid < current_grid:
                    target = 1  # DOWN
                else:
                    target = 2  # HOLD
            else:
                target = 2

            sequences.append(sequence)
            targets.append(target)

        # Convert to tensors
        X = torch.FloatTensor(np.array(sequences))
        y = torch.LongTensor(np.array(targets))

        dataset = TensorDataset(X, y)
        return DataLoader(dataset, batch_size=config['batch_size'], shuffle=True)

    def _train_model(self, model, train_loader, val_loader, config):
        """Train model with given configuration"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)

        # Optimizers for different components
        tcn_optimizer = optim.Adam(model.tcn.parameters(), lr=config['learning_rates']['tcn'])
        cnn_optimizer = optim.Adam(model.cnn.parameters(), lr=config['learning_rates']['cnn'])
        ppo_optimizer = optim.Adam(model.ppo.parameters(), lr=config['learning_rates']['ppo'])
        ensemble_optimizer = optim.Adam([model.ensemble_weights], lr=config['learning_rates']['ensemble'])

        criterion = nn.CrossEntropyLoss()

        training_history = {'train_loss': [], 'val_accuracy': []}
        best_val_accuracy = 0.0

        for epoch in range(config['epochs']):
            # Training phase
            model.train()
            total_loss = 0.0

            for data, targets in train_loader:
                data, targets = data.to(device), targets.to(device)

                # Train individual components
                tcn_optimizer.zero_grad()
                cnn_optimizer.zero_grad()
                ppo_optimizer.zero_grad()
                ensemble_optimizer.zero_grad()

                outputs = model(data)
                loss = criterion(outputs, targets)

                loss.backward()

                tcn_optimizer.step()
                cnn_optimizer.step()
                ppo_optimizer.step()
                ensemble_optimizer.step()

                # Clamp ensemble weights
                with torch.no_grad():
                    model.ensemble_weights.clamp_(min=0.01)

                total_loss += loss.item()

            # Validation phase
            model.eval()
            correct = 0
            total = 0

            with torch.no_grad():
                for data, targets in val_loader:
                    data, targets = data.to(device), targets.to(device)
                    outputs = model(data)
                    _, predicted = torch.max(outputs.data, 1)
                    total += targets.size(0)
                    correct += (predicted == targets).sum().item()

            val_accuracy = correct / total
            training_history['train_loss'].append(total_loss / len(train_loader))
            training_history['val_accuracy'].append(val_accuracy)

            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy

        return model, training_history

    def _evaluate_trading_performance(self, model, test_loader, test_data):
        """Evaluate trading performance on test set"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.eval()

        # Simulate trading performance
        trades = []
        equity_curve = [100.0]  # Start with $100
        daily_returns = []

        correct_predictions = 0
        total_predictions = 0

        with torch.no_grad():
            for i, (data, targets) in enumerate(test_loader):
                data, targets = data.to(device), targets.to(device)
                outputs = model(data)
                _, predicted = torch.max(outputs.data, 1)

                # Calculate accuracy
                correct_predictions += (predicted == targets).sum().item()
                total_predictions += targets.size(0)

                # Simulate trades based on predictions
                for j in range(len(predicted)):
                    if predicted[j] != 2:  # Not HOLD
                        # Simulate trade outcome based on accuracy
                        is_correct = predicted[j] == targets[j]

                        if is_correct:
                            # Winning trade: 2.5:1 risk-reward
                            profit = 2.5
                        else:
                            # Losing trade: -1
                            profit = -1.0

                        trades.append({
                            'profit': profit,
                            'correct': is_correct
                        })

                        # Update equity curve
                        new_equity = equity_curve[-1] + profit
                        equity_curve.append(max(new_equity, 0))  # Can't go below 0

                        # Daily return (simplified)
                        daily_return = profit / equity_curve[-2] if equity_curve[-2] > 0 else 0
                        daily_returns.append(daily_return)

        # Calculate trading metrics
        winning_trades = sum(1 for t in trades if t['profit'] > 0)
        total_trades = len(trades)
        net_profit = sum(t['profit'] for t in trades)

        trading_results = {
            'winning_trades': winning_trades,
            'total_trades': total_trades,
            'net_profit': net_profit,
            'trades': trades,
            'equity_curve': equity_curve,
            'daily_returns': daily_returns,
            'trading_days': max(len(daily_returns), 1),
            'accuracy': correct_predictions / total_predictions if total_predictions > 0 else 0
        }

        return trading_results

    def run_hyperparameter_tuning(self, num_trials=20):
        """Run complete hyperparameter tuning process"""
        logger.info("🚀 Starting Hyperparameter Tuning")
        logger.info(f"🎯 Optimization Target: Composite Score × Net Profit")
        logger.info(f"🔍 Number of Trials: {num_trials}")

        # Load data
        train_data, val_data, test_data = self.load_data()
        if train_data is None:
            logger.error("❌ Data loading failed")
            return None

        # Generate configurations
        configs = self.generate_hyperparameter_configs(num_trials)

        # Run trials
        for i, config in enumerate(configs):
            logger.info(f"🔄 Running trial {i+1}/{num_trials}")

            result = self.train_and_evaluate_config(config, train_data, val_data, test_data)
            self.results_history.append(result)

            # Update best configuration
            if result['reward'] > self.best_reward:
                self.best_reward = result['reward']
                self.best_config = config
                logger.info(f"🎉 NEW BEST REWARD: {self.best_reward:.4f}")

                # Save best model configuration
                self._save_best_config()

        # Final analysis
        self._analyze_results()

        return self.best_config, self.best_reward

    def _save_best_config(self):
        """Save best configuration to file"""
        best_result = {
            'best_reward': self.best_reward,
            'best_config': self.best_config,
            'timestamp': datetime.now().isoformat()
        }

        with open('best_hyperparameter_config.json', 'w') as f:
            json.dump(best_result, f, indent=2, default=str)

        logger.info(f"💾 Best configuration saved to best_hyperparameter_config.json")

    def _analyze_results(self):
        """Analyze hyperparameter tuning results"""
        logger.info("\n" + "="*80)
        logger.info("📊 HYPERPARAMETER TUNING RESULTS")
        logger.info("="*80)

        # Sort results by reward
        sorted_results = sorted(self.results_history, key=lambda x: x.get('reward', 0), reverse=True)

        # Top 5 results
        logger.info("🏆 TOP 5 CONFIGURATIONS:")
        for i, result in enumerate(sorted_results[:5]):
            logger.info(f"#{i+1} Trial {result['trial_id']}: Reward={result.get('reward', 0):.4f}")
            if 'composite_score' in result:
                logger.info(f"    Composite Score: {result['composite_score']:.4f}")
                logger.info(f"    Net Profit: {result['net_profit']:.2f}")
                logger.info(f"    Win Rate: {result['components']['win_rate']:.4f}")

        # Best configuration details
        logger.info(f"\n🎯 BEST CONFIGURATION (Reward: {self.best_reward:.4f}):")
        if self.best_config:
            for key, value in self.best_config.items():
                if key != 'trial_id':
                    logger.info(f"  {key}: {value}")

        # Performance statistics
        rewards = [r.get('reward', 0) for r in self.results_history if 'reward' in r]
        if rewards:
            logger.info(f"\n📈 PERFORMANCE STATISTICS:")
            logger.info(f"  Mean Reward: {np.mean(rewards):.4f}")
            logger.info(f"  Std Reward: {np.std(rewards):.4f}")
            logger.info(f"  Max Reward: {np.max(rewards):.4f}")
            logger.info(f"  Min Reward: {np.min(rewards):.4f}")

        logger.info("="*80)

def main():
    """Main hyperparameter tuning execution"""
    print("🎯 ENSEMBLE TCN-CNN-PPO HYPERPARAMETER TUNING")
    print("🎯 Optimization Target: Composite Score × Net Profit")
    print("🎯 Aim for Highest Reward")
    print("="*80)

    # Initialize tuner
    tuner = HyperparameterTuner()

    # Run tuning (adjust num_trials based on computational resources)
    best_config, best_reward = tuner.run_hyperparameter_tuning(num_trials=10)

    if best_config:
        print(f"\n🎉 HYPERPARAMETER TUNING COMPLETED")
        print(f"🏆 Best Reward: {best_reward:.4f}")
        print(f"💾 Best configuration saved to: best_hyperparameter_config.json")
        print(f"🚀 Use best configuration for final model training")
    else:
        print("❌ Hyperparameter tuning failed")

if __name__ == "__main__":
    main()
