#!/usr/bin/env python3
"""
Ensemble TCN-CNN-PPO Trainer - ONLY Ensemble Architecture
Per Master Document: "ensemble TCN CNN PPO architecture for trading systems"
Compliance Module Integration
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import logging
from datetime import datetime

# Import validation modules
from security_compliance_validator import ValidationGate

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TCNEnsemble(nn.Module):
    """TCN Ensemble Component"""
    
    def __init__(self, input_channels=4, num_models=3):
        super(TCNEnsemble, self).__init__()
        
        # Multiple TCN models with different configurations
        self.tcn_models = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=3, padding=1, dilation=1),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=3, padding=1, dilation=2),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=5, padding=2, dilation=1),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=3, padding=1, dilation=3),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=7, padding=3, dilation=1),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=3, padding=1, dilation=4),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            )
        ])
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, x):
        """Forward pass through TCN ensemble"""
        outputs = []
        for tcn_model in self.tcn_models:
            output = tcn_model(x).squeeze(-1)  # [batch, 64]
            outputs.append(output)
        
        # Weighted ensemble
        ensemble_output = torch.zeros_like(outputs[0])
        for i, output in enumerate(outputs):
            ensemble_output += self.ensemble_weights[i] * output
        
        return ensemble_output  # [batch, 64]

class CNNEnsemble(nn.Module):
    """CNN Ensemble Component"""
    
    def __init__(self, input_channels=4, num_models=3):
        super(CNNEnsemble, self).__init__()
        
        # Multiple CNN models with different configurations
        self.cnn_models = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=5, padding=2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=5, padding=2),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ),
            nn.Sequential(
                nn.Conv1d(input_channels, 64, kernel_size=7, padding=3),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Conv1d(64, 64, kernel_size=7, padding=3),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            )
        ])
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, x):
        """Forward pass through CNN ensemble"""
        outputs = []
        for cnn_model in self.cnn_models:
            output = cnn_model(x).squeeze(-1)  # [batch, 64]
            outputs.append(output)
        
        # Weighted ensemble
        ensemble_output = torch.zeros_like(outputs[0])
        for i, output in enumerate(outputs):
            ensemble_output += self.ensemble_weights[i] * output
        
        return ensemble_output  # [batch, 64]

class PPOEnsemble(nn.Module):
    """PPO Ensemble Component"""
    
    def __init__(self, input_size=135, num_models=3):
        super(PPOEnsemble, self).__init__()
        
        # Multiple PPO policy networks
        self.policy_models = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_size, 256),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Linear(128, 3),
                nn.Softmax(dim=-1)
            ),
            nn.Sequential(
                nn.Linear(input_size, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, 3),
                nn.Softmax(dim=-1)
            ),
            nn.Sequential(
                nn.Linear(input_size, 128),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 3),
                nn.Softmax(dim=-1)
            )
        ])
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.ones(num_models) / num_models)
        
    def forward(self, x):
        """Forward pass through PPO ensemble"""
        outputs = []
        for policy_model in self.policy_models:
            output = policy_model(x)  # [batch, 3]
            outputs.append(output)
        
        # Weighted ensemble
        ensemble_output = torch.zeros_like(outputs[0])
        for i, output in enumerate(outputs):
            ensemble_output += self.ensemble_weights[i] * output
        
        return ensemble_output  # [batch, 3]

class EnsembleTCNCNNPPOModel(nn.Module):
    """MASTER DOCUMENT COMPLIANT: Ensemble TCN-CNN-PPO Architecture"""
    
    def __init__(self):
        super(EnsembleTCNCNNPPOModel, self).__init__()
        
        # Ensemble components
        self.tcn_ensemble = TCNEnsemble(input_channels=4, num_models=3)
        self.cnn_ensemble = CNNEnsemble(input_channels=4, num_models=3)
        
        # Grid processor (7 features)
        self.grid_processor = nn.Sequential(
            nn.Linear(7, 14),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(14, 7)
        )
        
        # PPO ensemble (135 features: 64 TCN + 64 CNN + 7 Grid)
        self.ppo_ensemble = PPOEnsemble(input_size=135, num_models=3)
        
    def forward(self, market_data, grid_features):
        """Forward pass through complete ensemble"""
        # Handle batch dimension
        if len(market_data.shape) == 2:
            market_data = market_data.unsqueeze(0)
        if len(grid_features.shape) == 1:
            grid_features = grid_features.unsqueeze(0)
        
        # TCN Ensemble processing
        tcn_features = self.tcn_ensemble(market_data)  # [batch, 64]
        
        # CNN Ensemble processing
        cnn_features = self.cnn_ensemble(market_data)  # [batch, 64]
        
        # Grid processing
        grid_processed = self.grid_processor(grid_features)  # [batch, 7]
        
        # Combine features (64 + 64 + 7 = 135)
        combined_features = torch.cat([
            tcn_features,
            cnn_features,
            grid_processed
        ], dim=1)
        
        # PPO Ensemble final decision
        ensemble_policy = self.ppo_ensemble(combined_features)  # [batch, 3]
        
        return ensemble_policy

class EnsembleCompliantTrainer:
    """Ensemble Trainer with Compliance Module Integration"""
    
    def __init__(self):
        self.validator = ValidationGate()
        self.model = EnsembleTCNCNNPPOModel()
        
        # EXACT master document parameters
        self.params = {
            'grid_spacing': 0.0025,      # EXACTLY 0.25%
            'grid_tolerance': 0.001,     # EXACTLY 0.1%
            'risk_reward_ratio': 2.5,    # EXACTLY 2.5:1
            'risk_per_trade': 0.01,      # EXACTLY 1%
            'architecture': 'ensemble_tcn_cnn_ppo'  # COMPLIANCE: Ensemble only
        }
        
        # Master document targets
        self.targets = {
            'win_rate': 60.0,
            'trades_per_day': 8.0,
            'composite_score': 0.8
        }
        
        # Data split
        self.data_split = {
            'training_years': [2022, 2023],
            'out_of_sample_years': [2024],
            'backtest_years': [2021]
        }
        
    def validate_ensemble_compliance(self):
        """COMPLIANCE MODULE: Validate ensemble architecture"""
        logger.info("📋 COMPLIANCE: Validating ensemble architecture...")
        
        # Check ensemble components
        ensemble_checks = {
            'tcn_ensemble': hasattr(self.model, 'tcn_ensemble'),
            'cnn_ensemble': hasattr(self.model, 'cnn_ensemble'),
            'ppo_ensemble': hasattr(self.model, 'ppo_ensemble'),
            'ensemble_weights': all([
                hasattr(self.model.tcn_ensemble, 'ensemble_weights'),
                hasattr(self.model.cnn_ensemble, 'ensemble_weights'),
                hasattr(self.model.ppo_ensemble, 'ensemble_weights')
            ]),
            'multiple_models': all([
                len(self.model.tcn_ensemble.tcn_models) >= 3,
                len(self.model.cnn_ensemble.cnn_models) >= 3,
                len(self.model.ppo_ensemble.policy_models) >= 3
            ])
        }
        
        compliance_passed = all(ensemble_checks.values())
        
        logger.info(f"📋 COMPLIANCE: Ensemble Architecture Check:")
        for check, passed in ensemble_checks.items():
            logger.info(f"   {check}: {'✅ PASSED' if passed else '❌ FAILED'}")
        
        if compliance_passed:
            logger.info("✅ COMPLIANCE: Ensemble architecture validated")
        else:
            logger.error("❌ COMPLIANCE: Ensemble architecture validation failed")
        
        return compliance_passed
    
    def load_and_validate_data(self):
        """Load and validate data with compliance checks"""
        logger.info("🔒 ENSEMBLE: Loading and validating data...")
        
        data_path = 'real_bitcoin_4year_data.json'
        
        if not self.validator.security_module.validate_data_authenticity(data_path):
            logger.error("❌ SECURITY: Data validation failed")
            return None, None, None
        
        df = pd.read_json(data_path, orient='records')
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['year'] = df['datetime'].dt.year
        
        train_data = df[df['year'].isin(self.data_split['training_years'])].copy()
        out_of_sample_data = df[df['year'].isin(self.data_split['out_of_sample_years'])].copy()
        backtest_data = df[df['year'].isin(self.data_split['backtest_years'])].copy()
        
        logger.info(f"✅ ENSEMBLE: Data loaded")
        logger.info(f"   Training: {len(train_data)} samples")
        logger.info(f"   Out-of-Sample: {len(out_of_sample_data)} samples")
        logger.info(f"   Backtest: {len(backtest_data)} samples")
        
        return train_data, out_of_sample_data, backtest_data

    def prepare_ensemble_training_data(self, df):
        """Prepare training data for ensemble architecture"""
        logger.info("🎯 ENSEMBLE: Preparing ensemble training data...")

        market_features = []
        grid_features = []
        labels = []

        sequence_length = 4
        grid_signals = 0

        for i in range(len(df) - sequence_length - 10):
            # Market features (4 indicators)
            price_seq = df['close'].iloc[i:i+sequence_length].values
            rsi_seq = df['rsi'].iloc[i:i+sequence_length].values
            vwap_seq = df['vwap'].iloc[i:i+sequence_length].values
            volume_seq = df['volume'].iloc[i:i+sequence_length].values

            # Normalize
            price_seq = price_seq / np.max(price_seq)
            rsi_seq = rsi_seq / 100.0
            vwap_seq = vwap_seq / np.max(vwap_seq)
            volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq

            market_tensor = np.array([price_seq, rsi_seq, vwap_seq, volume_seq])
            market_features.append(market_tensor)

            # Grid features (7 features)
            current_idx = i + sequence_length - 1
            current_price = df['close'].iloc[current_idx]

            base_price = 100000
            grid_level = int((current_price - base_price) / (base_price * self.params['grid_spacing']))
            nearest_grid_price = base_price * (1 + grid_level * self.params['grid_spacing'])
            grid_distance = abs(current_price - nearest_grid_price) / current_price
            at_grid_level = grid_distance <= self.params['grid_tolerance']

            if at_grid_level:
                grid_signals += 1

            next_grid_up = nearest_grid_price * (1 + self.params['grid_spacing'])
            next_grid_down = nearest_grid_price * (1 - self.params['grid_spacing'])

            grid_vector = np.array([
                nearest_grid_price / 100000.0,
                float(at_grid_level),
                grid_distance,
                next_grid_up / 100000.0,
                next_grid_down / 100000.0,
                self.params['grid_spacing'],
                1.0 if at_grid_level else 0.0
            ])

            grid_features.append(grid_vector)

            # Enhanced labeling for ensemble
            future_prices = df['close'].iloc[current_idx+1:current_idx+11].values

            if len(future_prices) > 0 and at_grid_level:
                max_future = np.max(future_prices)
                min_future = np.min(future_prices)

                up_potential = (max_future - current_price) / current_price
                down_potential = (current_price - min_future) / current_price

                # Enhanced labeling for better ensemble performance
                if up_potential > down_potential * self.params['risk_reward_ratio']:
                    labels.append(0)  # BUY
                elif down_potential > up_potential * self.params['risk_reward_ratio']:
                    labels.append(1)  # SELL
                else:
                    labels.append(2)  # HOLD
            else:
                labels.append(2)  # HOLD

        market_features = np.array(market_features)
        grid_features = np.array(grid_features)
        labels = np.array(labels)

        buy_labels = np.sum(labels == 0)
        sell_labels = np.sum(labels == 1)
        hold_labels = np.sum(labels == 2)
        trading_rate = (buy_labels + sell_labels) / len(labels) * 100

        logger.info(f"🎯 ENSEMBLE: Training data prepared")
        logger.info(f"   Total samples: {len(market_features)}")
        logger.info(f"   Grid signals: {grid_signals}")
        logger.info(f"   Labels: BUY: {buy_labels}, SELL: {sell_labels}, HOLD: {hold_labels}")
        logger.info(f"   Trading rate: {trading_rate:.1f}%")

        return market_features, grid_features, labels

    def train_ensemble_model(self, train_data):
        """Train ensemble model with compliance validation"""
        logger.info("🎯 ENSEMBLE: Starting ensemble training...")

        # COMPLIANCE: Validate ensemble architecture
        if not self.validate_ensemble_compliance():
            logger.error("❌ COMPLIANCE: Ensemble architecture validation failed")
            return None

        # PRE-TRAINING VALIDATION
        code_files = [__file__]
        data_path = 'real_bitcoin_4year_data.json'

        if not self.validator.validate_before_training(code_files, data_path, self.params):
            logger.error("❌ VALIDATION GATE: Pre-training validation failed")
            return None

        # Prepare training data
        market_features, grid_features, labels = self.prepare_ensemble_training_data(train_data)
        if market_features is None:
            return None

        # Convert to tensors
        X_market = torch.FloatTensor(market_features)
        X_grid = torch.FloatTensor(grid_features)
        y_labels = torch.LongTensor(labels)

        # Split data
        train_size = int(0.8 * len(X_market))
        X_train_market = X_market[:train_size]
        X_train_grid = X_grid[:train_size]
        y_train = y_labels[:train_size]

        X_val_market = X_market[train_size:]
        X_val_grid = X_grid[train_size:]
        y_val = y_labels[train_size:]

        # Ensemble training setup
        class_weights = torch.FloatTensor([8.0, 8.0, 1.0])  # Balanced for ensemble
        criterion = nn.CrossEntropyLoss(weight=class_weights)

        # Separate optimizers for ensemble components
        tcn_optimizer = optim.AdamW(self.model.tcn_ensemble.parameters(), lr=0.001, weight_decay=1e-4)
        cnn_optimizer = optim.AdamW(self.model.cnn_ensemble.parameters(), lr=0.001, weight_decay=1e-4)
        ppo_optimizer = optim.AdamW(self.model.ppo_ensemble.parameters(), lr=0.0005, weight_decay=1e-4)
        grid_optimizer = optim.AdamW(self.model.grid_processor.parameters(), lr=0.001, weight_decay=1e-4)

        # Schedulers
        tcn_scheduler = optim.lr_scheduler.CosineAnnealingLR(tcn_optimizer, T_max=50)
        cnn_scheduler = optim.lr_scheduler.CosineAnnealingLR(cnn_optimizer, T_max=50)
        ppo_scheduler = optim.lr_scheduler.CosineAnnealingLR(ppo_optimizer, T_max=50)
        grid_scheduler = optim.lr_scheduler.CosineAnnealingLR(grid_optimizer, T_max=50)

        # Training parameters
        best_val_accuracy = 0.0
        best_model_state = None
        patience = 20
        patience_counter = 0
        batch_size = 64
        max_epochs = 50

        logger.info("🎯 ENSEMBLE: Training ensemble components...")

        for epoch in range(max_epochs):
            self.model.train()
            epoch_train_loss = 0.0
            num_batches = 0

            # Mini-batch training
            for i in range(0, len(X_train_market), batch_size):
                batch_market = X_train_market[i:i+batch_size]
                batch_grid = X_train_grid[i:i+batch_size]
                batch_labels = y_train[i:i+batch_size]

                # Forward pass through ensemble
                ensemble_policy = self.model(batch_market, batch_grid)

                # Calculate loss
                loss = criterion(ensemble_policy, batch_labels)

                # Backward pass for all ensemble components
                tcn_optimizer.zero_grad()
                cnn_optimizer.zero_grad()
                ppo_optimizer.zero_grad()
                grid_optimizer.zero_grad()

                loss.backward()

                # Gradient clipping for ensemble stability
                torch.nn.utils.clip_grad_norm_(self.model.tcn_ensemble.parameters(), max_norm=0.5)
                torch.nn.utils.clip_grad_norm_(self.model.cnn_ensemble.parameters(), max_norm=0.5)
                torch.nn.utils.clip_grad_norm_(self.model.ppo_ensemble.parameters(), max_norm=0.5)
                torch.nn.utils.clip_grad_norm_(self.model.grid_processor.parameters(), max_norm=0.5)

                # Update all optimizers
                tcn_optimizer.step()
                cnn_optimizer.step()
                ppo_optimizer.step()
                grid_optimizer.step()

                epoch_train_loss += loss.item()
                num_batches += 1

            train_loss = epoch_train_loss / num_batches

            # Update schedulers
            tcn_scheduler.step()
            cnn_scheduler.step()
            ppo_scheduler.step()
            grid_scheduler.step()

            # Validation
            self.model.eval()
            with torch.no_grad():
                val_ensemble_policy = self.model(X_val_market, X_val_grid)
                val_loss = criterion(val_ensemble_policy, y_val)

                # Calculate validation accuracy
                val_predictions = torch.argmax(val_ensemble_policy, dim=1)
                val_accuracy = (val_predictions == y_val).float().mean()

            # Save best model
            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy
                best_model_state = self.model.state_dict().copy()
                patience_counter = 0
            else:
                patience_counter += 1

            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch:2d}: Train: {train_loss:.4f}, Val: {val_loss:.4f}, Acc: {val_accuracy:.3f}")

            # Early stopping
            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch} - best accuracy: {best_val_accuracy:.3f}")
                break

        # Load best model
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        # Save ensemble model
        model_path = '02_signal_generator/models/ensemble_tcn_cnn_ppo_model.pth'
        os.makedirs(os.path.dirname(model_path), exist_ok=True)

        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'architecture': 'Ensemble TCN-CNN-PPO (Master Document Compliant)',
                'training_date': datetime.now().isoformat(),
                'ensemble_components': {
                    'tcn_models': 3,
                    'cnn_models': 3,
                    'ppo_models': 3
                },
                'compliance_validated': True,
                'security_validated': True,
                'best_val_accuracy': float(best_val_accuracy),
                'parameters': self.params
            }
        }, model_path)

        logger.info(f"✅ ENSEMBLE: Model saved to: {model_path}")
        logger.info(f"🎯 Best Validation Accuracy: {best_val_accuracy:.3f}")
        return model_path

    def evaluate_ensemble_model(self, test_data, phase_name, model_path):
        """Evaluate ensemble model with compliance checks"""
        logger.info(f"📊 ENSEMBLE: Evaluating {phase_name} with ensemble architecture...")

        # Security check
        if not self.validator.security_module.check_model_integrity(model_path):
            logger.error("❌ SECURITY: Model integrity check failed")
            return None

        # Ensemble trading simulation
        initial_balance = 1000.0
        current_balance = initial_balance
        trades = []
        position = None

        grid_signals = 0
        actual_trades = 0
        ensemble_decisions = {'BUY': 0, 'SELL': 0, 'HOLD': 0}

        for i in range(len(test_data) - 10):
            if i < 4:
                continue

            current_price = test_data['close'].iloc[i]

            # Grid calculation
            base_price = 100000
            grid_level = int((current_price - base_price) / (base_price * self.params['grid_spacing']))
            nearest_grid_price = base_price * (1 + grid_level * self.params['grid_spacing'])
            grid_distance = abs(current_price - nearest_grid_price) / current_price
            at_grid_level = grid_distance <= self.params['grid_tolerance']

            if at_grid_level:
                grid_signals += 1

                if position is None:
                    # Prepare inputs
                    price_seq = test_data['close'].iloc[i-4:i].values
                    rsi_seq = test_data['rsi'].iloc[i-4:i].values
                    vwap_seq = test_data['vwap'].iloc[i-4:i].values
                    volume_seq = test_data['volume'].iloc[i-4:i].values

                    # Normalize
                    price_seq = price_seq / np.max(price_seq)
                    rsi_seq = rsi_seq / 100.0
                    vwap_seq = vwap_seq / np.max(vwap_seq)
                    volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq

                    market_tensor = torch.FloatTensor([price_seq, rsi_seq, vwap_seq, volume_seq])

                    next_grid_up = nearest_grid_price * (1 + self.params['grid_spacing'])
                    next_grid_down = nearest_grid_price * (1 - self.params['grid_spacing'])

                    grid_vector = torch.FloatTensor([
                        nearest_grid_price / 100000.0,
                        float(at_grid_level),
                        grid_distance,
                        next_grid_up / 100000.0,
                        next_grid_down / 100000.0,
                        self.params['grid_spacing'],
                        1.0
                    ])

                    # Get ensemble prediction
                    self.model.eval()
                    with torch.no_grad():
                        market_tensor = market_tensor.unsqueeze(0)
                        grid_vector = grid_vector.unsqueeze(0)

                        ensemble_policy = self.model(market_tensor, grid_vector)
                        action = torch.argmax(ensemble_policy, dim=1).item()

                    # Track ensemble decisions
                    if action == 0:
                        ensemble_decisions['BUY'] += 1
                    elif action == 1:
                        ensemble_decisions['SELL'] += 1
                    else:
                        ensemble_decisions['HOLD'] += 1

                    # Execute trades based on ensemble decision
                    if action == 0:  # BUY
                        stop_loss_price = current_price * (1 - 0.01)
                        take_profit_price = current_price * (1 + 0.025)

                        position = {
                            'type': 'BUY',
                            'entry_price': current_price,
                            'entry_index': i,
                            'stop_loss': stop_loss_price,
                            'take_profit': take_profit_price,
                            'grid_level': nearest_grid_price
                        }
                        actual_trades += 1

                    elif action == 1:  # SELL
                        stop_loss_price = current_price * (1 + 0.01)
                        take_profit_price = current_price * (1 - 0.025)

                        position = {
                            'type': 'SELL',
                            'entry_price': current_price,
                            'entry_index': i,
                            'stop_loss': stop_loss_price,
                            'take_profit': take_profit_price,
                            'grid_level': nearest_grid_price
                        }
                        actual_trades += 1

            # Check position exit
            if position is not None:
                exit_triggered = False
                exit_reason = ""

                if position['type'] == 'BUY':
                    if current_price <= position['stop_loss']:
                        exit_triggered = True
                        exit_reason = "STOP_LOSS"
                    elif current_price >= position['take_profit']:
                        exit_triggered = True
                        exit_reason = "TAKE_PROFIT"
                else:  # SELL
                    if current_price >= position['stop_loss']:
                        exit_triggered = True
                        exit_reason = "STOP_LOSS"
                    elif current_price <= position['take_profit']:
                        exit_triggered = True
                        exit_reason = "TAKE_PROFIT"

                if exit_triggered:
                    # Position sizing
                    risk_amount = current_balance * self.params['risk_per_trade']

                    if position['type'] == 'BUY':
                        stop_loss_distance = position['entry_price'] - position['stop_loss']
                        position_size = risk_amount / stop_loss_distance if stop_loss_distance > 0 else 0
                        pnl = position_size * (current_price - position['entry_price'])
                    else:  # SELL
                        stop_loss_distance = position['stop_loss'] - position['entry_price']
                        position_size = risk_amount / stop_loss_distance if stop_loss_distance > 0 else 0
                        pnl = position_size * (position['entry_price'] - current_price)

                    current_balance += pnl

                    trade = {
                        'type': position['type'],
                        'entry_price': position['entry_price'],
                        'exit_price': current_price,
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'grid_level': position['grid_level']
                    }

                    trades.append(trade)
                    position = None

        # Calculate metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
        net_profit = current_balance - initial_balance

        days_in_period = len(test_data) / 24
        trades_per_day = total_trades / days_in_period if days_in_period > 0 else 0

        # Composite score
        if total_trades > 0 and len(winning_trades) > 0:
            avg_win = np.mean([t['pnl'] for t in winning_trades])
            losing_trades = [t for t in trades if t['pnl'] <= 0]
            avg_loss = np.mean([abs(t['pnl']) for t in losing_trades]) if losing_trades else 1
            profit_factor = avg_win / avg_loss if avg_loss > 0 else 3.0

            composite_score = (
                min(1.0, profit_factor / 3.0) * 0.28 +
                min(1.0, profit_factor / 3.0) * 0.22 +
                min(1.0, profit_factor / 1.5) * 0.20 +
                min(1.0, win_rate / 60.0) * 0.15 +
                0.10 +
                min(1.0, trades_per_day / 8.0) * 0.05
            )
        else:
            composite_score = 0.0

        corrected_reward = composite_score * max(0, net_profit)

        # Ensemble decision analysis
        total_decisions = sum(ensemble_decisions.values())
        decision_rates = {k: v/total_decisions*100 if total_decisions > 0 else 0 for k, v in ensemble_decisions.items()}

        logger.info(f"📊 ENSEMBLE {phase_name} Results:")
        logger.info(f"   Grid Signals: {grid_signals}")
        logger.info(f"   Actual Trades: {actual_trades}")
        logger.info(f"   Total Trades: {total_trades}")
        logger.info(f"   Win Rate: {win_rate:.1f}%")
        logger.info(f"   Trades/Day: {trades_per_day:.1f}")
        logger.info(f"   Net Profit: ${net_profit:.2f}")
        logger.info(f"   Composite Score: {composite_score:.3f}")
        logger.info(f"   Corrected Reward: {corrected_reward:.2f}")
        logger.info(f"   Ensemble Decisions: BUY: {decision_rates['BUY']:.1f}%, SELL: {decision_rates['SELL']:.1f}%, HOLD: {decision_rates['HOLD']:.1f}%")

        return {
            'phase': phase_name,
            'grid_signals': grid_signals,
            'actual_trades': actual_trades,
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'trades_per_day': trades_per_day,
            'composite_score': composite_score,
            'net_profit': net_profit,
            'corrected_reward': corrected_reward,
            'ensemble_decisions': ensemble_decisions,
            'final_balance': current_balance
        }

    def run_ensemble_training(self):
        """Run complete ensemble training with compliance validation"""
        logger.info("🎯 ENSEMBLE: Starting ensemble TCN-CNN-PPO training...")

        # Load data
        train_data, out_of_sample_data, backtest_data = self.load_and_validate_data()
        if train_data is None:
            return False

        # Train ensemble model
        model_path = self.train_ensemble_model(train_data)
        if model_path is None:
            return False

        # Evaluate on all phases
        results = {}
        results['training'] = self.evaluate_ensemble_model(train_data, "Training", model_path)
        results['out_of_sample'] = self.evaluate_ensemble_model(out_of_sample_data, "Out-of-Sample", model_path)
        results['backtest'] = self.evaluate_ensemble_model(backtest_data, "Backtest", model_path)

        # VALIDATION GATE
        if not self.validator.validate_before_results(results, model_path):
            logger.error("❌ VALIDATION GATE: Results validation failed")
            logger.error("🚨 RESULTS BLOCKED - NOT COMPLIANT")
            return False

        # Check hierarchy
        training_reward = results['training']['corrected_reward']
        out_of_sample_reward = results['out_of_sample']['corrected_reward']
        backtest_reward = results['backtest']['corrected_reward']

        hierarchy_correct = (backtest_reward > out_of_sample_reward and
                           backtest_reward > training_reward and
                           out_of_sample_reward > training_reward)

        # Check compliance against targets
        compliance_checks = {}
        for phase, result in results.items():
            if result:
                compliance_checks[phase] = {
                    'win_rate': result['win_rate'] >= self.targets['win_rate'],
                    'trades_per_day': result['trades_per_day'] >= self.targets['trades_per_day'],
                    'composite_score': result['composite_score'] >= self.targets['composite_score']
                }

        overall_compliance = all(all(checks.values()) for checks in compliance_checks.values())

        logger.info("\n" + "=" * 80)
        logger.info("🎯 ENSEMBLE TCN-CNN-PPO TRAINING RESULTS")
        logger.info("=" * 80)
        logger.info(f"🎯 ENSEMBLE ARCHITECTURE:")
        logger.info(f"   • TCN Ensemble: 3 models with different dilations")
        logger.info(f"   • CNN Ensemble: 3 models with different kernel sizes")
        logger.info(f"   • PPO Ensemble: 3 policy networks with different architectures")
        logger.info(f"   • Weighted ensemble voting for final decisions")
        logger.info(f"📊 PERFORMANCE:")
        logger.info(f"   Training Reward: {training_reward:.2f}")
        logger.info(f"   Out-of-Sample Reward: {out_of_sample_reward:.2f}")
        logger.info(f"   Backtest Reward: {backtest_reward:.2f}")
        logger.info(f"   Hierarchy Correct: {'✅ YES' if hierarchy_correct else '❌ NO'}")
        logger.info(f"📋 COMPLIANCE:")
        logger.info(f"   Overall Compliance: {'✅ PASSED' if overall_compliance else '❌ FAILED'}")
        logger.info(f"   Architecture: ✅ ENSEMBLE TCN-CNN-PPO (Master Document Compliant)")
        logger.info("=" * 80)

        # Save comprehensive results
        comprehensive_results = {
            'ensemble_results': results,
            'hierarchy_correct': hierarchy_correct,
            'overall_compliance': overall_compliance,
            'compliance_checks': compliance_checks,
            'architecture': 'Ensemble TCN-CNN-PPO',
            'ensemble_components': {
                'tcn_models': 3,
                'cnn_models': 3,
                'ppo_models': 3
            },
            'parameters': self.params,
            'targets': self.targets,
            'timestamp': datetime.now().isoformat()
        }

        with open('ensemble_tcn_cnn_ppo_results.json', 'w') as f:
            import json
            json.dump(comprehensive_results, f, indent=2)

        logger.info("📄 Ensemble results saved to: ensemble_tcn_cnn_ppo_results.json")

        return overall_compliance

def main():
    """Main ensemble training function"""
    print("🎯 ENSEMBLE TCN-CNN-PPO TRAINER")
    print("=" * 80)
    print("📋 MASTER DOCUMENT COMPLIANCE:")
    print("   ✅ ONLY Ensemble Architecture (as requested)")
    print("   ✅ Removed all non-ensemble implementations")
    print("   ✅ Added to compliance module validation")
    print("=" * 80)
    print("🎯 ENSEMBLE COMPONENTS:")
    print("   • TCN Ensemble: 3 models (different dilations)")
    print("   • CNN Ensemble: 3 models (different kernel sizes)")
    print("   • PPO Ensemble: 3 policy networks (different architectures)")
    print("   • Weighted ensemble voting for final decisions")
    print("=" * 80)
    print("📊 EXPECTED IMPROVEMENTS:")
    print("   • Better generalization from multiple models")
    print("   • Improved accuracy through ensemble voting")
    print("   • Higher robustness and stability")
    print("   • Enhanced performance towards 60% win rate target")
    print("=" * 80)
    print("🔒 COMPLIANCE MODULE:")
    print("   • Ensemble architecture validation")
    print("   • Security and compliance checks")
    print("   • Master document requirement enforcement")
    print("=" * 80)

    trainer = EnsembleCompliantTrainer()

    if trainer.run_ensemble_training():
        print("✅ ENSEMBLE TCN-CNN-PPO TRAINING SUCCESSFUL!")
        print("🎯 Ensemble architecture implemented and validated")
        print("📊 Multiple models working together for better decisions")
        print("🚀 Ready for deployment authorization")
        return True
    else:
        print("❌ ENSEMBLE TCN-CNN-PPO TRAINING FAILED")
        print("📋 Review compliance requirements")
        print("🔄 Optimize ensemble components if needed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
