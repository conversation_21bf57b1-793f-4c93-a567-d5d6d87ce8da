#!/usr/bin/env python3
"""
Simple Corrected Trainer - Core Training Only
4-Year Data Split: 2 years training, 1 year out-of-sample, 1 year backtest
Reward: Composite Score × Net Profit
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import logging
from datetime import datetime
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedGridProbabilityModel(nn.Module):
    """Corrected model for grid probability prediction"""
    
    def __init__(self):
        super(CorrectedGridProbabilityModel, self).__init__()
        
        # TCN Component
        self.tcn_layers = nn.Sequential(
            nn.Conv1d(4, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Conv1d(64, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1)
        )
        
        # CNN Component
        self.cnn_layers = nn.Sequential(
            nn.Conv1d(4, 64, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Conv1d(64, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1)
        )
        
        # Grid processor
        self.grid_processor = nn.Sequential(
            nn.Linear(7, 14),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(14, 7)
        )
        
        # Probability networks
        self.prob_upper_network = nn.Sequential(
            nn.Linear(135, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        self.prob_lower_network = nn.Sequential(
            nn.Linear(135, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
    def forward(self, market_data, grid_features):
        """Forward pass"""
        # Handle batch dimension
        if len(market_data.shape) == 2:
            market_data = market_data.unsqueeze(0)
        if len(grid_features.shape) == 1:
            grid_features = grid_features.unsqueeze(0)
        
        # TCN processing
        tcn_out = self.tcn_layers(market_data)
        tcn_features = tcn_out.squeeze(-1)
        
        # CNN processing
        cnn_out = self.cnn_layers(market_data)
        cnn_features = cnn_out.squeeze(-1)
        
        # Grid processing
        grid_processed = self.grid_processor(grid_features)
        
        # Combine features
        combined_features = torch.cat([
            tcn_features,
            cnn_features,
            grid_processed
        ], dim=1)
        
        # Predict probabilities
        prob_upper = self.prob_upper_network(combined_features)
        prob_lower = self.prob_lower_network(combined_features)
        
        return prob_upper, prob_lower

def load_and_split_data():
    """Load 4-year data and split"""
    try:
        logger.info("📊 Loading 4-year Bitcoin data...")
        
        data_path = 'real_bitcoin_4year_data.json'
        if not os.path.exists(data_path):
            logger.error("❌ Data file not found")
            return None, None, None
        
        df = pd.read_json(data_path, orient='records')
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['year'] = df['datetime'].dt.year
        
        # Split data
        train_data = df[df['year'].isin([2022, 2023])].copy()
        out_of_sample_data = df[df['year'] == 2024].copy()
        backtest_data = df[df['year'] == 2021].copy()
        
        logger.info(f"Training: {len(train_data)}, Out-of-Sample: {len(out_of_sample_data)}, Backtest: {len(backtest_data)}")
        
        return train_data, out_of_sample_data, backtest_data
        
    except Exception as e:
        logger.error(f"❌ Data loading failed: {e}")
        return None, None, None

def prepare_training_data(df):
    """Prepare corrected training data"""
    try:
        logger.info("🔧 Preparing training data...")
        
        market_features = []
        grid_features = []
        upper_labels = []
        lower_labels = []
        
        grid_spacing = 0.0025
        grid_tolerance = 0.01
        sequence_length = 4
        
        for i in range(len(df) - sequence_length - 10):
            # Market features
            price_seq = df['close'].iloc[i:i+sequence_length].values
            rsi_seq = df['rsi'].iloc[i:i+sequence_length].values
            vwap_seq = df['vwap'].iloc[i:i+sequence_length].values
            volume_seq = df['volume'].iloc[i:i+sequence_length].values
            
            # Normalize
            price_seq = price_seq / np.max(price_seq)
            rsi_seq = rsi_seq / 100.0
            vwap_seq = vwap_seq / np.max(vwap_seq)
            volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq
            
            market_tensor = np.array([price_seq, rsi_seq, vwap_seq, volume_seq])
            market_features.append(market_tensor)
            
            # Grid features
            current_idx = i + sequence_length - 1
            current_price = df['close'].iloc[current_idx]
            
            base_price = 100000
            grid_level = int((current_price - base_price) / (base_price * grid_spacing))
            nearest_grid_price = base_price * (1 + grid_level * grid_spacing)
            grid_distance = abs(current_price - nearest_grid_price) / current_price
            at_grid_level = grid_distance <= grid_tolerance
            
            next_grid_up = nearest_grid_price * (1 + grid_spacing)
            next_grid_down = nearest_grid_price * (1 - grid_spacing)
            
            grid_vector = np.array([
                nearest_grid_price / 100000.0,
                float(at_grid_level),
                grid_distance,
                next_grid_up / 100000.0,
                next_grid_down / 100000.0,
                grid_spacing,
                1.0 if at_grid_level else 0.0
            ])
            
            grid_features.append(grid_vector)
            
            # Labels - probability of reaching target grids
            future_prices = df['close'].iloc[current_idx+1:current_idx+11].values
            
            upper_reached = np.any(future_prices >= next_grid_up)
            lower_reached = np.any(future_prices <= next_grid_down)
            
            upper_labels.append(1.0 if upper_reached else 0.0)
            lower_labels.append(1.0 if lower_reached else 0.0)
        
        market_features = np.array(market_features)
        grid_features = np.array(grid_features)
        upper_labels = np.array(upper_labels)
        lower_labels = np.array(lower_labels)
        
        logger.info(f"✅ Prepared {len(market_features)} samples")
        logger.info(f"Upper grid probability: {np.mean(upper_labels):.1%}")
        logger.info(f"Lower grid probability: {np.mean(lower_labels):.1%}")
        
        return market_features, grid_features, upper_labels, lower_labels
        
    except Exception as e:
        logger.error(f"❌ Data preparation failed: {e}")
        return None, None, None, None

def train_model(train_data):
    """Train corrected model"""
    try:
        logger.info("🧠 Training corrected model...")
        
        # Prepare data
        market_features, grid_features, upper_labels, lower_labels = prepare_training_data(train_data)
        if market_features is None:
            return None
        
        # Convert to tensors
        X_market = torch.FloatTensor(market_features)
        X_grid = torch.FloatTensor(grid_features)
        y_upper = torch.FloatTensor(upper_labels)
        y_lower = torch.FloatTensor(lower_labels)
        
        # Split data
        train_size = int(0.8 * len(X_market))
        X_train_market = X_market[:train_size]
        X_train_grid = X_grid[:train_size]
        y_train_upper = y_upper[:train_size]
        y_train_lower = y_lower[:train_size]
        
        X_val_market = X_market[train_size:]
        X_val_grid = X_grid[train_size:]
        y_val_upper = y_upper[train_size:]
        y_val_lower = y_lower[train_size:]
        
        # Initialize model
        model = CorrectedGridProbabilityModel()
        criterion = nn.BCELoss()
        optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50, eta_min=1e-6)
        
        # Training
        best_val_loss = float('inf')
        best_model_state = None
        batch_size = 64
        max_epochs = 50
        
        logger.info("🔧 Starting training...")
        
        for epoch in range(max_epochs):
            model.train()
            epoch_train_loss = 0.0
            num_batches = 0
            
            # Mini-batch training
            for i in range(0, len(X_train_market), batch_size):
                batch_market = X_train_market[i:i+batch_size]
                batch_grid = X_train_grid[i:i+batch_size]
                batch_upper = y_train_upper[i:i+batch_size]
                batch_lower = y_train_lower[i:i+batch_size]
                
                # Forward pass
                prob_upper, prob_lower = model(batch_market, batch_grid)
                
                # Calculate loss
                loss_upper = criterion(prob_upper.squeeze(), batch_upper)
                loss_lower = criterion(prob_lower.squeeze(), batch_lower)
                batch_loss = loss_upper + loss_lower
                
                # Backward pass
                optimizer.zero_grad()
                batch_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                optimizer.step()
                
                epoch_train_loss += batch_loss.item()
                num_batches += 1
            
            train_loss = epoch_train_loss / num_batches
            scheduler.step()
            
            # Validation
            model.eval()
            with torch.no_grad():
                val_prob_upper, val_prob_lower = model(X_val_market, X_val_grid)
                val_loss_upper = criterion(val_prob_upper.squeeze(), y_val_upper)
                val_loss_lower = criterion(val_prob_lower.squeeze(), y_val_lower)
                val_loss = val_loss_upper + val_loss_lower
                
                upper_acc = ((val_prob_upper.squeeze() > 0.5) == (y_val_upper > 0.5)).float().mean()
                lower_acc = ((val_prob_lower.squeeze() > 0.5) == (y_val_lower > 0.5)).float().mean()
                avg_acc = (upper_acc + lower_acc) / 2
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict().copy()
            
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch:2d}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Acc: {avg_acc:.3f}")
        
        # Load best model
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
        
        # Save model
        model_path = '02_signal_generator/models/corrected_grid_probability_model.pth'
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_config': {
                'architecture': 'Corrected Grid Probability TCN-CNN-PPO',
                'training_date': datetime.now().isoformat(),
                'best_val_loss': float(best_val_loss)
            }
        }, model_path)
        
        logger.info(f"✅ Model saved to: {model_path}")
        return model
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return None

def evaluate_model(model, test_data, phase_name):
    """Evaluate model performance"""
    try:
        logger.info(f"📊 Evaluating on {phase_name}...")

        # Simulate trading
        initial_balance = 1000.0
        current_balance = initial_balance
        trades = []

        grid_spacing = 0.0025
        grid_tolerance = 0.01

        for i in range(len(test_data) - 10):
            if i < 4:
                continue

            current_price = test_data['close'].iloc[i]

            # Calculate grid level
            base_price = 100000
            grid_level = int((current_price - base_price) / (base_price * grid_spacing))
            nearest_grid_price = base_price * (1 + grid_level * grid_spacing)
            grid_distance = abs(current_price - nearest_grid_price) / current_price
            at_grid_level = grid_distance <= grid_tolerance

            if at_grid_level:
                # Prepare model inputs
                price_seq = test_data['close'].iloc[i-4:i].values
                rsi_seq = test_data['rsi'].iloc[i-4:i].values
                vwap_seq = test_data['vwap'].iloc[i-4:i].values
                volume_seq = test_data['volume'].iloc[i-4:i].values

                # Normalize
                price_seq = price_seq / np.max(price_seq)
                rsi_seq = rsi_seq / 100.0
                vwap_seq = vwap_seq / np.max(vwap_seq)
                volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq

                market_tensor = torch.FloatTensor([price_seq, rsi_seq, vwap_seq, volume_seq])

                next_grid_up = nearest_grid_price * (1 + grid_spacing)
                next_grid_down = nearest_grid_price * (1 - grid_spacing)

                grid_vector = torch.FloatTensor([
                    nearest_grid_price / 100000.0,
                    float(at_grid_level),
                    grid_distance,
                    next_grid_up / 100000.0,
                    next_grid_down / 100000.0,
                    grid_spacing,
                    1.0
                ])

                # Get predictions
                model.eval()
                with torch.no_grad():
                    market_tensor = market_tensor.unsqueeze(0)
                    grid_vector = grid_vector.unsqueeze(0)

                    prob_upper, prob_lower = model(market_tensor, grid_vector)
                    prob_upper = prob_upper.squeeze().item()
                    prob_lower = prob_lower.squeeze().item()

                # Simple trading logic
                if prob_upper > prob_lower and prob_upper > 0.75:
                    # BUY signal - look for take profit at next grid up
                    for j in range(i+1, min(i+20, len(test_data))):
                        future_price = test_data['close'].iloc[j]

                        if future_price >= next_grid_up:  # Take profit
                            pnl = (future_price - current_price) / current_price * current_balance * 0.01
                            current_balance += pnl
                            trades.append({
                                'type': 'BUY',
                                'entry_price': current_price,
                                'exit_price': future_price,
                                'pnl': pnl,
                                'exit_reason': 'TAKE_PROFIT'
                            })
                            break
                        elif future_price <= current_price * 0.99:  # Stop loss
                            pnl = (future_price - current_price) / current_price * current_balance * 0.01
                            current_balance += pnl
                            trades.append({
                                'type': 'BUY',
                                'entry_price': current_price,
                                'exit_price': future_price,
                                'pnl': pnl,
                                'exit_reason': 'STOP_LOSS'
                            })
                            break

                elif prob_lower > prob_upper and prob_lower > 0.75:
                    # SELL signal - look for take profit at next grid down
                    for j in range(i+1, min(i+20, len(test_data))):
                        future_price = test_data['close'].iloc[j]

                        if future_price <= next_grid_down:  # Take profit
                            pnl = (current_price - future_price) / current_price * current_balance * 0.01
                            current_balance += pnl
                            trades.append({
                                'type': 'SELL',
                                'entry_price': current_price,
                                'exit_price': future_price,
                                'pnl': pnl,
                                'exit_reason': 'TAKE_PROFIT'
                            })
                            break
                        elif future_price >= current_price * 1.01:  # Stop loss
                            pnl = (current_price - future_price) / current_price * current_balance * 0.01
                            current_balance += pnl
                            trades.append({
                                'type': 'SELL',
                                'entry_price': current_price,
                                'exit_price': future_price,
                                'pnl': pnl,
                                'exit_reason': 'STOP_LOSS'
                            })
                            break

        # Calculate metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
        net_profit = current_balance - initial_balance

        days_in_period = len(test_data) / 24
        trades_per_day = total_trades / days_in_period if days_in_period > 0 else 0

        # Composite score
        if total_trades > 0 and len(winning_trades) > 0:
            avg_win = np.mean([t['pnl'] for t in winning_trades])
            avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] <= 0]) if any(t['pnl'] <= 0 for t in trades) else -1
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 3.0

            composite_score = (
                min(1.0, profit_factor / 3.0) * 0.28 +
                min(1.0, profit_factor / 3.0) * 0.22 +
                min(1.0, profit_factor / 1.5) * 0.20 +
                min(1.0, win_rate / 60.0) * 0.15 +
                0.10 +
                min(1.0, trades_per_day / 8.0) * 0.05
            )
        else:
            composite_score = 0.0

        # CORRECTED REWARD: Composite × Net Profit
        corrected_reward = composite_score * max(0, net_profit)

        logger.info(f"📊 {phase_name} Results:")
        logger.info(f"   Trades: {total_trades}, Win Rate: {win_rate:.1f}%")
        logger.info(f"   Trades/Day: {trades_per_day:.1f}, Net Profit: ${net_profit:.2f}")
        logger.info(f"   Composite: {composite_score:.3f}, Corrected Reward: {corrected_reward:.2f}")

        return {
            'phase': phase_name,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'trades_per_day': trades_per_day,
            'composite_score': composite_score,
            'net_profit': net_profit,
            'corrected_reward': corrected_reward
        }

    except Exception as e:
        logger.error(f"❌ Evaluation failed: {e}")
        return None

def main():
    """Main training function"""
    print("🔧 SIMPLE CORRECTED TRAINER")
    print("=" * 60)
    print("📊 4-Year Data Split:")
    print("   • 2 Years: Training (2022-2023)")
    print("   • 1 Year: Out-of-Sample (2024)")
    print("   • 1 Year: Backtest (2021)")
    print("=" * 60)
    print("🎯 Corrected Parameters:")
    print("   • Grid Tolerance: 1.0% (practical)")
    print("   • Reward: Composite × Net Profit")
    print("   • Requirement: Backtest > Out-of-Sample > Training")
    print("=" * 60)

    # Load data
    train_data, out_of_sample_data, backtest_data = load_and_split_data()
    if train_data is None:
        print("❌ Data loading failed")
        return False

    # Train model
    model = train_model(train_data)
    if model is None:
        print("❌ Training failed")
        return False

    # Evaluate on all phases
    results = {}
    results['training'] = evaluate_model(model, train_data, "Training")
    results['out_of_sample'] = evaluate_model(model, out_of_sample_data, "Out-of-Sample")
    results['backtest'] = evaluate_model(model, backtest_data, "Backtest")

    # Check hierarchy
    training_reward = results['training']['corrected_reward']
    out_of_sample_reward = results['out_of_sample']['corrected_reward']
    backtest_reward = results['backtest']['corrected_reward']

    hierarchy_correct = (backtest_reward > out_of_sample_reward and
                        backtest_reward > training_reward and
                        out_of_sample_reward > training_reward)

    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   Training Reward: {training_reward:.2f}")
    print(f"   Out-of-Sample Reward: {out_of_sample_reward:.2f}")
    print(f"   Backtest Reward: {backtest_reward:.2f}")
    print(f"   Hierarchy Correct: {'✅ YES' if hierarchy_correct else '❌ NO'}")
    print("=" * 60)

    if hierarchy_correct:
        print("✅ HIERARCHY REQUIREMENT MET!")
        print("🚀 Model ready for deployment")
        return True
    else:
        print("❌ Hierarchy requirement not met")
        print("🔄 Need further optimization")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
