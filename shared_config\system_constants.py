#!/usr/bin/env python3
"""
System Constants
Master document compliant constants shared across all modules
"""

# MASTER DOCUMENT COMPLIANCE CONSTANTS
MASTER_DOC_VERSION = "1.0.0"
SYSTEM_NAME = "Enhanced Grid-Aware TCN-CNN-PPO Trading System"

# GRID SYSTEM CONSTANTS (PERMANENTLY LOCKED)
GRID_SPACING = 0.0025  # Exactly 0.25%
GRID_TOLERANCE = 0.00001  # Exactly 0.001%
GRID_ONLY_TRADING = True  # MANDATORY

# PERFORMANCE TARGETS (PERMANENTLY LOCKED)
WIN_RATE_TARGET = 0.60  # Exactly 60%
TRADES_PER_DAY_TARGET = 8.0  # Exactly 8 trades
COMPOSITE_SCORE_TARGET = 0.8  # Exactly 0.8
CONFIDENCE_THRESHOLD = 0.75  # Minimum 75%

# RISK MANAGEMENT (PERMANENTLY LOCKED)
RISK_PER_TRADE = 0.01  # Maximum 1%
RISK_REWARD_RATIO = 2.5  # Exactly 2.5:1
MAX_DAILY_TRADES = 8  # Hard cap
MAX_DAILY_RISK = 0.08  # Maximum 8%
MAX_DRAWDOWN = 0.10  # Maximum 10%

# TCN-CNN-PPO MODEL ARCHITECTURE (PERMANENTLY LOCKED)
TCN_FEATURES = 64  # Exactly 64
CNN_FEATURES = 64  # Exactly 64
GRID_FEATURES = 7  # Exactly 7
TOTAL_FEATURES = 135  # Exactly 135 (64+64+7)
SIGNAL_PERSISTENCE = 300  # Exactly 5 minutes

# TRADING CONFIGURATION
SYMBOL = "BTCUSDT"
MARGIN_TYPE = "ISOLATED"
LEVERAGE = 10
INTERVAL = "30m"

# SYSTEM PORTS
SIGNAL_GENERATOR_PORT = 5000
TRADING_ENGINE_PORT = 5001

# SECURITY SETTINGS
PRE_EXECUTION_SCAN_REQUIRED = True
CODE_INTEGRITY_CHECK_REQUIRED = True
AUTHORIZATION_REQUIRED = True
EMERGENCY_PROTOCOLS_ENABLED = True

# TELEGRAM SETTINGS
TELEGRAM_INTEGRATION_ENABLED = True
NOTIFICATION_ENABLED = True

# LOGGING SETTINGS
LOG_LEVEL = "INFO"
LOG_ROTATION = True
MAX_LOG_SIZE = "10MB"
BACKUP_COUNT = 5

# FILE PATHS
CONFIG_DIR = "shared_config"
LOGS_DIR = "08_logging_system/logs"
MODELS_DIR = "02_signal_generator/models"

# COMPLIANCE RULES
COMPLIANCE_CHECK_INTERVAL = 60  # seconds
VIOLATION_THRESHOLD = 3  # max violations before shutdown
MONITORING_ENABLED = True

# PERFORMANCE METRICS
METRICS_UPDATE_INTERVAL = 30  # seconds
PERFORMANCE_TRACKING_ENABLED = True
REAL_TIME_MONITORING = True
