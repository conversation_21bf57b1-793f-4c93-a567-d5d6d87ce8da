#!/usr/bin/env python3
"""
Test Quick Master Document Compliant Trained Model
Test the newly trained model to see if it generates trading signals
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import logging
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickGridAwareTCNCNNPPOEnsemble(nn.Module):
    """Quick Grid-Aware TCN-CNN-PPO Ensemble (matches trained model)"""
    
    def __init__(self):
        super(QuickGridAwareTCNCNNPPOEnsemble, self).__init__()
        
        # EXACT ARCHITECTURE FROM TRAINING
        hidden_dim = 64
        dropout_rate = 0.1
        
        # TCN Component
        self.tcn = nn.Sequential(
            nn.Conv1d(7, hidden_dim, 3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.AdaptiveAvgPool1d(1),
            nn.<PERSON><PERSON>(),
            nn.Linear(hidden_dim, 32)  # TCN Features: 32
        )
        
        # CNN Component
        self.cnn = nn.Sequential(
            nn.Conv1d(7, hidden_dim, 5, padding=2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(hidden_dim, 32)  # CNN Features: 32
        )
        
        # PPO Component
        self.ppo_actor = nn.Sequential(
            nn.Linear(71, 128),  # 32 TCN + 32 CNN + 7 Grid = 71
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 3)  # BUY, SELL, HOLD probabilities
        )
        
        # Individual classifiers
        self.tcn_classifier = nn.Linear(32, 3)
        self.cnn_classifier = nn.Linear(32, 3)
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
        
        logger.info("🏗️ Quick Grid-Aware TCN-CNN-PPO Ensemble Loaded")
    
    def forward(self, x, grid_features):
        """Forward pass with grid-aware processing"""
        x_transposed = x.transpose(1, 2)  # [batch, features, sequence]
        
        # Component processing
        tcn_features = self.tcn(x_transposed)      # [batch, 32]
        cnn_features = self.cnn(x_transposed)      # [batch, 32]
        
        # PPO state vector (71 features total)
        ppo_state = torch.cat([tcn_features, cnn_features, grid_features], dim=1)
        
        # Individual predictions
        tcn_pred = torch.softmax(self.tcn_classifier(tcn_features), dim=1)
        cnn_pred = torch.softmax(self.cnn_classifier(cnn_features), dim=1)
        ppo_pred = torch.softmax(self.ppo_actor(ppo_state), dim=1)
        
        # Ensemble combination
        weights = torch.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = weights[0] * tcn_pred + weights[1] * cnn_pred + weights[2] * ppo_pred
        
        return ensemble_pred, {
            'tcn_pred': tcn_pred,
            'cnn_pred': cnn_pred,
            'ppo_pred': ppo_pred,
            'weights': weights,
            'confidence': torch.max(ensemble_pred, dim=1)[0]
        }

class QuickModelTester:
    """Test the quick trained model"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # EXACT MASTER DOCUMENT PARAMETERS
        self.grid_spacing = 0.0025          # EXACTLY 0.25%
        self.grid_tolerance = 0.001         # EXACTLY 0.1%
        self.confidence_threshold = 0.50    # Lower threshold for testing
        self.sequence_length = 30           # Match training
        
        logger.info("🔍 Quick Model Tester Initialized")
        logger.info(f"🖥️  Device: {self.device}")
    
    def load_test_data(self):
        """Load test data"""
        try:
            logger.info("📊 Loading test Bitcoin data...")
            
            # Load recent data for testing
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').tail(100).reset_index(drop=True)
            
            # Add ATR indicator
            df = self.add_real_atr_indicator(df)
            
            logger.info(f"📊 Test data loaded: {len(df)} samples")
            return df
            
        except Exception as e:
            logger.error(f"❌ Test data loading failed: {e}")
            return None
    
    def add_real_atr_indicator(self, df):
        """Add REAL ATR indicator"""
        try:
            tr_list = []
            for i in range(len(df)):
                if i == 0:
                    tr = df.iloc[i]['high'] - df.iloc[i]['low']
                else:
                    tr1 = df.iloc[i]['high'] - df.iloc[i]['low']
                    tr2 = abs(df.iloc[i]['high'] - df.iloc[i-1]['close'])
                    tr3 = abs(df.iloc[i]['low'] - df.iloc[i-1]['close'])
                    tr = max(tr1, tr2, tr3)
                tr_list.append(tr)
            
            df['atr'] = pd.Series(tr_list).rolling(14, min_periods=1).mean().fillna(0)
            return df
            
        except Exception as e:
            logger.error(f"❌ ATR calculation failed: {e}")
            return df
    
    def calculate_grid_levels(self, price):
        """Calculate grid levels per master document"""
        base_level = round(price / (1 + self.grid_spacing)) * (1 + self.grid_spacing)
        grid_level = base_level
        grid_distance = abs(price - grid_level) / grid_level
        
        return grid_level, grid_distance
    
    def prepare_input(self, data, index):
        """Prepare model input"""
        try:
            if index < self.sequence_length:
                return None, None
            
            # Market data sequence
            sequence = data.iloc[index-self.sequence_length:index][
                ['open', 'high', 'low', 'close', 'rsi', 'vwap', 'atr']
            ].values
            
            if np.any(np.isnan(sequence)) or np.any(np.isinf(sequence)):
                return None, None
            
            # Grid features (7 features per master document)
            current_row = data.iloc[index]
            current_price = float(current_row['close'])
            grid_level, grid_distance = self.calculate_grid_levels(current_price)
            
            # EXACT MASTER DOCUMENT GRID FEATURES
            grid_features = [
                grid_level,                                    # Current grid level
                grid_distance,                                 # Distance to grid
                self.grid_tolerance,                          # Grid tolerance
                grid_level * (1 + self.grid_spacing),        # Next grid up
                grid_level * (1 - self.grid_spacing),        # Next grid down
                self.grid_spacing,                            # Grid spacing (0.25%)
                1.0 if grid_distance <= self.grid_tolerance else 0.0  # Grid compliance
            ]
            
            X = torch.FloatTensor(sequence).unsqueeze(0)
            grid_tensor = torch.FloatTensor(grid_features).unsqueeze(0)
            
            return X, grid_tensor
            
        except Exception as e:
            return None, None
    
    def test_quick_model(self):
        """Test the quick trained model"""
        logger.info("🚀 Testing Quick Master Document Compliant Trained Model")
        logger.info("="*80)
        
        # Load test data
        data = self.load_test_data()
        if data is None:
            logger.error("❌ Test data loading failed")
            return None
        
        # Load trained model
        try:
            logger.info("🔍 Loading quick trained model...")
            
            model = QuickGridAwareTCNCNNPPOEnsemble()
            checkpoint = torch.load('quick_master_compliant_trained_model.pth', map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            logger.info("✅ Quick trained model loaded successfully")
            logger.info(f"📊 Model info: {checkpoint.get('training_type', 'Unknown')}")
            logger.info(f"🎯 Training accuracy: {checkpoint.get('val_accuracy', 'Unknown'):.3f}")
            
        except Exception as e:
            logger.error(f"❌ Model loading failed: {e}")
            return None
        
        # Test signal generation
        signals_generated = []
        
        logger.info("🔄 Testing signal generation...")
        
        for i in range(self.sequence_length, min(self.sequence_length + 20, len(data))):
            # Prepare input
            X, grid_tensor = self.prepare_input(data, i)
            if X is None or grid_tensor is None:
                continue
            
            # Get prediction
            X, grid_tensor = X.to(self.device), grid_tensor.to(self.device)
            
            with torch.no_grad():
                prediction, components = model(X, grid_tensor)
                probabilities = torch.softmax(prediction, dim=1)[0]
                signal = torch.argmax(prediction, dim=1).item()
                confidence = torch.max(probabilities).item()
            
            signal_record = {
                'index': i,
                'price': float(data.iloc[i]['close']),
                'signal': signal,
                'signal_name': ['BUY', 'SELL', 'HOLD'][signal],
                'confidence': confidence,
                'probabilities': {
                    'BUY': float(probabilities[0]),
                    'SELL': float(probabilities[1]),
                    'HOLD': float(probabilities[2])
                },
                'components': {
                    'tcn_weight': components['weights'][0].item(),
                    'cnn_weight': components['weights'][1].item(),
                    'ppo_weight': components['weights'][2].item()
                },
                'qualified': confidence >= self.confidence_threshold
            }
            
            signals_generated.append(signal_record)
            
            logger.info(f"Signal {len(signals_generated)}: {signal_record['signal_name']} "
                      f"(conf: {confidence:.3f}) at ${signal_record['price']:.2f} "
                      f"{'✅' if signal_record['qualified'] else '❌'}")
        
        # Analyze results
        if signals_generated:
            total_signals = len(signals_generated)
            buy_signals = sum(1 for s in signals_generated if s['signal'] == 0)
            sell_signals = sum(1 for s in signals_generated if s['signal'] == 1)
            hold_signals = sum(1 for s in signals_generated if s['signal'] == 2)
            qualified_signals = sum(1 for s in signals_generated if s['qualified'])
            
            avg_confidence = np.mean([s['confidence'] for s in signals_generated])
            
            logger.info(f"\n📊 SIGNAL ANALYSIS:")
            logger.info(f"   Total Signals: {total_signals}")
            logger.info(f"   BUY: {buy_signals} ({buy_signals/total_signals*100:.1f}%)")
            logger.info(f"   SELL: {sell_signals} ({sell_signals/total_signals*100:.1f}%)")
            logger.info(f"   HOLD: {hold_signals} ({hold_signals/total_signals*100:.1f}%)")
            logger.info(f"   Qualified (≥{self.confidence_threshold:.0%}): {qualified_signals} ({qualified_signals/total_signals*100:.1f}%)")
            logger.info(f"   Average Confidence: {avg_confidence:.3f}")
            
            # Save results
            test_results = {
                'test_type': 'Quick Master Document Compliant Trained Model Test',
                'model_file': 'quick_master_compliant_trained_model.pth',
                'confidence_threshold': self.confidence_threshold,
                'signals_generated': signals_generated,
                'summary': {
                    'total_signals': total_signals,
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'hold_signals': hold_signals,
                    'qualified_signals': qualified_signals,
                    'avg_confidence': avg_confidence
                },
                'timestamp': datetime.now().isoformat()
            }
            
            with open('quick_model_test_results.json', 'w') as f:
                json.dump(test_results, f, indent=2, default=str)
            
            logger.info("💾 Test results saved to: quick_model_test_results.json")
            
            return test_results
        else:
            logger.error("❌ No signals generated")
            return None

def main():
    """Main testing execution"""
    print("🔍 QUICK MASTER DOCUMENT COMPLIANT TRAINED MODEL TEST")
    print("✅ Testing Newly Trained Model")
    print("🎯 Signal Generation Analysis")
    print("📊 Confidence Threshold Testing")
    print("="*80)
    
    try:
        # Initialize tester
        tester = QuickModelTester()
        
        # Test model
        results = tester.test_quick_model()
        
        if results:
            summary = results['summary']
            print("\n🎉 QUICK MODEL TEST COMPLETED!")
            print(f"📊 Total Signals: {summary['total_signals']}")
            print(f"🎯 Qualified Signals: {summary['qualified_signals']}")
            print(f"📈 Average Confidence: {summary['avg_confidence']:.3f}")
            print("📊 Check quick_model_test_results.json for details")
        else:
            print("\n❌ Quick model test failed")
            
    except Exception as e:
        print(f"\n🚨 TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
