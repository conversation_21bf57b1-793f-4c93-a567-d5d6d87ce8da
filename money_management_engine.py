#!/usr/bin/env python3
"""
MONEY MANAGEMENT ENGINE
Implements the $100 equivalent compounding system with $1 SL / $2.5 TP
Fully integrated with existing trading system
"""

import sys
import json
import logging
from datetime import datetime
import decimal
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP

sys.path.append('01_binance_connector')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MoneyManager:
    """
    Money Management Engine for $100 Equivalent Compounding System
    
    SPECIFICATIONS:
    - Starting Balance Equivalent: $100.00
    - Risk per Trade: 1% = $1.00 (Stop Loss)
    - Reward per Trade: 2.5% = $2.50 (Take Profit)
    - Risk-Reward Ratio: 2.5:1
    - Compounding: ENABLED
    """
    
    def __init__(self, starting_balance_equivalent=100.0):
        # Core Parameters
        self.starting_balance_equivalent = starting_balance_equivalent  # $100.00
        self.risk_percentage = 0.01                                    # 1%
        self.reward_percentage = 0.025                                 # 2.5%
        self.risk_reward_ratio = 2.5                                   # 2.5:1
        
        # Risk Management
        self.minimum_balance = starting_balance_equivalent * 0.1       # $10.00
        self.maximum_position_percent = 0.9                            # 90% of balance
        self.maximum_daily_loss_percent = 0.05                         # 5% daily loss limit
        self.maximum_drawdown_percent = 0.2                            # 20% max drawdown
        
        # State Tracking
        self.current_balance = 0.0
        self.daily_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.trade_history = []
        
        # Integration
        self.binance_connector = None
        
        logger.info("MONEY MANAGEMENT ENGINE INITIALIZED")
        logger.info(f"Starting Balance Equivalent: ${self.starting_balance_equivalent:.2f}")
        logger.info(f"Risk per Trade: {self.risk_percentage:.1%} (${self.starting_balance_equivalent * self.risk_percentage:.2f})")
        logger.info(f"Reward per Trade: {self.reward_percentage:.1%} (${self.starting_balance_equivalent * self.reward_percentage:.2f})")
        logger.info(f"Risk-Reward Ratio: {self.risk_reward_ratio:.1f}:1")
    
    def initialize_balance_tracking(self):
        """Initialize balance tracking with current account"""
        try:
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance_connector = BinanceRealMoneyConnector()
            
            # Get current balance
            balance_info = self.binance_connector.get_isolated_margin_balance()
            self.current_balance = balance_info['total_usdt_value']
            
            logger.info("BALANCE TRACKING INITIALIZED")
            logger.info(f"Current Account Balance: ${self.current_balance:.2f}")
            
            # Determine scaling factor
            if self.current_balance >= self.starting_balance_equivalent:
                self.scale_factor = 1.0
                logger.info("Using full $100 equivalent parameters")
            else:
                self.scale_factor = self.current_balance / self.starting_balance_equivalent
                logger.info(f"Scaling factor: {self.scale_factor:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Balance tracking initialization failed: {e}")
            return False
    
    def calculate_position_size(self, current_price=None):
        """
        Calculate position size using money management rules
        
        CALCULATION:
        Position Size = Risk Amount / Risk Percentage
        For $1 risk at 1%: Position Size = $1.00 / 0.01 = $100.00
        """
        try:
            # Get current price if not provided
            if not current_price:
                ticker = self.binance_connector.client.get_symbol_ticker(symbol='BTCUSDT')
                current_price = float(ticker['price'])
            
            # Update current balance
            balance_info = self.binance_connector.get_isolated_margin_balance()
            self.current_balance = balance_info['total_usdt_value']
            
            # Calculate base amounts
            if self.current_balance >= self.starting_balance_equivalent:
                # Use full $100 equivalent parameters
                base_risk_amount = self.starting_balance_equivalent * self.risk_percentage    # $1.00
                base_reward_amount = self.starting_balance_equivalent * self.reward_percentage # $2.50
                base_position_size = base_risk_amount / self.risk_percentage                   # $100.00
            else:
                # Scale proportionally to current balance
                scale_factor = self.current_balance / self.starting_balance_equivalent
                base_risk_amount = self.starting_balance_equivalent * self.risk_percentage * scale_factor
                base_reward_amount = self.starting_balance_equivalent * self.reward_percentage * scale_factor
                base_position_size = base_risk_amount / self.risk_percentage
            
            # Ensure position doesn't exceed balance limits
            max_position = self.current_balance * self.maximum_position_percent
            position_size_usd = min(base_position_size, max_position)
            
            # Calculate BTC quantity with proper rounding
            btc_quantity = position_size_usd / current_price
            
            # Get Binance symbol requirements
            symbol_info = self.binance_connector.client.get_symbol_info('BTCUSDT')
            step_size = None
            min_qty = None
            min_notional = None
            tick_size = None
            
            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                    min_qty = float(filter_item['minQty'])
                elif filter_item['filterType'] == 'MIN_NOTIONAL':
                    min_notional = float(filter_item['minNotional'])
                elif filter_item['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_item['tickSize'])
            
            # Round BTC quantity to step size
            decimal.getcontext().rounding = ROUND_DOWN
            qty_decimal = Decimal(str(btc_quantity))
            step_decimal = Decimal(str(step_size))
            btc_quantity = float(qty_decimal.quantize(step_decimal))
            
            # Ensure minimum requirements
            btc_quantity = max(btc_quantity, min_qty)
            
            # Check minimum notional
            if btc_quantity * current_price < min_notional:
                btc_quantity = min_notional / current_price
                btc_quantity = float(Decimal(str(btc_quantity)).quantize(step_decimal, rounding=ROUND_HALF_UP))
            
            # Recalculate actual values
            actual_position_size = btc_quantity * current_price
            actual_risk_amount = actual_position_size * self.risk_percentage
            actual_reward_amount = actual_position_size * self.reward_percentage
            
            # Calculate SL and TP prices
            decimal.getcontext().rounding = ROUND_HALF_UP
            price_decimal = Decimal(str(current_price))
            tick_decimal = Decimal(str(tick_size))
            
            entry_price = float(price_decimal.quantize(tick_decimal))
            stop_loss_price = float(Decimal(str(entry_price * (1 - self.risk_percentage))).quantize(tick_decimal))
            take_profit_price = float(Decimal(str(entry_price * (1 + self.reward_percentage))).quantize(tick_decimal))
            
            # Create position data
            position_data = {
                # Core Position Data
                'entry_price': entry_price,
                'btc_quantity': btc_quantity,
                'position_size_usd': actual_position_size,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                
                # Risk/Reward Data
                'risk_amount': actual_risk_amount,
                'reward_amount': actual_reward_amount,
                'risk_percentage': self.risk_percentage,
                'reward_percentage': self.reward_percentage,
                'risk_reward_ratio': actual_reward_amount / actual_risk_amount,
                
                # Money Management Data
                'current_balance': self.current_balance,
                'starting_balance_equivalent': self.starting_balance_equivalent,
                'scale_factor': self.current_balance / self.starting_balance_equivalent,
                'balance_utilization_percent': (actual_position_size / self.current_balance) * 100,
                
                # Binance Requirements
                'step_size': step_size,
                'tick_size': tick_size,
                'min_notional': min_notional,
                'min_qty': min_qty,
                
                # Formatted Strings for Orders
                'quantity_str': f"{btc_quantity:.8f}".rstrip('0').rstrip('.'),
                'entry_price_str': f"{entry_price:.2f}",
                'stop_loss_str': f"{stop_loss_price:.2f}",
                'take_profit_str': f"{take_profit_price:.2f}",
                'stop_limit_str': f"{stop_loss_price * 0.999:.2f}"
            }
            
            logger.info("POSITION SIZE CALCULATED")
            logger.info(f"Current Balance: ${self.current_balance:.2f}")
            logger.info(f"Scale Factor: {position_data['scale_factor']:.3f}x")
            logger.info(f"Position Size: ${actual_position_size:.2f}")
            logger.info(f"BTC Quantity: {btc_quantity:.8f}")
            logger.info(f"Risk: ${actual_risk_amount:.2f} ({self.risk_percentage:.1%})")
            logger.info(f"Reward: ${actual_reward_amount:.2f} ({self.reward_percentage:.1%})")
            logger.info(f"Risk-Reward: {actual_reward_amount/actual_risk_amount:.1f}:1")
            logger.info(f"Balance Utilization: {position_data['balance_utilization_percent']:.1f}%")
            
            return position_data
            
        except Exception as e:
            logger.error(f"Position size calculation failed: {e}")
            return None
    
    def validate_trade_safety(self, position_data):
        """Validate trade meets all safety requirements"""
        try:
            safety_checks = {
                'sufficient_balance': self.current_balance >= self.minimum_balance,
                'position_size_ok': position_data['position_size_usd'] <= (self.current_balance * self.maximum_position_percent),
                'risk_amount_reasonable': position_data['risk_amount'] <= (self.current_balance * 0.02),  # Max 2% risk
                'min_notional_met': position_data['position_size_usd'] >= position_data['min_notional'],
                'risk_reward_ratio_ok': position_data['risk_reward_ratio'] >= 2.0,
                'daily_loss_limit_ok': abs(self.daily_pnl) < (self.current_balance * self.maximum_daily_loss_percent),
                'quantity_valid': position_data['btc_quantity'] >= position_data['min_qty']
            }
            
            all_safe = all(safety_checks.values())
            
            logger.info("TRADE SAFETY VALIDATION")
            for check, result in safety_checks.items():
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"  {check}: {status}")
            
            if all_safe:
                logger.info("✅ TRADE SAFETY VALIDATION: PASSED")
            else:
                logger.warning("⚠️ TRADE SAFETY VALIDATION: FAILED")
                
            return all_safe, safety_checks
            
        except Exception as e:
            logger.error(f"Trade safety validation failed: {e}")
            return False, {}
    
    def record_trade_result(self, trade_result):
        """Record trade result and update statistics"""
        try:
            self.total_trades += 1
            
            # Update win/loss counts
            if trade_result.get('result') == 'WIN':
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Update daily P&L
            pnl = trade_result.get('pnl', 0)
            self.daily_pnl += pnl
            
            # Update balance
            balance_info = self.binance_connector.get_isolated_margin_balance()
            new_balance = balance_info['total_usdt_value']
            balance_change = new_balance - self.current_balance
            
            # Create trade record
            trade_record = {
                'trade_number': self.total_trades,
                'timestamp': datetime.now().isoformat(),
                'result': trade_result.get('result', 'UNKNOWN'),
                'entry_price': trade_result.get('entry_price', 0),
                'exit_price': trade_result.get('exit_price', 0),
                'quantity': trade_result.get('quantity', 0),
                'pnl': pnl,
                'balance_before': self.current_balance,
                'balance_after': new_balance,
                'balance_change': balance_change,
                'win_rate': (self.winning_trades / self.total_trades) * 100,
                'compounding_factor': new_balance / self.starting_balance_equivalent,
                'daily_pnl': self.daily_pnl
            }
            
            self.trade_history.append(trade_record)
            self.current_balance = new_balance
            
            logger.info("TRADE RESULT RECORDED")
            logger.info(f"Trade #{self.total_trades}: {trade_result.get('result', 'UNKNOWN')}")
            logger.info(f"P&L: ${pnl:.2f}")
            logger.info(f"Balance: ${self.current_balance:.2f} → ${new_balance:.2f}")
            logger.info(f"Win Rate: {(self.winning_trades/self.total_trades)*100:.1f}%")
            logger.info(f"Compounding Factor: {new_balance/self.starting_balance_equivalent:.3f}x")
            
            # Save trade history
            self.save_trade_history()
            
            return trade_record
            
        except Exception as e:
            logger.error(f"Trade result recording failed: {e}")
            return None
    
    def get_money_management_status(self):
        """Get comprehensive money management status"""
        try:
            # Update current balance
            if self.binance_connector:
                balance_info = self.binance_connector.get_isolated_margin_balance()
                self.current_balance = balance_info['total_usdt_value']
            
            status = {
                # Balance Information
                'current_balance': self.current_balance,
                'starting_balance_equivalent': self.starting_balance_equivalent,
                'compounding_factor': self.current_balance / self.starting_balance_equivalent,
                'balance_growth_percent': ((self.current_balance - self.starting_balance_equivalent) / self.starting_balance_equivalent) * 100,
                
                # Risk/Reward Information
                'risk_per_trade': self.current_balance * self.risk_percentage if self.current_balance >= self.starting_balance_equivalent else self.current_balance * self.risk_percentage,
                'reward_per_trade': self.current_balance * self.reward_percentage if self.current_balance >= self.starting_balance_equivalent else self.current_balance * self.reward_percentage,
                'risk_reward_ratio': self.risk_reward_ratio,
                
                # Trading Statistics
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
                'daily_pnl': self.daily_pnl,
                
                # Risk Management
                'minimum_balance': self.minimum_balance,
                'maximum_position_percent': self.maximum_position_percent * 100,
                'daily_loss_limit': self.current_balance * self.maximum_daily_loss_percent,
                'drawdown_limit': self.current_balance * self.maximum_drawdown_percent,
                
                # System Status
                'compounding_enabled': True,
                'safety_checks_enabled': True,
                'integration_status': 'OPERATIONAL' if self.binance_connector else 'DISCONNECTED'
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get money management status: {e}")
            return None
    
    def save_trade_history(self):
        """Save trade history to file"""
        try:
            history_data = {
                'money_management_config': {
                    'starting_balance_equivalent': self.starting_balance_equivalent,
                    'risk_percentage': self.risk_percentage,
                    'reward_percentage': self.reward_percentage,
                    'risk_reward_ratio': self.risk_reward_ratio
                },
                'current_status': self.get_money_management_status(),
                'trade_history': self.trade_history,
                'last_updated': datetime.now().isoformat()
            }
            
            filename = f'money_management_history_{datetime.now().strftime("%Y%m%d")}.json'
            with open(filename, 'w') as f:
                json.dump(history_data, f, indent=2, default=str)
            
            logger.info(f"Trade history saved to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save trade history: {e}")

def main():
    """Test money management engine"""
    print("🚀 MONEY MANAGEMENT ENGINE TEST")
    print("$100 Equivalent | $1 SL | $2.5 TP | 2.5:1 RR | Compounding")
    print("="*70)
    
    try:
        # Initialize money manager
        money_manager = MoneyManager()
        
        # Initialize balance tracking
        if not money_manager.initialize_balance_tracking():
            print("❌ Failed to initialize balance tracking")
            return
        
        # Calculate position size
        position_data = money_manager.calculate_position_size()
        if not position_data:
            print("❌ Failed to calculate position size")
            return
        
        # Validate trade safety
        is_safe, safety_checks = money_manager.validate_trade_safety(position_data)
        
        # Get money management status
        status = money_manager.get_money_management_status()
        
        print(f"\n📊 MONEY MANAGEMENT STATUS:")
        print(f"  Current Balance: ${status['current_balance']:.2f}")
        print(f"  Starting Equivalent: ${status['starting_balance_equivalent']:.2f}")
        print(f"  Compounding Factor: {status['compounding_factor']:.3f}x")
        print(f"  Balance Growth: {status['balance_growth_percent']:.1f}%")
        print(f"  Risk per Trade: ${status['risk_per_trade']:.2f}")
        print(f"  Reward per Trade: ${status['reward_per_trade']:.2f}")
        print(f"  Risk-Reward Ratio: {status['risk_reward_ratio']:.1f}:1")
        
        print(f"\n📋 POSITION DATA:")
        print(f"  Position Size: ${position_data['position_size_usd']:.2f}")
        print(f"  BTC Quantity: {position_data['btc_quantity']:.8f}")
        print(f"  Entry Price: ${position_data['entry_price']:.2f}")
        print(f"  Stop Loss: ${position_data['stop_loss_price']:.2f} (${position_data['risk_amount']:.2f})")
        print(f"  Take Profit: ${position_data['take_profit_price']:.2f} (${position_data['reward_amount']:.2f})")
        print(f"  Balance Utilization: {position_data['balance_utilization_percent']:.1f}%")
        
        print(f"\n✅ MONEY MANAGEMENT ENGINE READY!")
        print(f"✅ Integration: {'OPERATIONAL' if status['integration_status'] == 'OPERATIONAL' else 'FAILED'}")
        print(f"✅ Safety Validation: {'PASSED' if is_safe else 'FAILED'}")
        print(f"✅ Compounding: {'ENABLED' if status['compounding_enabled'] else 'DISABLED'}")
        
        if is_safe:
            print(f"\n🚀 READY FOR LIVE TRADING WITH FULL INTEGRATION!")
        else:
            print(f"\n⚠️ SAFETY CHECKS FAILED - REVIEW REQUIRED")
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
