#!/usr/bin/env python3
"""
ANALYZE CURRENT MARKET SITUATION
Check if our trades should have already hit TP or SL
"""

import sys
sys.path.append('01_binance_connector')

def analyze_situation():
    try:
        from binance_real_money_connector import Binance<PERSON>ealMoneyConnector
        
        connector = BinanceRealMoneyConnector()
        
        # Get current price
        ticker = connector.client.get_symbol_ticker(symbol='BTCUSDT')
        current_price = float(ticker['price'])
        
        print('CURRENT MARKET ANALYSIS:')
        print(f'Current BTC Price: ${current_price:.2f}')
        print()
        
        # Our existing trades
        trades = [
            {'entry_order_id': 46196674337, 'entry_price': 119733.06, 'quantity': 0.00008000},
            {'entry_order_id': 46196764679, 'entry_price': 119706.84, 'quantity': 0.00008000}
        ]
        
        print('TRADE ANALYSIS:')
        total_pnl = 0
        
        for i, trade in enumerate(trades, 1):
            entry_price = trade['entry_price']
            sl_price = entry_price * 0.999  # -0.1%
            tp_price = entry_price * 1.0025  # +0.25%
            
            current_pnl_percent = ((current_price - entry_price) / entry_price) * 100
            current_pnl_usd = (current_price - entry_price) * trade['quantity']
            total_pnl += current_pnl_usd
            
            print(f'Trade {i} (Order: {trade["entry_order_id"]}):')
            print(f'  Entry Price: ${entry_price:.2f}')
            print(f'  Current Price: ${current_price:.2f}')
            print(f'  Original SL: ${sl_price:.2f}')
            print(f'  Original TP: ${tp_price:.2f}')
            print(f'  Current P&L: {current_pnl_percent:.3f}% (${current_pnl_usd:.4f})')
            
            if current_price > tp_price:
                print(f'  STATUS: TAKE PROFIT SHOULD HAVE HIT! 🎉')
                result = 'TP HIT'
            elif current_price < sl_price:
                print(f'  STATUS: STOP LOSS SHOULD HAVE HIT 📉')
                result = 'SL HIT'
            else:
                print(f'  STATUS: Trade still active')
                result = 'ACTIVE'
            
            print(f'  RESULT: {result}')
            print()
        
        print(f'TOTAL P&L: ${total_pnl:.4f}')
        
        # Check if we should manually close positions
        if current_price > max(trade['entry_price'] * 1.0025 for trade in trades):
            print('🎉 RECOMMENDATION: MANUALLY CLOSE POSITIONS - TAKE PROFIT LEVELS EXCEEDED!')
            return 'MANUAL_CLOSE_TP'
        elif current_price < min(trade['entry_price'] * 0.999 for trade in trades):
            print('📉 RECOMMENDATION: MANUALLY CLOSE POSITIONS - STOP LOSS LEVELS EXCEEDED!')
            return 'MANUAL_CLOSE_SL'
        else:
            print('📊 RECOMMENDATION: Place new OCO orders with current market prices')
            return 'PLACE_NEW_OCO'
            
    except Exception as e:
        print(f'Error: {e}')
        return 'ERROR'

if __name__ == "__main__":
    result = analyze_situation()
    print(f'\nFINAL RECOMMENDATION: {result}')
