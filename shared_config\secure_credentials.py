#!/usr/bin/env python3
"""
Secure Credentials Manager
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Secure loading of API credentials from environment variables
"""

import os
import logging
from typing import Dict, Optional
from dotenv import load_dotenv
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecureCredentialsManager:
    """Secure credential management system"""
    
    def __init__(self):
        self.credentials = {}
        self.load_environment()
        
    def load_environment(self):
        """Load environment variables from .env file"""
        try:
            # Load .env file if it exists
            if os.path.exists('.env'):
                load_dotenv('.env')
                logger.info("✅ Environment variables loaded from .env file")
            else:
                logger.warning("⚠️ No .env file found, using system environment variables")
                
        except Exception as e:
            logger.error(f"❌ Failed to load environment variables: {e}")
            raise
    
    def get_binance_credentials(self) -> Dict[str, str]:
        """Get Binance API credentials securely"""
        try:
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            if not api_key or not api_secret:
                raise ValueError("Binance API credentials not found in environment variables")
                
            if api_key == 'your_binance_api_key_here' or api_secret == 'your_binance_api_secret_here':
                raise ValueError("Please set actual Binance API credentials in .env file")
                
            # Validate key format (basic check)
            if len(api_key) < 50 or len(api_secret) < 50:
                raise ValueError("Invalid Binance API key format")
                
            return {
                'api_key': api_key,
                'api_secret': api_secret
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get Binance credentials: {e}")
            raise
    
    def get_telegram_credentials(self) -> Dict[str, str]:
        """Get Telegram bot credentials securely"""
        try:
            bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
            chat_id = os.getenv('TELEGRAM_CHAT_ID')
            
            if not bot_token or not chat_id:
                raise ValueError("Telegram credentials not found in environment variables")
                
            if bot_token == 'your_telegram_bot_token_here' or chat_id == 'your_telegram_chat_id_here':
                raise ValueError("Please set actual Telegram credentials in .env file")
                
            # Get authorized users
            authorized_users = os.getenv('AUTHORIZED_USERS', chat_id).split(',')
            admin_users = os.getenv('ADMIN_USERS', chat_id).split(',')
            
            return {
                'bot_token': bot_token,
                'chat_id': chat_id,
                'authorized_users': [user.strip() for user in authorized_users],
                'admin_users': [user.strip() for user in admin_users]
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get Telegram credentials: {e}")
            raise
    
    def get_trading_config(self) -> Dict[str, any]:
        """Get trading configuration from environment"""
        try:
            return {
                'symbol': os.getenv('TRADING_SYMBOL', 'BTCUSDT'),
                'margin_type': os.getenv('MARGIN_TYPE', 'ISOLATED'),
                'leverage': int(os.getenv('LEVERAGE', '10')),
                'starting_balance': float(os.getenv('STARTING_BALANCE', '100.0')),
                'risk_per_trade': float(os.getenv('RISK_PER_TRADE', '0.01')),
                'reward_per_trade': float(os.getenv('REWARD_PER_TRADE', '0.025')),
                'grid_spacing': float(os.getenv('GRID_SPACING', '0.0025')),
                'grid_tolerance': float(os.getenv('GRID_TOLERANCE', '0.00001')),
                'confidence_threshold': float(os.getenv('CONFIDENCE_THRESHOLD', '0.75')),
                'min_trade_interval': int(os.getenv('MIN_TRADE_INTERVAL', '300'))
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get trading config: {e}")
            raise
    
    def get_security_config(self) -> Dict[str, str]:
        """Get security configuration"""
        try:
            return {
                'authorization_code': os.getenv('AUTHORIZATION_CODE', '7c4a8d09d8e3c4a1'),
                'encryption_key': os.getenv('ENCRYPTION_KEY', 'default_key_change_me'),
                'log_level': os.getenv('LOG_LEVEL', 'INFO'),
                'enable_debug': os.getenv('ENABLE_DEBUG', 'false').lower() == 'true'
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get security config: {e}")
            raise
    
    def validate_credentials(self) -> bool:
        """Validate all credentials are properly set"""
        try:
            logger.info("🔍 Validating credentials...")
            
            # Check Binance credentials
            binance_creds = self.get_binance_credentials()
            logger.info("✅ Binance credentials validated")
            
            # Check Telegram credentials
            telegram_creds = self.get_telegram_credentials()
            logger.info("✅ Telegram credentials validated")
            
            # Check trading config
            trading_config = self.get_trading_config()
            logger.info("✅ Trading configuration validated")
            
            # Check security config
            security_config = self.get_security_config()
            logger.info("✅ Security configuration validated")
            
            logger.info("🎯 All credentials validated successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Credential validation failed: {e}")
            return False
    
    def create_secure_config_file(self, config_type: str, output_path: str):
        """Create secure configuration file without exposing credentials"""
        try:
            if config_type == 'binance':
                config = {
                    "binance_config": {
                        "api_key": "LOADED_FROM_ENV",
                        "api_secret": "LOADED_FROM_ENV"
                    },
                    "trading_config": self.get_trading_config(),
                    "master_document_compliance": {
                        "starting_balance": 100.0,
                        "risk_reward_ratio": 2.5,
                        "grid_compliance": True,
                        "real_indicators": True,
                        "tcn_cnn_ppo_model": True,
                        "trades_per_day_target": 8.0,
                        "win_rate_target": 0.60,
                        "composite_score": 0.8,
                        "signal_persistence": 300,
                        "grid_only_trading": True,
                        "pre_execution_scanning": True,
                        "telegram_integration": True
                    }
                }
            elif config_type == 'telegram':
                config = {
                    "bot_token": "LOADED_FROM_ENV",
                    "chat_id": "LOADED_FROM_ENV",
                    "enabled": True,
                    "notifications": {
                        "trade_signals": True,
                        "trade_execution": True,
                        "trade_results": True,
                        "system_status": True,
                        "daily_summary": True,
                        "error_alerts": True
                    },
                    "message_format": {
                        "use_html": True,
                        "include_charts": False,
                        "include_analysis": True
                    }
                }
            else:
                raise ValueError(f"Unknown config type: {config_type}")
            
            with open(output_path, 'w') as f:
                json.dump(config, f, indent=2)
                
            logger.info(f"✅ Secure config file created: {output_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create secure config file: {e}")
            raise

# Global instance
credentials_manager = SecureCredentialsManager()

def get_binance_credentials():
    """Get Binance credentials"""
    return credentials_manager.get_binance_credentials()

def get_telegram_credentials():
    """Get Telegram credentials"""
    return credentials_manager.get_telegram_credentials()

def get_trading_config():
    """Get trading configuration"""
    return credentials_manager.get_trading_config()

def get_security_config():
    """Get security configuration"""
    return credentials_manager.get_security_config()

def validate_all_credentials():
    """Validate all credentials"""
    return credentials_manager.validate_credentials()

if __name__ == "__main__":
    # Test credential loading
    try:
        if validate_all_credentials():
            print("🎯 All credentials loaded successfully")
        else:
            print("❌ Credential validation failed")
    except Exception as e:
        print(f"❌ Error: {e}")
