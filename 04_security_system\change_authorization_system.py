#!/usr/bin/env python3
"""
Change Authorization System
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Authorization manager for system modifications
"""

import json
import hashlib
import time
import logging
from datetime import datetime, timedelta
import os
import getpass

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChangeAuthorizationSystem:
    """Authorization system for managing system changes"""
    
    def __init__(self):
        self.authorized_users = self.load_authorized_users()
        self.pending_changes = []
        self.change_history = []
        self.locked_components = self.load_locked_components()
        self.authorization_required = True
        
    def load_authorized_users(self):
        """Load authorized users from config"""
        try:
            if os.path.exists('authorized_users.json'):
                with open('authorized_users.json', 'r') as f:
                    return json.load(f)
            else:
                # Create default authorized users
                default_users = {
                    'admin': {
                        'password_hash': self.hash_password('admin123'),
                        'permissions': ['ALL'],
                        'created': datetime.now().isoformat()
                    }
                }
                
                with open('authorized_users.json', 'w') as f:
                    json.dump(default_users, f, indent=2)
                
                return default_users
                
        except Exception as e:
            logger.error(f"❌ Failed to load authorized users: {e}")
            return {}
    
    def load_locked_components(self):
        """Load permanently locked components"""
        return {
            'grid_spacing': 0.0025,
            'grid_tolerance': 0.00001,
            'risk_per_trade': 0.01,
            'confidence_threshold': 0.75,
            'max_trades_per_day': 8,
            'win_rate_target': 0.60,
            'composite_score': 0.8,
            'risk_reward_ratio': 2.5,
            'signal_persistence': 300,  # 5 minutes
            'tcn_cnn_ppo_architecture': True,
            'dual_indicator_system': ['RSI', 'VWAP'],
            'port_configuration': [5000, 5001],
            'grid_only_trading_enforcement': True
        }
    
    def hash_password(self, password):
        """Hash password for secure storage"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username, password):
        """Authenticate user credentials"""
        try:
            if username not in self.authorized_users:
                logger.warning(f"⚠️ Authentication failed: Unknown user {username}")
                return False
            
            user_data = self.authorized_users[username]
            password_hash = self.hash_password(password)
            
            if password_hash != user_data['password_hash']:
                logger.warning(f"⚠️ Authentication failed: Invalid password for {username}")
                return False
            
            logger.info(f"✅ User {username} authenticated successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    def check_permissions(self, username, action):
        """Check if user has permission for specific action"""
        try:
            if username not in self.authorized_users:
                return False
            
            user_permissions = self.authorized_users[username]['permissions']
            
            # Check for ALL permission
            if 'ALL' in user_permissions:
                return True
            
            # Check for specific permission
            if action in user_permissions:
                return True
            
            logger.warning(f"⚠️ Permission denied: {username} cannot perform {action}")
            return False
            
        except Exception as e:
            logger.error(f"❌ Permission check error: {e}")
            return False
    
    def is_component_locked(self, component_name):
        """Check if component is permanently locked"""
        return component_name in self.locked_components
    
    def request_change_authorization(self, change_request):
        """Request authorization for system change"""
        try:
            # Validate change request
            if not self.validate_change_request(change_request):
                return False
            
            # Check if component is locked
            if self.is_component_locked(change_request['component']):
                logger.error(f"🔒 Component {change_request['component']} is permanently locked")
                return False
            
            # Add to pending changes
            change_id = self.generate_change_id()
            change_request['id'] = change_id
            change_request['status'] = 'PENDING'
            change_request['requested_at'] = datetime.now().isoformat()
            
            self.pending_changes.append(change_request)
            
            logger.info(f"📝 Change request {change_id} submitted for authorization")
            return change_id
            
        except Exception as e:
            logger.error(f"❌ Change request failed: {e}")
            return False
    
    def validate_change_request(self, change_request):
        """Validate change request format and content"""
        required_fields = ['component', 'change_type', 'description', 'justification']
        
        for field in required_fields:
            if field not in change_request:
                logger.error(f"❌ Missing required field: {field}")
                return False
        
        # Validate change type
        valid_types = ['PARAMETER_CHANGE', 'CODE_MODIFICATION', 'CONFIGURATION_UPDATE']
        if change_request['change_type'] not in valid_types:
            logger.error(f"❌ Invalid change type: {change_request['change_type']}")
            return False
        
        return True
    
    def generate_change_id(self):
        """Generate unique change ID"""
        timestamp = str(int(time.time()))
        return f"CHG_{timestamp}"
    
    def authorize_change(self, change_id, username, password):
        """Authorize a pending change"""
        try:
            # Authenticate user
            if not self.authenticate_user(username, password):
                return False
            
            # Check permissions
            if not self.check_permissions(username, 'AUTHORIZE_CHANGES'):
                return False
            
            # Find pending change
            change_request = None
            for change in self.pending_changes:
                if change['id'] == change_id:
                    change_request = change
                    break
            
            if not change_request:
                logger.error(f"❌ Change request {change_id} not found")
                return False
            
            # Check if component is locked
            if self.is_component_locked(change_request['component']):
                logger.error(f"🔒 Cannot authorize change to locked component: {change_request['component']}")
                return False
            
            # Authorize change
            change_request['status'] = 'AUTHORIZED'
            change_request['authorized_by'] = username
            change_request['authorized_at'] = datetime.now().isoformat()
            
            # Move to history
            self.change_history.append(change_request)
            self.pending_changes.remove(change_request)
            
            # Save change history
            self.save_change_history()
            
            logger.info(f"✅ Change {change_id} authorized by {username}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Change authorization failed: {e}")
            return False
    
    def reject_change(self, change_id, username, password, reason):
        """Reject a pending change"""
        try:
            # Authenticate user
            if not self.authenticate_user(username, password):
                return False
            
            # Check permissions
            if not self.check_permissions(username, 'REJECT_CHANGES'):
                return False
            
            # Find pending change
            change_request = None
            for change in self.pending_changes:
                if change['id'] == change_id:
                    change_request = change
                    break
            
            if not change_request:
                logger.error(f"❌ Change request {change_id} not found")
                return False
            
            # Reject change
            change_request['status'] = 'REJECTED'
            change_request['rejected_by'] = username
            change_request['rejected_at'] = datetime.now().isoformat()
            change_request['rejection_reason'] = reason
            
            # Move to history
            self.change_history.append(change_request)
            self.pending_changes.remove(change_request)
            
            # Save change history
            self.save_change_history()
            
            logger.info(f"❌ Change {change_id} rejected by {username}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Change rejection failed: {e}")
            return False
    
    def save_change_history(self):
        """Save change history to file"""
        try:
            with open('change_history.json', 'w') as f:
                json.dump(self.change_history, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Failed to save change history: {e}")
    
    def get_pending_changes(self):
        """Get list of pending changes"""
        return self.pending_changes.copy()
    
    def get_change_history(self, limit=50):
        """Get change history"""
        return self.change_history[-limit:] if limit else self.change_history
    
    def emergency_unlock(self, username, password, emergency_code):
        """Emergency unlock for critical situations"""
        try:
            # Authenticate user
            if not self.authenticate_user(username, password):
                return False
            
            # Verify emergency code (would be provided separately)
            expected_code = "EMERGENCY_2024"  # This would be more secure in production
            if emergency_code != expected_code:
                logger.error("❌ Invalid emergency code")
                return False
            
            # Log emergency unlock
            emergency_event = {
                'type': 'EMERGENCY_UNLOCK',
                'user': username,
                'timestamp': datetime.now().isoformat(),
                'reason': 'Emergency system unlock'
            }
            
            self.change_history.append(emergency_event)
            self.save_change_history()
            
            logger.critical(f"🚨 EMERGENCY UNLOCK performed by {username}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Emergency unlock failed: {e}")
            return False
    
    def interactive_authorization(self):
        """Interactive authorization interface"""
        print("🔐 Change Authorization System")
        print("=" * 40)
        
        while True:
            print("\nOptions:")
            print("1. View pending changes")
            print("2. Authorize change")
            print("3. Reject change")
            print("4. View change history")
            print("5. Exit")
            
            choice = input("\nSelect option: ").strip()
            
            if choice == '1':
                self.display_pending_changes()
            elif choice == '2':
                self.interactive_authorize()
            elif choice == '3':
                self.interactive_reject()
            elif choice == '4':
                self.display_change_history()
            elif choice == '5':
                break
            else:
                print("❌ Invalid option")
    
    def display_pending_changes(self):
        """Display pending changes"""
        if not self.pending_changes:
            print("📝 No pending changes")
            return
        
        print("\n📝 Pending Changes:")
        print("-" * 60)
        for change in self.pending_changes:
            print(f"ID: {change['id']}")
            print(f"Component: {change['component']}")
            print(f"Type: {change['change_type']}")
            print(f"Description: {change['description']}")
            print(f"Requested: {change['requested_at']}")
            print("-" * 60)
    
    def interactive_authorize(self):
        """Interactive change authorization"""
        change_id = input("Enter change ID: ").strip()
        username = input("Username: ").strip()
        password = getpass.getpass("Password: ")
        
        if self.authorize_change(change_id, username, password):
            print("✅ Change authorized successfully")
        else:
            print("❌ Authorization failed")
    
    def interactive_reject(self):
        """Interactive change rejection"""
        change_id = input("Enter change ID: ").strip()
        username = input("Username: ").strip()
        password = getpass.getpass("Password: ")
        reason = input("Rejection reason: ").strip()
        
        if self.reject_change(change_id, username, password, reason):
            print("❌ Change rejected successfully")
        else:
            print("❌ Rejection failed")
    
    def display_change_history(self):
        """Display change history"""
        history = self.get_change_history(10)  # Last 10 changes
        
        if not history:
            print("📚 No change history")
            return
        
        print("\n📚 Recent Change History:")
        print("-" * 60)
        for change in history:
            print(f"ID: {change.get('id', 'N/A')}")
            print(f"Status: {change['status']}")
            print(f"Component: {change.get('component', 'N/A')}")
            if 'authorized_by' in change:
                print(f"Authorized by: {change['authorized_by']}")
            if 'rejected_by' in change:
                print(f"Rejected by: {change['rejected_by']}")
            print("-" * 60)

# Global authorization instance
authorization_system = ChangeAuthorizationSystem()

def require_authorization(component, change_type, description, justification):
    """Require authorization for system change"""
    change_request = {
        'component': component,
        'change_type': change_type,
        'description': description,
        'justification': justification
    }
    
    return authorization_system.request_change_authorization(change_request)

if __name__ == "__main__":
    authorization_system.interactive_authorization()
