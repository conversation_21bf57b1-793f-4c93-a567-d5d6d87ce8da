#!/usr/bin/env python3
"""
🚀 DIRECT REAL MONEY TRADE EXECUTION
Execute actual real money trades with proper value levels
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('05_trading_engine')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('direct_real_money_trade.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Execute direct real money trade"""
    logger.info("🚀 STARTING DIRECT REAL MONEY TRADE EXECUTION")
    logger.info("=" * 60)
    
    try:
        # Import and initialize Binance connector
        from binance_real_money_connector import BinanceRealMoneyConnector
        binance = BinanceRealMoneyConnector()
        
        # Get account status
        logger.info("💰 Getting account status...")
        balance_info = binance.get_isolated_margin_balance()
        current_balance = balance_info['total_usdt_value']
        usdt_balance = balance_info['usdt_balance']
        
        logger.info(f"💰 Account Status:")
        logger.info(f"   Total Balance: ${current_balance:.2f}")
        logger.info(f"   Available USDT: ${usdt_balance:.2f}")
        
        # Calculate trade amounts (conservative 0.2% risk)
        risk_percentage = 0.002  # 0.2%
        reward_percentage = 0.005  # 0.5%
        
        risk_amount = current_balance * risk_percentage
        reward_amount = current_balance * reward_percentage
        
        logger.info(f"📊 Trade Parameters:")
        logger.info(f"   Risk: {risk_percentage:.1%} = ${risk_amount:.2f}")
        logger.info(f"   Reward: {reward_percentage:.1%} = ${reward_amount:.2f}")
        logger.info(f"   Risk-Reward Ratio: {reward_amount/risk_amount:.1f}:1")
        
        # Get current BTC price
        current_price = binance.get_current_price('BTCUSDT')
        logger.info(f"📈 Current BTC Price: ${current_price:.2f}")
        
        # Calculate position size
        # For a BUY trade: quantity = risk_amount / (entry_price - stop_loss_price)
        # For conservative trading, use 0.1% stop loss distance
        stop_loss_distance_pct = 0.001  # 0.1%
        take_profit_distance_pct = 0.0025  # 0.25% (2.5:1 ratio)
        
        # Determine trade direction (simple momentum-based)
        # For demo purposes, let's use a BUY signal
        trade_action = "BUY"
        entry_price = current_price
        
        if trade_action == "BUY":
            stop_loss_price = entry_price * (1 - stop_loss_distance_pct)
            take_profit_price = entry_price * (1 + take_profit_distance_pct)
        else:  # SELL
            stop_loss_price = entry_price * (1 + stop_loss_distance_pct)
            take_profit_price = entry_price * (1 - take_profit_distance_pct)
        
        # Calculate quantity based on risk amount
        price_difference = abs(entry_price - stop_loss_price)
        quantity = risk_amount / price_difference
        
        logger.info(f"🎯 Trade Setup:")
        logger.info(f"   Action: {trade_action}")
        logger.info(f"   Entry Price: ${entry_price:.2f}")
        logger.info(f"   Stop Loss: ${stop_loss_price:.2f}")
        logger.info(f"   Take Profit: ${take_profit_price:.2f}")
        logger.info(f"   Quantity: {quantity:.6f} BTC")
        logger.info(f"   Position Value: ${quantity * entry_price:.2f}")
        
        # Validate trade parameters
        if quantity * entry_price < 10:  # Minimum order size
            logger.error("❌ Position size too small (minimum $10)")
            return False
        
        if usdt_balance < quantity * entry_price:
            logger.error("❌ Insufficient USDT balance")
            return False
        
        # Execute the trade
        logger.info("🚀 EXECUTING REAL MONEY TRADE")
        
        try:
            # Place market order for entry
            if trade_action == "BUY":
                order_result = binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='BUY',
                    type='MARKET',
                    quantity=f"{quantity:.6f}",
                    isIsolated='TRUE'
                )
            else:
                order_result = binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    type='MARKET',
                    quantity=f"{quantity:.6f}",
                    isIsolated='TRUE'
                )
            
            logger.info("✅ ENTRY ORDER EXECUTED")
            logger.info(f"   Order ID: {order_result['orderId']}")
            logger.info(f"   Status: {order_result['status']}")
            
            # Place OCO order for exit (Stop Loss + Take Profit)
            if trade_action == "BUY":
                oco_result = binance.client.create_margin_oco_order(
                    symbol='BTCUSDT',
                    side='SELL',
                    quantity=f"{quantity:.6f}",
                    price=f"{take_profit_price:.2f}",
                    stopPrice=f"{stop_loss_price:.2f}",
                    stopLimitPrice=f"{stop_loss_price * 0.999:.2f}",
                    isIsolated='TRUE'
                )
            else:
                oco_result = binance.client.create_margin_oco_order(
                    symbol='BTCUSDT',
                    side='BUY',
                    quantity=f"{quantity:.6f}",
                    price=f"{take_profit_price:.2f}",
                    stopPrice=f"{stop_loss_price:.2f}",
                    stopLimitPrice=f"{stop_loss_price * 1.001:.2f}",
                    isIsolated='TRUE'
                )
            
            logger.info("✅ OCO EXIT ORDER PLACED")
            logger.info(f"   OCO Order ID: {oco_result['orderListId']}")
            
            # Monitor the trade
            logger.info("👁️ MONITORING TRADE...")
            
            start_time = time.time()
            max_monitor_time = 3600  # 1 hour
            
            while time.time() - start_time < max_monitor_time:
                try:
                    # Check OCO order status
                    oco_status = binance.client.get_margin_oco_order(
                        orderListId=oco_result['orderListId'],
                        isIsolated='TRUE',
                        symbol='BTCUSDT'
                    )
                    
                    if oco_status['listStatusType'] == 'ALL_DONE':
                        logger.info("🏁 TRADE COMPLETED")
                        
                        # Determine outcome
                        for order in oco_status['orders']:
                            order_detail = binance.client.get_margin_order(
                                symbol='BTCUSDT',
                                orderId=order['orderId'],
                                isIsolated='TRUE'
                            )
                            
                            if order_detail['status'] == 'FILLED':
                                executed_price = float(order_detail['price'])
                                
                                if trade_action == "BUY":
                                    if executed_price >= take_profit_price * 0.99:
                                        outcome = "PROFIT"
                                        pnl = reward_amount
                                    else:
                                        outcome = "LOSS"
                                        pnl = -risk_amount
                                else:
                                    if executed_price <= take_profit_price * 1.01:
                                        outcome = "PROFIT"
                                        pnl = reward_amount
                                    else:
                                        outcome = "LOSS"
                                        pnl = -risk_amount
                                
                                logger.info(f"   Outcome: {outcome}")
                                logger.info(f"   Exit Price: ${executed_price:.2f}")
                                logger.info(f"   P&L: ${pnl:.2f}")
                                
                                return {
                                    'success': True,
                                    'outcome': outcome,
                                    'pnl': pnl,
                                    'entry_price': entry_price,
                                    'exit_price': executed_price
                                }
                    
                    # Log current price
                    current_price = binance.get_current_price('BTCUSDT')
                    logger.info(f"⏳ Trade in progress - Current Price: ${current_price:.2f}")
                    
                    time.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    logger.error(f"❌ Error monitoring trade: {e}")
                    time.sleep(60)
            
            logger.warning("⚠️ Trade monitoring timeout")
            return {'success': False, 'error': 'Monitoring timeout'}
            
        except Exception as e:
            logger.error(f"❌ Trade execution failed: {e}")
            return {'success': False, 'error': str(e)}
    
    except Exception as e:
        logger.error(f"❌ System error: {e}")
        return {'success': False, 'error': str(e)}
    
    finally:
        logger.info("=" * 60)
        logger.info("🏁 DIRECT REAL MONEY TRADE COMPLETE")

if __name__ == "__main__":
    result = main()
    if result and result.get('success'):
        print(f"✅ Trade completed successfully: {result['outcome']} - P&L: ${result['pnl']:.2f}")
    else:
        print(f"❌ Trade failed: {result.get('error', 'Unknown error')}")
