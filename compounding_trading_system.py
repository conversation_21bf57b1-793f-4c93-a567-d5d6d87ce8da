#!/usr/bin/env python3
"""
COMPOUNDING TRADING SYSTEM
Implements tier-based compounding strategy for account size $605.90
Maintains exact $1 SL / $2.5 TP while systematically growing account
"""

import sys
import json
import time
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

class CompoundingTradingSystem:
    """
    Tier-based compounding system optimized for medium-sized accounts
    """
    
    def __init__(self):
        # Account tiers
        self.TIER_1_MAX = 1000.0      # Foundation building
        self.TIER_2_MAX = 2500.0      # Growth acceleration  
        self.TIER_3_MAX = 5000.0      # Aggressive growth
        
        # Current tier parameters (Tier 1)
        self.risk_per_trade = 1.0     # Exact $1
        self.reward_per_trade = 2.5   # Exact $2.5
        self.base_position_percent = 0.8  # 80% of USDT
        self.safety_buffer_percent = 0.2  # 20% buffer
        
        # Compounding parameters
        self.compounding_trigger = 5  # Every 5 wins
        self.compounding_increase = 0.10  # 10% increase
        self.current_compounding_level = 1.0
        
        # State tracking
        self.wins_since_compound = 0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.current_tier = 1
        
        # Components
        self.binance = None
        self.telegram_bot = None
        
        print("🔄 COMPOUNDING TRADING SYSTEM INITIALIZED")
        print(f"Tier 1: Foundation Building Phase")
        print(f"Risk: ${self.risk_per_trade:.2f} | Reward: ${self.reward_per_trade:.2f}")
        print(f"Compounding: Every {self.compounding_trigger} wins (+{self.compounding_increase:.0%})")
    
    def initialize_components(self):
        """Initialize trading components"""
        try:
            # Initialize Binance
            from binance_real_money_connector import BinanceRealMoneyConnector
            self.binance = BinanceRealMoneyConnector()
            
            # Initialize Telegram
            try:
                from telegram_trading_bot import ComprehensiveTelegramTradingBot
                self.telegram_bot = ComprehensiveTelegramTradingBot()
                print("✅ Telegram bot: OPERATIONAL")
            except Exception as e:
                print(f"⚠️ Telegram bot failed: {e}")
                self.telegram_bot = None
            
            return True
            
        except Exception as e:
            print(f"❌ Component initialization failed: {e}")
            return False
    
    def get_current_tier_info(self, balance):
        """Determine current tier and parameters"""
        if balance < self.TIER_1_MAX:
            return {
                'tier': 1,
                'name': 'Foundation Building',
                'risk': 1.0,
                'reward': 2.5,
                'position_percent': 0.8,
                'compounding_trigger': 5,
                'compounding_increase': 0.10,
                'next_tier_target': self.TIER_1_MAX
            }
        elif balance < self.TIER_2_MAX:
            return {
                'tier': 2,
                'name': 'Growth Acceleration',
                'risk': 1.5,
                'reward': 3.75,
                'position_percent': 0.75,
                'compounding_trigger': 3,
                'compounding_increase': 0.15,
                'next_tier_target': self.TIER_2_MAX
            }
        elif balance < self.TIER_3_MAX:
            return {
                'tier': 3,
                'name': 'Aggressive Growth',
                'risk': 2.5,
                'reward': 6.25,
                'position_percent': 0.70,
                'compounding_trigger': 2,
                'compounding_increase': 0.20,
                'next_tier_target': self.TIER_3_MAX
            }
        else:
            return {
                'tier': 4,
                'name': 'Mature Account',
                'risk': balance * 0.002,  # 0.2% of balance
                'reward': balance * 0.005,  # 0.5% of balance
                'position_percent': 0.60,
                'compounding_trigger': 1,
                'compounding_increase': 0.05,
                'next_tier_target': None
            }
    
    def calculate_compounding_position(self):
        """Calculate position size with compounding"""
        try:
            # Get current balance
            balance_info = self.binance.get_isolated_margin_balance()
            current_balance = balance_info['total_usdt_value']
            usdt_balance = balance_info['usdt_balance']
            
            # Get tier information
            tier_info = self.get_current_tier_info(current_balance)
            
            # Update tier if changed
            if tier_info['tier'] != self.current_tier:
                self.upgrade_tier(tier_info)
            
            # Calculate base position size
            base_position = usdt_balance * tier_info['position_percent']
            
            # Apply compounding multiplier
            compounded_position = base_position * self.current_compounding_level
            
            # Ensure we don't exceed available balance
            max_position = usdt_balance * 0.95  # Never use more than 95%
            final_position = min(compounded_position, max_position)
            
            # Get current BTC price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Calculate BTC quantity
            btc_quantity = final_position / current_price
            btc_quantity = max(0.00001, round(btc_quantity, 8))
            
            # Recalculate actual position
            actual_position = btc_quantity * current_price
            
            # Calculate grid levels for exact dollar amounts
            sl_grid_level = self.risk_per_trade / actual_position
            tp_grid_level = self.reward_per_trade / actual_position
            
            # Calculate prices
            entry_price = current_price
            stop_loss_price = round(entry_price * (1 - sl_grid_level), 2)
            take_profit_price = round(entry_price * (1 + tp_grid_level), 2)
            
            position_data = {
                'current_balance': current_balance,
                'usdt_balance': usdt_balance,
                'tier_info': tier_info,
                'base_position': base_position,
                'compounding_level': self.current_compounding_level,
                'final_position': actual_position,
                'btc_quantity': btc_quantity,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'risk_amount': self.risk_per_trade,
                'reward_amount': self.reward_per_trade,
                'sl_grid_level': sl_grid_level,
                'tp_grid_level': tp_grid_level,
                'balance_usage': (actual_position / usdt_balance) * 100,
                'safety_buffer': usdt_balance - actual_position,
                'wins_until_compound': self.compounding_trigger - self.wins_since_compound
            }
            
            print(f"📊 COMPOUNDING POSITION CALCULATED:")
            print(f"  Tier: {tier_info['tier']} ({tier_info['name']})")
            print(f"  Balance: ${current_balance:.2f}")
            print(f"  Base Position: ${base_position:.2f}")
            print(f"  Compounding Level: {self.current_compounding_level:.2f}x")
            print(f"  Final Position: ${actual_position:.2f}")
            print(f"  Risk: ${self.risk_per_trade:.2f} | Reward: ${self.reward_per_trade:.2f}")
            print(f"  Wins Until Compound: {position_data['wins_until_compound']}")
            
            return position_data
            
        except Exception as e:
            print(f"❌ Position calculation failed: {e}")
            return None
    
    def upgrade_tier(self, new_tier_info):
        """Upgrade to new tier"""
        old_tier = self.current_tier
        self.current_tier = new_tier_info['tier']
        
        # Update parameters
        self.risk_per_trade = new_tier_info['risk']
        self.reward_per_trade = new_tier_info['reward']
        self.base_position_percent = new_tier_info['position_percent']
        self.compounding_trigger = new_tier_info['compounding_trigger']
        self.compounding_increase = new_tier_info['compounding_increase']
        
        # Reset compounding level for new tier
        self.current_compounding_level = 1.0
        self.wins_since_compound = 0
        
        print(f"🎉 TIER UPGRADE!")
        print(f"  From Tier {old_tier} → Tier {self.current_tier}")
        print(f"  New Risk: ${self.risk_per_trade:.2f}")
        print(f"  New Reward: ${self.reward_per_trade:.2f}")
        print(f"  New Compounding: Every {self.compounding_trigger} wins")
        
        # Send Telegram notification
        if self.telegram_bot:
            upgrade_message = f"""
🎉 TIER UPGRADE ACHIEVED!

📊 TIER PROGRESSION:
From: Tier {old_tier}
To: Tier {self.current_tier} ({new_tier_info['name']})

💰 NEW PARAMETERS:
- Risk per Trade: ${self.risk_per_trade:.2f}
- Reward per Trade: ${self.reward_per_trade:.2f}
- Compounding: Every {self.compounding_trigger} wins
- Position Usage: {self.base_position_percent:.0%} of USDT

🚀 COMPOUNDING RESET TO 1.0x
Ready for accelerated growth in new tier!
"""
            self.telegram_bot.send_message(upgrade_message)
    
    def check_compounding_trigger(self, trade_result):
        """Check if compounding should be triggered"""
        if trade_result == 'WIN':
            self.wins_since_compound += 1
            
            if self.wins_since_compound >= self.compounding_trigger:
                self.trigger_compounding()
    
    def trigger_compounding(self):
        """Trigger compounding increase"""
        old_level = self.current_compounding_level
        self.current_compounding_level *= (1 + self.compounding_increase)
        self.wins_since_compound = 0
        
        print(f"🔄 COMPOUNDING TRIGGERED!")
        print(f"  Level: {old_level:.2f}x → {self.current_compounding_level:.2f}x")
        print(f"  Increase: +{self.compounding_increase:.0%}")
        print(f"  Next Trigger: {self.compounding_trigger} wins")
        
        # Send Telegram notification
        if self.telegram_bot:
            compound_message = f"""
🔄 COMPOUNDING TRIGGERED!

📈 POSITION SIZE INCREASE:
- Previous Level: {old_level:.2f}x
- New Level: {self.current_compounding_level:.2f}x
- Increase: +{self.compounding_increase:.0%}

💰 MAINTAINED TARGETS:
- Risk: ${self.risk_per_trade:.2f} (exact)
- Reward: ${self.reward_per_trade:.2f} (exact)
- Risk-Reward: {self.reward_per_trade/self.risk_per_trade:.1f}:1

🎯 NEXT COMPOUNDING:
{self.compounding_trigger} more wins needed
"""
            self.telegram_bot.send_message(compound_message)
    
    def execute_compounding_trade(self):
        """Execute a single compounding trade"""
        try:
            print(f"\n🚀 EXECUTING COMPOUNDING TRADE #{self.total_trades + 1}")
            
            # Calculate position with compounding
            position_data = self.calculate_compounding_position()
            if not position_data:
                return False
            
            # Send pre-trade notification
            if self.telegram_bot:
                pre_trade_message = f"""
🚀 COMPOUNDING TRADE #{self.total_trades + 1}

📊 TIER {position_data['tier_info']['tier']} - {position_data['tier_info']['name']}
- Balance: ${position_data['current_balance']:.2f}
- Compounding Level: {position_data['compounding_level']:.2f}x

💰 POSITION DETAILS:
- Position Size: ${position_data['final_position']:.2f}
- BTC Quantity: {position_data['btc_quantity']:.8f}
- Balance Usage: {position_data['balance_usage']:.1f}%
- Safety Buffer: ${position_data['safety_buffer']:.2f}

🎯 EXACT TARGETS:
- Risk: ${position_data['risk_amount']:.2f}
- Reward: ${position_data['reward_amount']:.2f}
- Entry: ${position_data['entry_price']:,.2f}
- SL: ${position_data['stop_loss_price']:,.2f}
- TP: ${position_data['take_profit_price']:,.2f}

🔄 COMPOUNDING STATUS:
Wins until next compound: {position_data['wins_until_compound']}
"""
                self.telegram_bot.send_message(pre_trade_message)
            
            # Execute entry order
            quantity_str = f"{position_data['btc_quantity']:.8f}".rstrip('0').rstrip('.')
            
            buy_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='BUY',
                type='MARKET',
                quantity=quantity_str
            )
            
            entry_order_id = buy_order['orderId']
            entry_time = datetime.now()
            
            print(f"🎉 ENTRY ORDER EXECUTED: {entry_order_id}")
            
            # Wait and get execution details
            time.sleep(3)
            
            order_details = self.binance.client.get_order(symbol='BTCUSDT', orderId=entry_order_id)
            actual_quantity = float(order_details['executedQty'])
            
            # Calculate actual fill price
            if 'fills' in buy_order and buy_order['fills']:
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
                actual_entry_price = total_cost / actual_quantity
            else:
                actual_entry_price = position_data['entry_price']
            
            actual_cost = actual_quantity * actual_entry_price
            
            # Recalculate final prices for exact dollar amounts
            final_sl_grid = position_data['risk_amount'] / actual_cost
            final_tp_grid = position_data['reward_amount'] / actual_cost
            final_stop_loss = round(actual_entry_price * (1 - final_sl_grid), 2)
            final_take_profit = round(actual_entry_price * (1 + final_tp_grid), 2)
            
            # Place exit orders
            quantity_str = f"{actual_quantity:.8f}".rstrip('0').rstrip('.')
            
            # Place Take Profit order
            tp_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='SELL',
                type='LIMIT',
                timeInForce='GTC',
                quantity=quantity_str,
                price=f"{final_take_profit:.2f}"
            )
            
            # Place Stop Loss order
            sl_order = self.binance.client.create_order(
                symbol='BTCUSDT',
                side='SELL',
                type='STOP_LOSS_LIMIT',
                timeInForce='GTC',
                quantity=quantity_str,
                price=f"{final_stop_loss * 0.999:.2f}",
                stopPrice=f"{final_stop_loss:.2f}"
            )
            
            tp_order_id = tp_order['orderId']
            sl_order_id = sl_order['orderId']
            
            print(f"🎯 EXIT ORDERS PLACED!")
            print(f"  Take Profit: {tp_order_id}")
            print(f"  Stop Loss: {sl_order_id}")
            
            # Send execution notification
            if self.telegram_bot:
                execution_message = f"""
🎉 COMPOUNDING TRADE EXECUTED!

📋 ORDER NUMBERS:
Entry: {entry_order_id}
Take Profit: {tp_order_id}
Stop Loss: {sl_order_id}

📊 EXECUTION DETAILS:
- Entry Price: ${actual_entry_price:,.2f}
- Quantity: {actual_quantity:.8f} BTC
- Cost: ${actual_cost:.2f}
- Compounding Level: {position_data['compounding_level']:.2f}x

🎯 EXACT TARGETS:
- SL: ${final_stop_loss:,.2f} (${position_data['risk_amount']:.2f})
- TP: ${final_take_profit:,.2f} (${position_data['reward_amount']:.2f})

🔄 MONITORING UNTIL COMPLETION...
"""
                self.telegram_bot.send_message(execution_message)
            
            # Store trade data for monitoring
            trade_data = {
                'entry_order_id': entry_order_id,
                'tp_order_id': tp_order_id,
                'sl_order_id': sl_order_id,
                'entry_price': actual_entry_price,
                'quantity': actual_quantity,
                'cost': actual_cost,
                'stop_loss_price': final_stop_loss,
                'take_profit_price': final_take_profit,
                'risk_amount': position_data['risk_amount'],
                'reward_amount': position_data['reward_amount'],
                'compounding_level': position_data['compounding_level'],
                'tier': position_data['tier_info']['tier'],
                'entry_time': entry_time
            }
            
            # Monitor until completion
            result = self.monitor_trade_completion(trade_data)
            
            if result:
                # Update statistics
                self.total_trades += 1
                if result['result'] == 'WIN':
                    self.winning_trades += 1
                else:
                    self.losing_trades += 1
                
                # Check compounding trigger
                self.check_compounding_trigger(result['result'])
                
                # Send completion notification
                self.send_completion_notification(result)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Compounding trade execution failed: {e}")
            return False
    
    def monitor_trade_completion(self, trade_data):
        """Monitor trade until completion"""
        try:
            print(f"🔄 MONITORING COMPOUNDING TRADE...")
            
            start_time = datetime.now()
            check_count = 0
            
            while (datetime.now() - start_time).total_seconds() < (24 * 3600):  # 24 hours max
                check_count += 1
                
                # Check orders
                try:
                    tp_order = self.binance.client.get_order(symbol='BTCUSDT', orderId=trade_data['tp_order_id'])
                    sl_order = self.binance.client.get_order(symbol='BTCUSDT', orderId=trade_data['sl_order_id'])
                    
                    if tp_order['status'] == 'FILLED':
                        # Take profit hit
                        try:
                            self.binance.client.cancel_order(symbol='BTCUSDT', orderId=trade_data['sl_order_id'])
                        except:
                            pass
                        
                        return {
                            'result': 'WIN',
                            'result_type': 'TAKE_PROFIT',
                            'exit_price': float(tp_order['price']),
                            'pnl': trade_data['reward_amount'],
                            'exit_order_id': trade_data['tp_order_id'],
                            'exit_time': datetime.now(),
                            'trade_data': trade_data
                        }
                        
                    elif sl_order['status'] == 'FILLED':
                        # Stop loss hit
                        try:
                            self.binance.client.cancel_order(symbol='BTCUSDT', orderId=trade_data['tp_order_id'])
                        except:
                            pass
                        
                        return {
                            'result': 'LOSS',
                            'result_type': 'STOP_LOSS',
                            'exit_price': float(sl_order['price']),
                            'pnl': -trade_data['risk_amount'],
                            'exit_order_id': trade_data['sl_order_id'],
                            'exit_time': datetime.now(),
                            'trade_data': trade_data
                        }
                
                except Exception as e:
                    print(f"Order check failed: {e}")
                
                # Wait before next check
                time.sleep(60)  # Check every minute
                
                # Log progress
                if check_count % 30 == 0:  # Every 30 minutes
                    elapsed_minutes = (datetime.now() - start_time).total_seconds() / 60
                    print(f"⏱️ Monitoring: {elapsed_minutes:.0f} minutes elapsed")
            
            print(f"⏰ Monitoring timeout after 24 hours")
            return None
            
        except Exception as e:
            print(f"❌ Trade monitoring failed: {e}")
            return None
    
    def send_completion_notification(self, result):
        """Send trade completion notification"""
        if not self.telegram_bot:
            return
        
        result_emoji = "🎉" if result['result'] == 'WIN' else "📉"
        duration = (result['exit_time'] - result['trade_data']['entry_time']).total_seconds() / 60
        
        completion_message = f"""
{result_emoji} COMPOUNDING TRADE COMPLETED!

📊 RESULT: {result['result']} ({result['result_type']})
- Entry: ${result['trade_data']['entry_price']:,.2f}
- Exit: ${result['exit_price']:,.2f}
- P&L: ${result['pnl']:.2f}
- Duration: {duration:.1f} minutes

🔄 COMPOUNDING STATUS:
- Level: {result['trade_data']['compounding_level']:.2f}x
- Tier: {result['trade_data']['tier']}
- Wins Since Compound: {self.wins_since_compound}
- Wins Until Next: {self.compounding_trigger - self.wins_since_compound}

📈 PERFORMANCE:
- Total Trades: {self.total_trades}
- Win Rate: {(self.winning_trades/self.total_trades)*100:.1f}%
- Wins: {self.winning_trades} | Losses: {self.losing_trades}

📋 ORDER NUMBERS:
Entry: {result['trade_data']['entry_order_id']}
Exit: {result['exit_order_id']}
"""
        
        self.telegram_bot.send_message(completion_message)
    
    def run_compounding_system(self, num_trades=None):
        """Run the compounding trading system"""
        try:
            print(f"🚀 STARTING COMPOUNDING TRADING SYSTEM")
            print(f"="*60)
            
            if not self.initialize_components():
                return False
            
            # Send system startup notification
            if self.telegram_bot:
                startup_message = f"""
🚀 COMPOUNDING TRADING SYSTEM STARTED!

📊 TIER 1 - FOUNDATION BUILDING
- Risk per Trade: ${self.risk_per_trade:.2f}
- Reward per Trade: ${self.reward_per_trade:.2f}
- Compounding: Every {self.compounding_trigger} wins (+{self.compounding_increase:.0%})
- Position Usage: {self.base_position_percent:.0%} of USDT

🎯 SYSTEM FEATURES:
✅ Exact dollar risk/reward targets
✅ Automatic compounding after wins
✅ Tier-based progression system
✅ Safety buffers maintained
✅ Detailed monitoring and alerts

🔄 COMPOUNDING SYSTEM: ACTIVE
Ready for systematic account growth!
"""
                self.telegram_bot.send_message(startup_message)
            
            # Execute trades
            if num_trades:
                # Execute specific number of trades
                for i in range(num_trades):
                    print(f"\n{'='*60}")
                    print(f"COMPOUNDING TRADE {i+1}/{num_trades}")
                    print(f"{'='*60}")
                    
                    if not self.execute_compounding_trade():
                        print(f"❌ Trade {i+1} failed")
                        break
                    
                    print(f"✅ Trade {i+1} completed")
                    
                    # Brief pause between trades
                    if i < num_trades - 1:
                        time.sleep(5)
            else:
                # Continuous trading
                trade_count = 0
                while True:
                    trade_count += 1
                    print(f"\n{'='*60}")
                    print(f"COMPOUNDING TRADE {trade_count}")
                    print(f"{'='*60}")
                    
                    if not self.execute_compounding_trade():
                        print(f"❌ Trade {trade_count} failed")
                        break
                    
                    print(f"✅ Trade {trade_count} completed")
                    
                    # Brief pause between trades
                    time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ Compounding system failed: {e}")
            return False

def main():
    """Main execution"""
    print("🔄 COMPOUNDING TRADING SYSTEM")
    print("Tier-based compounding for systematic account growth")
    print("="*60)
    
    try:
        system = CompoundingTradingSystem()
        
        print(f"\nOptions:")
        print(f"1. Execute 1 test trade")
        print(f"2. Execute 5 trades")
        print(f"3. Execute 10 trades")
        print(f"4. Run continuous compounding")
        
        choice = input("\nEnter choice (1-4): ")
        
        if choice == '1':
            system.run_compounding_system(1)
        elif choice == '2':
            system.run_compounding_system(5)
        elif choice == '3':
            system.run_compounding_system(10)
        elif choice == '4':
            system.run_compounding_system()
        else:
            print("Invalid choice")
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
