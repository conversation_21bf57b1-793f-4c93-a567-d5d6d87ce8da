#!/usr/bin/env python3
"""
Start Trading with 0.2% Risk
Direct deployment with test trade first
"""

import sys
import os
import json
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials, get_binance_credentials, get_telegram_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleTrading:
    """Simple trading system with 0.2% risk"""
    
    def __init__(self):
        self.binance = None
        self.telegram = None
        self.risk_percentage = 0.002  # 0.2%
        self.reward_ratio = 2.5
        
    def initialize(self):
        """Initialize components"""
        try:
            logger.info("🚀 Initializing trading system...")
            
            # Validate credentials
            if not validate_all_credentials():
                return False
            
            # Initialize Binance
            self.binance = BinanceRealMoneyConnector()
            
            # Initialize Telegram
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    def get_account_status(self):
        """Get current account status"""
        try:
            balance = self.binance.get_account_balance()
            if balance:
                return {
                    'total_balance': balance['total_usdt_value'],
                    'btc_balance': balance['btc']['netAsset'],
                    'usdt_balance': balance['usdt']['netAsset'],
                    'current_price': balance['current_btc_price'],
                    'margin_level': balance['margin_level']
                }
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get account status: {e}")
            return None
    
    def calculate_trade_params(self, account_balance, current_price):
        """Calculate trade parameters with 0.2% risk"""
        try:
            # Risk amount (0.2% of account)
            risk_amount = account_balance * self.risk_percentage
            reward_amount = risk_amount * self.reward_ratio
            
            # Price distances (simplified)
            risk_percentage = self.risk_percentage  # 0.2%
            reward_percentage = risk_percentage * self.reward_ratio  # 0.5%
            
            return {
                'risk_amount': risk_amount,
                'reward_amount': reward_amount,
                'risk_percentage': risk_percentage,
                'reward_percentage': reward_percentage,
                'stop_loss_distance': current_price * risk_percentage,
                'take_profit_distance': current_price * reward_percentage
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate trade params: {e}")
            return None
    
    def send_startup_message(self, account_status, trade_params):
        """Send startup notification"""
        try:
            if self.telegram:
                message = f"""
🚀 **TRADING SYSTEM STARTED - 0.2% RISK**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Account Balance:** ${account_status['total_balance']:.2f}
📊 **BTC Balance:** {account_status['btc_balance']:.6f} BTC
💵 **USDT Balance:** ${account_status['usdt_balance']:.2f} USDT
📈 **BTC Price:** ${account_status['current_price']:.2f}
⚖️ **Margin Level:** {account_status['margin_level']:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔴 **Stop Loss:** ${trade_params['risk_amount']:.2f} (0.2%)
🟢 **Take Profit:** ${trade_params['reward_amount']:.2f} (0.5%)
⚖️ **Risk-Reward:** 2.5:1
🔄 **Compounding:** Enabled
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧪 **PHASE 1: TEST TRADE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: MONITORING FOR SIGNALS**
"""
                self.telegram.send_message(message)
                
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")
    
    def execute_test_trade(self):
        """Execute a simple test trade"""
        try:
            logger.info("🧪 Executing test trade...")
            
            # Get account status
            account_status = self.get_account_status()
            if not account_status:
                return False
            
            # Calculate trade parameters
            trade_params = self.calculate_trade_params(
                account_status['total_balance'],
                account_status['current_price']
            )
            
            if not trade_params:
                return False
            
            # Get market data for signal
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False
            
            current_price = market_data['close'].iloc[-1]
            current_rsi = market_data['rsi'].iloc[-1]
            
            # Simple signal logic (for test)
            if current_rsi < 30:
                signal = 'BUY'
            elif current_rsi > 70:
                signal = 'SELL'
            else:
                signal = 'HOLD'
            
            if signal == 'HOLD':
                logger.info("📊 No trading signal - waiting...")
                if self.telegram:
                    self.telegram.send_message(f"📊 **MONITORING:** RSI: {current_rsi:.1f} - No signal yet")
                return False
            
            # Send trade notification
            if self.telegram:
                trade_message = f"""
📡 **TEST TRADE SIGNAL**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **Signal:** {signal}
💰 **Entry Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
🔴 **Risk:** ${trade_params['risk_amount']:.2f}
🟢 **Reward:** ${trade_params['reward_amount']:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing test trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute trade (simplified for test)
            trade_result = self.binance.execute_full_trade(
                signal,
                current_price,
                0.8  # confidence
            )
            
            if trade_result:
                logger.info("✅ Test trade executed successfully")
                
                # Send success notification
                if self.telegram:
                    success_message = f"""
✅ **TEST TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Order ID:** {trade_result.get('orderId', 'N/A')}
⏳ **Status:** Monitoring for completion
🎯 **Next:** Full system activation after TP/SL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                    self.telegram.send_message(success_message)
                
                return True
            else:
                logger.error("❌ Test trade execution failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test trade error: {e}")
            return False
    
    def start_monitoring(self):
        """Start monitoring for trading signals"""
        try:
            logger.info("👁️ Starting signal monitoring...")
            
            # Get initial status
            account_status = self.get_account_status()
            if not account_status:
                logger.error("❌ Failed to get account status")
                return
            
            trade_params = self.calculate_trade_params(
                account_status['total_balance'],
                account_status['current_price']
            )
            
            # Send startup message
            self.send_startup_message(account_status, trade_params)
            
            # Monitor for signals
            check_count = 0
            while True:
                try:
                    check_count += 1
                    logger.info(f"🔍 Signal check #{check_count}")
                    
                    # Try to execute test trade
                    if self.execute_test_trade():
                        logger.info("✅ Test trade completed - monitoring for result")
                        break
                    
                    # Wait before next check
                    time.sleep(60)  # Check every minute
                    
                    # Send periodic status (every 10 checks)
                    if check_count % 10 == 0 and self.telegram:
                        account_status = self.get_account_status()
                        if account_status:
                            status_message = f"""
📊 **SYSTEM STATUS UPDATE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Balance:** ${account_status['total_balance']:.2f}
📈 **BTC Price:** ${account_status['current_price']:.2f}
🔍 **Checks:** {check_count}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: MONITORING ACTIVE**
"""
                            self.telegram.send_message(status_message)
                    
                except KeyboardInterrupt:
                    logger.info("🛑 Monitoring stopped by user")
                    break
                except Exception as e:
                    logger.error(f"❌ Monitoring error: {e}")
                    time.sleep(30)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")

def main():
    """Main function"""
    print("🚀 STARTING TRADING SYSTEM - 0.2% RISK")
    print("=" * 50)
    print("📋 Account Balance: $600.46")
    print("📋 Risk per Trade: 0.2% = $1.21")
    print("📋 Take Profit: 0.5% = $3.03")
    print("📋 Risk-Reward Ratio: 2.5:1")
    print("=" * 50)
    
    # Initialize trading system
    trading = SimpleTrading()
    
    if not trading.initialize():
        print("❌ System initialization failed")
        return
    
    print("✅ System initialized - starting monitoring...")
    
    # Start monitoring
    trading.start_monitoring()

if __name__ == "__main__":
    main()
