#!/usr/bin/env python3
"""
Fixed Master Training System
Fixes confidence threshold and trading simulation issues
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
import json
from datetime import datetime, timedelta

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedMasterTrainingSystem:
    """Fixed master training system with proper confidence and trading simulation"""
    
    def __init__(self):
        self.binance = None
        self.telegram = None
        self.model = None
        
    def initialize_system(self):
        """Initialize fixed master training system"""
        try:
            logger.info("🚀 Initializing FIXED MASTER TRAINING SYSTEM...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ Fixed master training system initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Fixed master training initialization failed: {e}")
            return False
    
    def load_existing_model(self):
        """Load the existing trained model"""
        try:
            logger.info("🧠 Loading existing trained model...")
            
            # Load the model that was just trained
            from master_compliant_4year_training import MasterCompliant4YearTrainer
            trainer = MasterCompliant4YearTrainer()
            trainer.create_master_tcn_model()
            
            # Load saved weights
            model_path = os.path.join('02_signal_generator', 'models', 'best_real_3year_trained_model.pth')
            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location='cpu')
                if 'model_state_dict' in checkpoint:
                    trainer.model.load_state_dict(checkpoint['model_state_dict'])
                    logger.info("✅ Trained model loaded successfully")
                else:
                    logger.error("❌ No model state dict in checkpoint")
                    return None
            else:
                logger.error("❌ No trained model found")
                return None
            
            trainer.model.eval()
            return trainer.model
            
        except Exception as e:
            logger.error(f"❌ Failed to load existing model: {e}")
            return None
    
    def run_fixed_trading_simulation(self, data, model, phase_name):
        """Run fixed trading simulation with proper confidence handling"""
        try:
            logger.info(f"🧪 Running FIXED trading simulation for {phase_name}...")
            
            # Prepare features
            market_features = []
            grid_features = []
            
            sequence_length = 4
            for i in range(len(data) - sequence_length):
                # Market data
                price_seq = data['close'].iloc[i:i+sequence_length].values
                rsi_seq = data['rsi'].iloc[i:i+sequence_length].values
                vwap_seq = data['vwap'].iloc[i:i+sequence_length].values
                volume_seq = data['volume'].iloc[i:i+sequence_length].values
                
                # Normalize
                price_seq = price_seq / np.max(price_seq)
                rsi_seq = rsi_seq / 100.0
                vwap_seq = vwap_seq / np.max(vwap_seq)
                volume_seq = volume_seq / np.max(volume_seq) if np.max(volume_seq) > 0 else volume_seq
                
                market_tensor = np.array([price_seq, rsi_seq, vwap_seq, volume_seq])
                market_features.append(market_tensor)
                
                # Grid features
                current_idx = i + sequence_length - 1
                current_price = data['close'].iloc[current_idx]
                
                grid_vector = np.array([
                    data['grid_level'].iloc[current_idx] / 100.0,
                    data['at_grid_level'].iloc[current_idx],
                    data['grid_distance'].iloc[current_idx],
                    1.0025,  # Normalized
                    0.9975,  # Normalized
                    0.0025,
                    data['grid_compliance_score'].iloc[current_idx]
                ])
                
                grid_features.append(grid_vector)
            
            # Convert to tensors
            X_market = torch.FloatTensor(market_features)
            X_grid = torch.FloatTensor(grid_features)
            
            # Get model predictions
            model.eval()
            with torch.no_grad():
                policy_logits, value = model(X_market, X_grid)
                probabilities = torch.softmax(policy_logits, dim=1)
                confidences = torch.max(probabilities, dim=1)[0]
                predictions = torch.argmax(probabilities, dim=1)
            
            # FIXED: Use lower confidence threshold
            confidence_threshold = 0.4  # Lowered from 0.75
            
            # Trading simulation
            initial_balance = 1000.0
            current_balance = initial_balance
            trades = []
            position = None
            
            high_confidence_count = 0
            
            for i, (pred, conf) in enumerate(zip(predictions, confidences)):
                if i >= len(data) - sequence_length - 1:
                    continue
                
                current_price = data['close'].iloc[i + sequence_length]
                
                # Count high confidence signals
                if conf.item() > confidence_threshold:
                    high_confidence_count += 1
                    
                    signal = ['BUY', 'SELL', 'HOLD'][pred.item()]
                    
                    # Execute trade if no position and actionable signal
                    if position is None and signal in ['BUY', 'SELL']:
                        # Calculate position size
                        risk_amount = current_balance * 0.01  # 1% risk
                        stop_loss_distance = current_price * 0.01  # 1% stop loss
                        position_size = risk_amount / stop_loss_distance
                        
                        position = {
                            'type': signal,
                            'entry_price': current_price,
                            'entry_index': i,
                            'position_size': position_size,
                            'stop_loss': current_price * (0.99 if signal == 'BUY' else 1.01),
                            'take_profit': current_price * (1.025 if signal == 'BUY' else 0.975),
                            'confidence': conf.item()
                        }
                
                # Check position exit
                if position is not None:
                    exit_triggered = False
                    exit_reason = ""
                    
                    if position['type'] == 'BUY':
                        if current_price <= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "STOP_LOSS"
                        elif current_price >= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "TAKE_PROFIT"
                    else:  # SELL
                        if current_price >= position['stop_loss']:
                            exit_triggered = True
                            exit_reason = "STOP_LOSS"
                        elif current_price <= position['take_profit']:
                            exit_triggered = True
                            exit_reason = "TAKE_PROFIT"
                    
                    # Exit position
                    if exit_triggered:
                        if position['type'] == 'BUY':
                            pnl = position['position_size'] * (current_price - position['entry_price'])
                        else:
                            pnl = position['position_size'] * (position['entry_price'] - current_price)
                        
                        current_balance += pnl
                        
                        trade = {
                            'type': position['type'],
                            'entry_price': position['entry_price'],
                            'exit_price': current_price,
                            'pnl': pnl,
                            'exit_reason': exit_reason,
                            'confidence': position['confidence']
                        }
                        
                        trades.append(trade)
                        position = None
            
            # Calculate results
            total_trades = len(trades)
            winning_trades = [t for t in trades if t['pnl'] > 0]
            win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
            
            # Calculate trades per day
            days_in_period = len(data) / 24  # Hourly data
            trades_per_day = total_trades / days_in_period if days_in_period > 0 else 0
            
            # Calculate metrics
            total_pnl = current_balance - initial_balance
            avg_confidence = confidences.mean().item()
            
            # Calculate composite score
            if total_trades > 0 and win_rate > 0:
                avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
                avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] <= 0]) if any(t['pnl'] <= 0 for t in trades) else -1
                profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 1.0
                
                # Composite score calculation
                win_rate_component = min(1.0, win_rate / 60.0)  # Normalize to 60% target
                profit_component = min(1.0, profit_factor / 2.5)  # Normalize to 2.5 target
                composite_score = (win_rate_component * 0.7 + profit_component * 0.3) * 0.8
            else:
                composite_score = 0.0
                profit_factor = 0.0
            
            # New reward calculation
            new_reward = composite_score * trades_per_day
            
            logger.info(f"📊 {phase_name} Results:")
            logger.info(f"   Total Trades: {total_trades}")
            logger.info(f"   Win Rate: {win_rate:.1f}%")
            logger.info(f"   Trades per Day: {trades_per_day:.1f}")
            logger.info(f"   Composite Score: {composite_score:.3f}")
            logger.info(f"   New Reward: {new_reward:.2f}")
            logger.info(f"   High Confidence Signals: {high_confidence_count}")
            logger.info(f"   Average Confidence: {avg_confidence:.3f}")
            
            return {
                'total_trades': total_trades,
                'winning_trades': len(winning_trades),
                'win_rate': win_rate,
                'trades_per_day': trades_per_day,
                'total_pnl': total_pnl,
                'composite_score': composite_score,
                'new_reward': new_reward,
                'avg_confidence': avg_confidence,
                'high_confidence_signals': high_confidence_count,
                'confidence_threshold': confidence_threshold
            }
            
        except Exception as e:
            logger.error(f"❌ Fixed trading simulation failed: {e}")
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'win_rate': 0.0,
                'trades_per_day': 0.0,
                'total_pnl': 0.0,
                'composite_score': 0.0,
                'new_reward': 0.0,
                'avg_confidence': 0.0,
                'high_confidence_signals': 0,
                'confidence_threshold': confidence_threshold
            }
    
    def load_real_data(self):
        """Load the real 4-year data that was saved"""
        try:
            logger.info("📊 Loading real 4-year Bitcoin data...")
            
            data_path = 'real_bitcoin_4year_data.json'
            if not os.path.exists(data_path):
                logger.error("❌ Real data file not found")
                return None
            
            df = pd.read_json(data_path, orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['year'] = df['datetime'].dt.year
            
            logger.info(f"✅ Loaded {len(df)} real data points")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to load real data: {e}")
            return None
    
    def run_fixed_validation_pipeline(self):
        """Run fixed validation pipeline with proper results"""
        try:
            logger.info("🚀 Starting FIXED VALIDATION PIPELINE...")
            
            # Load model
            model = self.load_existing_model()
            if model is None:
                return False
            
            # Load data
            df = self.load_real_data()
            if df is None:
                return False
            
            # Split data
            train_data = df[df['year'].isin([2022, 2023])].copy()
            out_of_sample_data = df[df['year'] == 2024].copy()
            backtest_data = df[df['year'] == 2021].copy()
            latest_data = df[df['datetime'] >= df['datetime'].max() - timedelta(days=3)].copy()
            
            # Run simulations
            results = {}
            
            logger.info("🧪 Running training data simulation...")
            results['training'] = self.run_fixed_trading_simulation(train_data, model, "Training")
            
            logger.info("🧪 Running out-of-sample simulation...")
            results['out_of_sample'] = self.run_fixed_trading_simulation(out_of_sample_data, model, "Out-of-Sample")
            
            logger.info("🧪 Running backtest simulation...")
            results['backtest'] = self.run_fixed_trading_simulation(backtest_data, model, "Backtest")
            
            logger.info("🧪 Running final 3-day simulation...")
            results['final_3day'] = self.run_fixed_trading_simulation(latest_data, model, "Final 3-Day")
            
            # Check hierarchy
            training_reward = results['training']['new_reward']
            out_of_sample_reward = results['out_of_sample']['new_reward']
            backtest_reward = results['backtest']['new_reward']
            final_reward = results['final_3day']['new_reward']
            
            hierarchy_correct = training_reward < out_of_sample_reward < backtest_reward
            final_best = final_reward >= max(training_reward, out_of_sample_reward, backtest_reward)
            
            # Generate simple HTML report
            self.generate_simple_html_report(results, hierarchy_correct, final_best)
            
            # Send results
            if self.telegram:
                results_message = f"""
FIXED MASTER TRAINING RESULTS
Training: {training_reward:.2f} new reward
Out-of-Sample: {out_of_sample_reward:.2f} new reward  
Backtest: {backtest_reward:.2f} new reward
Final 3-Day: {final_reward:.2f} new reward
Hierarchy: {'CORRECT' if hierarchy_correct else 'INCORRECT'}
Final Best: {'YES' if final_best else 'NO'}
"""
                self.telegram.send_message(results_message)
            
            logger.info("✅ Fixed validation pipeline completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Fixed validation pipeline failed: {e}")
            return False
    
    def generate_simple_html_report(self, results, hierarchy_correct, final_best):
        """Generate simple HTML report without unicode issues"""
        try:
            logger.info("📄 Generating simple HTML report...")
            
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Master Training Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 1000px; margin: 0 auto; }}
        .phase {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
        .success {{ background-color: #d4edda; }}
        .warning {{ background-color: #fff3cd; }}
        .error {{ background-color: #f8d7da; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ border: 1px solid #ccc; padding: 8px; text-align: center; }}
        th {{ background-color: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Master Compliant Training Results</h1>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <table>
            <tr>
                <th>Phase</th>
                <th>Total Trades</th>
                <th>Win Rate (%)</th>
                <th>Trades/Day</th>
                <th>Composite Score</th>
                <th>New Reward</th>
                <th>Target Met</th>
            </tr>
"""
            
            for phase_name, phase_data in results.items():
                win_rate = phase_data['win_rate']
                trades_per_day = phase_data['trades_per_day']
                new_reward = phase_data['new_reward']
                
                target_met = win_rate >= 60.0 and trades_per_day >= 8.0
                row_class = 'success' if target_met else 'error'
                
                html_content += f"""
            <tr class="{row_class}">
                <td>{phase_name.title()}</td>
                <td>{phase_data['total_trades']}</td>
                <td>{win_rate:.1f}</td>
                <td>{trades_per_day:.1f}</td>
                <td>{phase_data['composite_score']:.3f}</td>
                <td>{new_reward:.2f}</td>
                <td>{'YES' if target_met else 'NO'}</td>
            </tr>
"""
            
            html_content += f"""
        </table>
        
        <div class="phase {'success' if hierarchy_correct else 'error'}">
            <h2>Performance Hierarchy</h2>
            <p>Requirement: Training < Out-of-Sample < Backtest</p>
            <p>Status: {'PASSED' if hierarchy_correct else 'FAILED'}</p>
        </div>
        
        <div class="phase {'success' if final_best else 'error'}">
            <h2>Final 3-Day Performance</h2>
            <p>Requirement: Best performance of all phases</p>
            <p>Status: {'PASSED' if final_best else 'FAILED'}</p>
        </div>
    </div>
</body>
</html>
"""
            
            # Save with proper encoding
            with open('master_training_report.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info("✅ Simple HTML report generated successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to generate simple HTML report: {e}")
            return False

def main():
    """Main function"""
    print("🔧 FIXED MASTER TRAINING VALIDATION")
    print("=" * 60)
    print("📋 Fix confidence threshold (0.4 vs 0.75)")
    print("📋 Fix trading simulation")
    print("📋 Generate proper results")
    print("📋 Create HTML report")
    print("=" * 60)
    
    system = FixedMasterTrainingSystem()
    
    if not system.initialize_system():
        print("❌ Fixed system initialization failed")
        return False
    
    print("🧪 Running fixed validation pipeline...")
    if system.run_fixed_validation_pipeline():
        print("✅ FIXED VALIDATION COMPLETED!")
        print("📄 HTML report: master_training_report.html")
        return True
    else:
        print("❌ Fixed validation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
