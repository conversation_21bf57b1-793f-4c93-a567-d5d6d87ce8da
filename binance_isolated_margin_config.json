{"binance_config": {"api_key": "LOADED_FROM_ENV", "api_secret": "LOADED_FROM_ENV", "note": "Actual credentials are loaded from environment variables for security"}, "trading_config": {"symbol": "BTCUSDT", "margin_type": "ISOLATED", "leverage": 10, "max_leverage": 10, "risk_per_trade": 0.01, "reward_per_trade": 0.025, "grid_spacing": 0.0025, "confidence_threshold": 0.75, "execution_method": "LIMIT_ORDERS_EXACT_GRID", "min_trade_interval": 300}, "master_document_compliance": {"starting_balance": 100.0, "risk_reward_ratio": 2.5, "grid_compliance": true, "real_indicators": true, "tcn_cnn_ppo_model": true, "trades_per_day_target": 8.0, "win_rate_target": 0.6, "composite_score": 0.8, "signal_persistence": 300, "grid_only_trading": true, "pre_execution_scanning": true, "telegram_integration": true, "model_purpose": "GRID_TO_GRID_PROBABILITY_PREDICTION", "execution_method": "LIMIT_ORDERS_AT_EXACT_GRID_LEVELS"}}