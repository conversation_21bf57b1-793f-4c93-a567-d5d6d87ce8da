#!/usr/bin/env python3
"""
Diagnostic Backtest - Identify Core Issues
- Check data quality
- Validate model loading
- Test basic functionality
- No simulation code
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import logging
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class DiagnosticBacktest:
    """Diagnostic backtest to identify issues"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"🔍 Diagnostic Backtest - Device: {self.device}")
    
    def check_data_quality(self):
        """Check data quality and structure"""
        try:
            logger.info("📊 Checking data quality...")
            
            # Load data
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').reset_index(drop=True)
            
            logger.info(f"✅ Data loaded: {len(df):,} samples")
            logger.info(f"📅 Date range: {df['datetime'].min()} to {df['datetime'].max()}")
            logger.info(f"📊 Columns: {list(df.columns)}")
            
            # Check for required columns
            required_cols = ['open', 'high', 'low', 'close', 'rsi', 'vwap']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                logger.error(f"❌ Missing columns: {missing_cols}")
                return False
            
            # Check data quality
            null_counts = df[required_cols].isnull().sum()
            logger.info(f"📊 Null counts: {null_counts.to_dict()}")
            
            # Check for infinite values
            inf_counts = {}
            for col in required_cols:
                inf_counts[col] = np.isinf(df[col]).sum()
            logger.info(f"📊 Infinite values: {inf_counts}")
            
            # Sample data
            logger.info(f"📊 Sample data:")
            logger.info(df[required_cols].head().to_string())
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Data quality check failed: {e}")
            return False
    
    def check_model_loading(self):
        """Check model loading capabilities"""
        try:
            logger.info("🔍 Checking model loading...")
            
            # Check available models
            import os
            model_files = [f for f in os.listdir('.') if f.endswith('.pth')]
            logger.info(f"📊 Available model files: {model_files}")
            
            # Try to load quick_best_model.pth
            if 'quick_best_model.pth' in model_files:
                checkpoint = torch.load('quick_best_model.pth', map_location='cpu', weights_only=False)
                logger.info(f"✅ quick_best_model.pth loaded")
                logger.info(f"📊 Checkpoint keys: {list(checkpoint.keys())}")
                
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                    logger.info(f"📊 Model layers: {len(state_dict)} parameters")
                    
                    # Show some layer names
                    layer_names = list(state_dict.keys())[:10]
                    logger.info(f"📊 Sample layers: {layer_names}")
                    
                    return True
                else:
                    logger.error("❌ No model_state_dict in checkpoint")
                    return False
            else:
                logger.error("❌ quick_best_model.pth not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Model loading check failed: {e}")
            return False
    
    def create_simple_model(self):
        """Create simple model for testing"""
        class SimpleTestModel(nn.Module):
            def __init__(self):
                super(SimpleTestModel, self).__init__()
                
                # Simple architecture
                self.tcn = nn.Sequential(
                    nn.Conv1d(7, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(64, 32)
                )
                
                self.cnn = nn.Sequential(
                    nn.Conv1d(7, 64, 5, padding=2),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(64, 32)
                )
                
                self.ppo = nn.Sequential(
                    nn.Linear(71, 64),  # 32+32+7=71
                    nn.ReLU(),
                    nn.Linear(64, 3)
                )
                
                self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
                self.tcn_classifier = nn.Linear(32, 3)
                self.cnn_classifier = nn.Linear(32, 3)
            
            def forward(self, x, grid_features):
                x_transposed = x.transpose(1, 2)
                
                tcn_features = self.tcn(x_transposed)
                cnn_features = self.cnn(x_transposed)
                ppo_state = torch.cat([tcn_features, cnn_features, grid_features], dim=1)
                
                tcn_pred = torch.softmax(self.tcn_classifier(tcn_features), dim=1)
                cnn_pred = torch.softmax(self.cnn_classifier(cnn_features), dim=1)
                ppo_pred = torch.softmax(self.ppo(ppo_state), dim=1)
                
                weights = torch.softmax(self.ensemble_weights, dim=0)
                ensemble_pred = weights[0] * tcn_pred + weights[1] * cnn_pred + weights[2] * ppo_pred
                
                return ensemble_pred
        
        return SimpleTestModel()
    
    def test_model_inference(self):
        """Test model inference with sample data"""
        try:
            logger.info("🔍 Testing model inference...")
            
            # Create model
            model = self.create_simple_model()
            model.eval()
            
            # Create sample input
            batch_size = 1
            sequence_length = 60
            num_features = 7
            
            # Sample market data
            X = torch.randn(batch_size, sequence_length, num_features)
            grid_features = torch.randn(batch_size, 7)
            
            logger.info(f"📊 Input shapes: X={X.shape}, grid={grid_features.shape}")
            
            # Test inference
            with torch.no_grad():
                output = model(X, grid_features)
                prediction = torch.argmax(output, dim=1).item()
                confidence = torch.max(torch.softmax(output, dim=1)).item()
            
            logger.info(f"✅ Model inference successful")
            logger.info(f"📊 Output shape: {output.shape}")
            logger.info(f"📊 Prediction: {prediction} (0=BUY, 1=SELL, 2=HOLD)")
            logger.info(f"📊 Confidence: {confidence:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Model inference test failed: {e}")
            return False
    
    def test_data_processing(self):
        """Test data processing pipeline"""
        try:
            logger.info("🔍 Testing data processing...")
            
            # Load small sample
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df = df.tail(100).copy()  # Small sample
            
            # Add ATR
            tr_list = []
            for i in range(len(df)):
                if i == 0:
                    tr = df.iloc[i]['high'] - df.iloc[i]['low']
                else:
                    tr1 = df.iloc[i]['high'] - df.iloc[i]['low']
                    tr2 = abs(df.iloc[i]['high'] - df.iloc[i-1]['close'])
                    tr3 = abs(df.iloc[i]['low'] - df.iloc[i-1]['close'])
                    tr = max(tr1, tr2, tr3)
                tr_list.append(tr)
            
            df['atr'] = pd.Series(tr_list).rolling(14, min_periods=1).mean().fillna(0)
            
            logger.info("✅ ATR calculation successful")
            
            # Test sequence preparation
            sequence_length = 60
            if len(df) > sequence_length:
                index = sequence_length
                sequence = df.iloc[index-sequence_length:index][
                    ['open', 'high', 'low', 'close', 'rsi', 'vwap', 'atr']
                ].values
                
                logger.info(f"✅ Sequence preparation successful")
                logger.info(f"📊 Sequence shape: {sequence.shape}")
                logger.info(f"📊 Has NaN: {np.any(np.isnan(sequence))}")
                logger.info(f"📊 Has Inf: {np.any(np.isinf(sequence))}")
                
                return True
            else:
                logger.error("❌ Insufficient data for sequence")
                return False
                
        except Exception as e:
            logger.error(f"❌ Data processing test failed: {e}")
            return False
    
    def run_diagnostic(self):
        """Run complete diagnostic"""
        logger.info("🚀 Starting Diagnostic Backtest")
        logger.info("="*60)
        
        results = {
            'data_quality': False,
            'model_loading': False,
            'model_inference': False,
            'data_processing': False
        }
        
        # Run tests
        results['data_quality'] = self.check_data_quality()
        results['model_loading'] = self.check_model_loading()
        results['model_inference'] = self.test_model_inference()
        results['data_processing'] = self.test_data_processing()
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("📊 DIAGNOSTIC RESULTS")
        logger.info("="*60)
        
        for test, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"{test.replace('_', ' ').title()}: {status}")
        
        all_passed = all(results.values())
        
        if all_passed:
            logger.info("\n🎉 ALL TESTS PASSED - Ready for clean backtest")
        else:
            logger.info("\n⚠️  SOME TESTS FAILED - Issues need to be resolved")
        
        # Save diagnostic results
        diagnostic_report = {
            'diagnostic_completed': True,
            'all_tests_passed': all_passed,
            'individual_results': results,
            'timestamp': datetime.now().isoformat(),
            'recommendations': self.get_recommendations(results)
        }
        
        with open('diagnostic_results.json', 'w') as f:
            json.dump(diagnostic_report, f, indent=2)
        
        logger.info("💾 Diagnostic results saved to diagnostic_results.json")
        logger.info("="*60)
        
        return results
    
    def get_recommendations(self, results):
        """Get recommendations based on test results"""
        recommendations = []
        
        if not results['data_quality']:
            recommendations.append("Fix data quality issues - check for missing/invalid values")
        
        if not results['model_loading']:
            recommendations.append("Fix model loading - check model file integrity and architecture")
        
        if not results['model_inference']:
            recommendations.append("Fix model inference - check input/output dimensions")
        
        if not results['data_processing']:
            recommendations.append("Fix data processing - check sequence preparation and indicators")
        
        if all(results.values()):
            recommendations.append("All tests passed - proceed with clean backtest")
        
        return recommendations

def main():
    print("🔍 DIAGNOSTIC BACKTEST")
    print("📊 Identifying Core Issues Before Clean Backtest")
    print("🚫 No Simulation - Pure Diagnostic Testing")
    print("="*60)
    
    diagnostic = DiagnosticBacktest()
    results = diagnostic.run_diagnostic()
    
    if all(results.values()):
        print("\n🎉 Diagnostic completed - All systems ready!")
        print("✅ Proceed with clean secure backtest")
    else:
        print("\n⚠️  Diagnostic found issues")
        print("📊 Check diagnostic_results.json for details")

if __name__ == "__main__":
    main()
