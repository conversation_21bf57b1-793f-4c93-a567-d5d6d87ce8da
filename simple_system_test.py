#!/usr/bin/env python3
"""
SIMPLE SYSTEM TEST
Test all components without Unicode issues
"""

import os
import sys
import time
import json
import logging
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

# Setup logging without Unicode
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_system_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleSystemTest:
    """Simple system test without Unicode issues"""
    
    def __init__(self):
        self.test_results = {}
        logger.info("SIMPLE SYSTEM TEST INITIALIZED")
    
    def test_binance_connection(self):
        """Test Binance connection"""
        try:
            logger.info("Testing Binance connection...")
            
            from binance_real_money_connector import BinanceRealMoneyConnector
            connector = BinanceRealMoneyConnector()
            
            # Test account access
            account_info = connector.get_account_info()
            if not account_info:
                logger.error("Binance account connection failed")
                self.test_results['binance_connection'] = 'FAILED'
                return False
            
            logger.info("Binance account connection: SUCCESS")
            
            # Test isolated margin
            margin_balance = connector.get_isolated_margin_balance()
            if not margin_balance:
                logger.error("Isolated margin access failed")
                self.test_results['isolated_margin'] = 'FAILED'
                return False
            
            balance = margin_balance['total_usdt_value']
            logger.info(f"Isolated margin balance: ${balance:.2f}")
            
            # Test market data
            ticker = connector.client.get_symbol_ticker(symbol='BTCUSDT')
            price = float(ticker['price'])
            logger.info(f"Current BTC price: ${price:.2f}")
            
            self.test_results['binance_connection'] = 'SUCCESS'
            self.test_results['isolated_margin'] = 'SUCCESS'
            self.test_results['current_balance'] = balance
            self.test_results['btc_price'] = price
            
            return True
            
        except Exception as e:
            logger.error(f"Binance connection test failed: {e}")
            self.test_results['binance_connection'] = 'FAILED'
            self.test_results['error'] = str(e)
            return False
    
    def test_order_permissions(self):
        """Test if we can place orders (without actually placing them)"""
        try:
            logger.info("Testing order permissions...")
            
            from binance_real_money_connector import BinanceRealMoneyConnector
            connector = BinanceRealMoneyConnector()
            
            # Test by getting account info which requires API permissions
            account_info = connector.get_account_info()
            
            # Check if we have trading permissions
            can_trade = account_info.get('canTrade', False)
            can_withdraw = account_info.get('canWithdraw', False)
            can_deposit = account_info.get('canDeposit', False)
            
            logger.info(f"Trading permissions - Trade: {can_trade}, Withdraw: {can_withdraw}, Deposit: {can_deposit}")
            
            if can_trade:
                self.test_results['trading_permissions'] = 'SUCCESS'
                logger.info("Trading permissions: VERIFIED")
                return True
            else:
                self.test_results['trading_permissions'] = 'FAILED'
                logger.error("Trading permissions: DENIED")
                return False
                
        except Exception as e:
            logger.error(f"Order permissions test failed: {e}")
            self.test_results['trading_permissions'] = 'FAILED'
            return False
    
    def test_telegram_bot(self):
        """Test Telegram bot"""
        try:
            logger.info("Testing Telegram bot...")
            
            from telegram_trading_bot import ComprehensiveTelegramTradingBot
            bot = ComprehensiveTelegramTradingBot()
            
            # Test message sending
            test_message = "SYSTEM TEST: Binance connection verified, balance confirmed, ready for testing."
            bot.send_message(test_message)
            
            logger.info("Telegram bot test: SUCCESS")
            self.test_results['telegram_bot'] = 'SUCCESS'
            return True
            
        except Exception as e:
            logger.error(f"Telegram bot test failed: {e}")
            self.test_results['telegram_bot'] = 'FAILED'
            return False
    
    def test_signal_generator(self):
        """Test signal generator"""
        try:
            logger.info("Testing signal generator...")
            
            sys.path.append('02_signal_generator')
            from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
            
            generator = GridAwareSignalGenerator()
            
            # Test model loading
            if generator.load_model():
                logger.info("Signal generator model loading: SUCCESS")
                self.test_results['signal_generator'] = 'SUCCESS'
                return True
            else:
                logger.error("Signal generator model loading: FAILED")
                self.test_results['signal_generator'] = 'FAILED'
                return False
                
        except Exception as e:
            logger.error(f"Signal generator test failed: {e}")
            self.test_results['signal_generator'] = 'FAILED'
            return False
    
    def execute_dry_run_trade(self):
        """Execute a dry run trade (calculate but don't place orders)"""
        try:
            logger.info("Executing dry run trade test...")
            
            # Get current price and balance
            btc_price = self.test_results.get('btc_price', 120000)
            balance = self.test_results.get('current_balance', 600)
            
            # Calculate trade parameters using proven model
            risk_per_trade = 0.01  # 1%
            stop_loss_percent = 0.001  # 0.1%
            take_profit_percent = 0.0025  # 0.25%
            
            risk_amount = balance * risk_per_trade  # $6 risk
            position_size = risk_amount / stop_loss_percent  # $6000 position
            btc_quantity = position_size / btc_price
            
            stop_loss_price = btc_price * (1 - stop_loss_percent)
            take_profit_price = btc_price * (1 + take_profit_percent)
            
            logger.info("DRY RUN TRADE CALCULATION:")
            logger.info(f"  Current Balance: ${balance:.2f}")
            logger.info(f"  BTC Price: ${btc_price:.2f}")
            logger.info(f"  Risk Amount: ${risk_amount:.2f}")
            logger.info(f"  Position Size: ${position_size:.2f}")
            logger.info(f"  BTC Quantity: {btc_quantity:.6f}")
            logger.info(f"  Stop Loss: ${stop_loss_price:.2f} (-0.1%)")
            logger.info(f"  Take Profit: ${take_profit_price:.2f} (+0.25%)")
            logger.info(f"  Risk-Reward Ratio: 2.5:1")
            
            # Store dry run results
            self.test_results['dry_run_trade'] = {
                'balance': balance,
                'btc_price': btc_price,
                'risk_amount': risk_amount,
                'position_size': position_size,
                'btc_quantity': btc_quantity,
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'risk_reward_ratio': 2.5
            }
            
            logger.info("Dry run trade calculation: SUCCESS")
            return True
            
        except Exception as e:
            logger.error(f"Dry run trade test failed: {e}")
            return False
    
    def run_complete_system_test(self):
        """Run complete system test"""
        try:
            logger.info("STARTING COMPLETE SYSTEM TEST")
            logger.info("="*60)
            
            all_tests_passed = True
            
            # Test 1: Binance Connection
            logger.info("TEST 1: Binance Connection")
            if not self.test_binance_connection():
                logger.error("Binance connection test FAILED")
                all_tests_passed = False
            else:
                logger.info("Binance connection test PASSED")
            
            # Test 2: Order Permissions
            logger.info("TEST 2: Order Permissions")
            if not self.test_order_permissions():
                logger.error("Order permissions test FAILED")
                all_tests_passed = False
            else:
                logger.info("Order permissions test PASSED")
            
            # Test 3: Telegram Bot
            logger.info("TEST 3: Telegram Bot")
            if not self.test_telegram_bot():
                logger.error("Telegram bot test FAILED")
                all_tests_passed = False
            else:
                logger.info("Telegram bot test PASSED")
            
            # Test 4: Signal Generator
            logger.info("TEST 4: Signal Generator")
            if not self.test_signal_generator():
                logger.error("Signal generator test FAILED")
                all_tests_passed = False
            else:
                logger.info("Signal generator test PASSED")
            
            # Test 5: Dry Run Trade
            logger.info("TEST 5: Dry Run Trade")
            if not self.execute_dry_run_trade():
                logger.error("Dry run trade test FAILED")
                all_tests_passed = False
            else:
                logger.info("Dry run trade test PASSED")
            
            # Generate results
            self.test_results['overall_status'] = 'PASSED' if all_tests_passed else 'FAILED'
            self.test_results['timestamp'] = datetime.now().isoformat()
            
            # Save results
            with open('system_test_results.json', 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            
            logger.info("="*60)
            if all_tests_passed:
                logger.info("ALL SYSTEM TESTS PASSED")
                logger.info("SYSTEM IS READY FOR LIVE DEPLOYMENT")
            else:
                logger.error("SOME SYSTEM TESTS FAILED")
                logger.error("SYSTEM IS NOT READY FOR LIVE DEPLOYMENT")
            
            return all_tests_passed
            
        except Exception as e:
            logger.error(f"Complete system test failed: {e}")
            return False

def main():
    """Main execution"""
    print("SIMPLE SYSTEM TEST")
    print("Testing all components for live deployment readiness")
    print("="*60)
    
    try:
        # Initialize test
        test = SimpleSystemTest()
        
        # Run complete test
        if test.run_complete_system_test():
            print("\nALL SYSTEM TESTS PASSED!")
            print("SYSTEM IS READY FOR LIVE DEPLOYMENT!")
            
            # Display key results
            results = test.test_results
            print(f"\nKEY RESULTS:")
            print(f"  Binance Connection: {results.get('binance_connection', 'Unknown')}")
            print(f"  Isolated Margin: {results.get('isolated_margin', 'Unknown')}")
            print(f"  Trading Permissions: {results.get('trading_permissions', 'Unknown')}")
            print(f"  Current Balance: ${results.get('current_balance', 0):.2f}")
            print(f"  BTC Price: ${results.get('btc_price', 0):.2f}")
            print(f"  Telegram Bot: {results.get('telegram_bot', 'Unknown')}")
            print(f"  Signal Generator: {results.get('signal_generator', 'Unknown')}")
            
            if 'dry_run_trade' in results:
                dry_run = results['dry_run_trade']
                print(f"\nDRY RUN TRADE CALCULATION:")
                print(f"  Risk per Trade: ${dry_run['risk_amount']:.2f}")
                print(f"  Position Size: ${dry_run['position_size']:.2f}")
                print(f"  Stop Loss: ${dry_run['stop_loss']:.2f}")
                print(f"  Take Profit: ${dry_run['take_profit']:.2f}")
                print(f"  Risk-Reward: {dry_run['risk_reward_ratio']:.1f}:1")
            
        else:
            print("\nSYSTEM TESTS FAILED!")
            print("System is NOT ready for live deployment")
            print("Check system_test_results.json for details")
            
    except Exception as e:
        print(f"\nSYSTEM TEST ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
