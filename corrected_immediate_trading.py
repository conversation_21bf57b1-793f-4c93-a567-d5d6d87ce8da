#!/usr/bin/env python3
"""
Corrected Immediate Trading with Proper Position Sizing
0.1% SL with OCO orders, then TCN-CNN-PPO activation
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
import threading

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedImmediateTrading:
    """Corrected immediate trading with proper position sizing"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.test_trade_active = False
        self.tcn_system_active = False
        
    def initialize_system(self):
        """Initialize system components"""
        try:
            logger.info("🚀 Initializing corrected trading system...")
            
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            logger.info("✅ System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def execute_corrected_trade(self):
        """Execute trade with corrected position sizing"""
        try:
            logger.info("⚡ Executing corrected immediate trade...")
            
            # Get account balance
            balance_info = self.binance.get_account_balance()
            if not balance_info:
                logger.error("❌ Failed to get account balance")
                return False
            
            account_balance = balance_info['total_usdt_value']
            current_price = balance_info['current_btc_price']
            
            # Get market data
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False
            
            current_rsi = market_data['rsi'].iloc[-1]
            current_vwap = market_data['vwap'].iloc[-1]
            
            # Determine signal
            if current_rsi < 50:
                signal = 'BUY'
            else:
                signal = 'SELL'
            
            # Calculate CORRECTED position sizing
            # Risk 0.1% of account balance = $0.60 (for $600 account)
            risk_amount = account_balance * 0.001  # 0.1%
            reward_amount = risk_amount * 2.5      # 2.5:1 ratio
            
            # Calculate price levels
            if signal == 'BUY':
                entry_price = current_price
                stop_loss_price = current_price * 0.999    # 0.1% below
                take_profit_price = current_price * 1.0025 # 0.25% above
            else:
                entry_price = current_price
                stop_loss_price = current_price * 1.001    # 0.1% above
                take_profit_price = current_price * 0.9975 # 0.25% below
            
            # Calculate position size to risk exactly the risk_amount
            price_distance = abs(entry_price - stop_loss_price)
            position_size_btc = risk_amount / price_distance
            position_value_usdt = position_size_btc * entry_price
            
            # Ensure position size is within account limits (max 10% of account)
            max_position_value = account_balance * 0.10  # 10% max
            if position_value_usdt > max_position_value:
                position_size_btc = max_position_value / entry_price
                position_value_usdt = max_position_value
                actual_risk = position_size_btc * price_distance
            else:
                actual_risk = risk_amount
            
            actual_reward = actual_risk * 2.5
            
            # Send trade notification
            if self.telegram:
                trade_message = f"""
⚡ **CORRECTED IMMEDIATE TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Account Balance:** ${account_balance:.2f}
🎯 **Signal:** {signal}
💰 **Entry Price:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position Size:** {position_size_btc:.6f} BTC
💵 **Position Value:** ${position_value_usdt:.2f}
🔴 **Risk Amount:** ${actual_risk:.2f}
🟢 **Reward Amount:** ${actual_reward:.2f}
⚖️ **Risk-Reward:** {actual_reward/actual_risk:.1f}:1
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **RSI:** {current_rsi:.1f}
📊 **VWAP:** ${current_vwap:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute the trade using simplified approach
            try:
                # Place market order
                if signal == 'BUY':
                    market_order = self.binance.client.create_margin_order(
                        symbol='BTCUSDT',
                        side='BUY',
                        type='MARKET',
                        quantity=f"{position_size_btc:.6f}",
                        isIsolated='TRUE'
                    )
                else:
                    market_order = self.binance.client.create_margin_order(
                        symbol='BTCUSDT',
                        side='SELL',
                        type='MARKET',
                        quantity=f"{position_size_btc:.6f}",
                        isIsolated='TRUE'
                    )
                
                if market_order:
                    logger.info("✅ Market order executed successfully")
                    
                    # Place OCO order for exit
                    exit_side = 'SELL' if signal == 'BUY' else 'BUY'
                    
                    oco_order = self.binance.client.create_margin_oco_order(
                        symbol='BTCUSDT',
                        side=exit_side,
                        quantity=f"{position_size_btc:.6f}",
                        price=f"{take_profit_price:.2f}",
                        stopPrice=f"{stop_loss_price:.2f}",
                        stopLimitPrice=f"{stop_loss_price:.2f}",
                        stopLimitTimeInForce='GTC',
                        isIsolated='TRUE'
                    )
                    
                    if oco_order:
                        logger.info("✅ OCO order placed successfully")
                        
                        # Send success notification
                        if self.telegram:
                            success_message = f"""
✅ **TRADE EXECUTED SUCCESSFULLY**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Market Order ID:** {market_order.get('orderId', 'N/A')}
📊 **OCO Order ID:** {oco_order.get('orderListId', 'N/A')}
📋 **Status:** OCO order active
🎯 **Monitoring:** Until TP or SL hit
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Waiting for trade completion...**
"""
                            self.telegram.send_message(success_message)
                        
                        # Start monitoring
                        self.test_trade_active = True
                        self.monitor_trade_completion(market_order, signal, entry_price, stop_loss_price, take_profit_price, actual_risk, actual_reward)
                        return True
                    else:
                        logger.error("❌ OCO order failed")
                        return False
                else:
                    logger.error("❌ Market order failed")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Trade execution error: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Corrected trade execution error: {e}")
            return False
    
    def monitor_trade_completion(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Monitor trade until completion"""
        try:
            logger.info("👁️ Monitoring trade completion...")
            
            # Start monitoring in separate thread
            monitor_thread = threading.Thread(
                target=self._trade_monitoring_loop,
                args=(trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount)
            )
            monitor_thread.daemon = True
            monitor_thread.start()
            
        except Exception as e:
            logger.error(f"❌ Trade monitoring setup error: {e}")
    
    def _trade_monitoring_loop(self, trade_result, signal, entry_price, sl_price, tp_price, risk_amount, reward_amount):
        """Trade monitoring loop"""
        try:
            start_time = datetime.now()
            max_wait_minutes = 60
            check_interval = 30
            
            while self.test_trade_active:
                try:
                    current_price = self.binance.get_current_price()
                    
                    # Check if TP or SL hit
                    trade_completed = False
                    result_type = None
                    pnl = 0
                    
                    if signal == 'BUY':
                        if current_price <= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price >= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount
                    else:  # SELL
                        if current_price >= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -risk_amount
                        elif current_price <= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = reward_amount
                    
                    if trade_completed:
                        self.complete_test_trade(result_type, pnl, current_price)
                        break
                    
                    # Check timeout
                    elapsed_time = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed_time > max_wait_minutes:
                        logger.warning("⏰ Trade monitoring timeout")
                        self.complete_test_trade('TIMEOUT', 0, current_price)
                        break
                    
                    # Send periodic updates
                    if int(elapsed_time) % 5 == 0:  # Every 5 minutes
                        if self.telegram:
                            update_message = f"""
👁️ **TRADE MONITORING UPDATE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **Current Price:** ${current_price:.2f}
🎯 **Entry:** ${entry_price:.2f}
🔴 **Stop Loss:** ${sl_price:.2f}
🟢 **Take Profit:** ${tp_price:.2f}
⏰ **Elapsed:** {elapsed_time:.1f} minutes
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: MONITORING ACTIVE**
"""
                            self.telegram.send_message(update_message)
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Monitoring loop error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ Trade monitoring error: {e}")
            self.complete_test_trade('ERROR', 0, 0)
    
    def complete_test_trade(self, result_type, pnl, final_price):
        """Complete test trade and activate TCN-CNN-PPO"""
        try:
            self.test_trade_active = False
            success = result_type in ['TAKE_PROFIT', 'TIMEOUT']
            
            if self.telegram:
                emoji = "✅" if success else "🔴"
                status = "SUCCESS" if success else "STOPPED"
                next_action = "🚀 **ACTIVATING TCN-CNN-PPO SYSTEM**" if success else "⚠️ **SYSTEM REVIEW REQUIRED**"
                
                completion_message = f"""
{emoji} **TEST TRADE COMPLETED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Result:** {result_type}
💰 **P&L:** ${pnl:+.2f}
📈 **Final Price:** ${final_price:.2f}
⏰ **Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{next_action}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(completion_message)
            
            if success:
                logger.info("✅ Test trade successful - Activating TCN-CNN-PPO system")
                self.activate_tcn_system()
            else:
                logger.error("❌ Test trade failed")
                
        except Exception as e:
            logger.error(f"❌ Test completion error: {e}")
    
    def activate_tcn_system(self):
        """Activate TCN-CNN-PPO system"""
        try:
            logger.info("🚀 Activating TCN-CNN-PPO system...")
            self.tcn_system_active = True
            
            if self.telegram:
                activation_message = f"""
🚀 **TCN-CNN-PPO SYSTEM ACTIVATED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **Test Trade:** Completed successfully
🧠 **AI Model:** TCN-CNN-PPO Ensemble
📊 **Grid Aware:** 0.25% spacing compliance
📈 **Monitoring:** Grid/Sentiment/Confidence
⚖️ **Risk:** 0.1% SL, 0.25% TP
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **LIVE TRADING ACTIVE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(activation_message)
            
            # Start TCN monitoring
            self.start_tcn_monitoring()
            
        except Exception as e:
            logger.error(f"❌ TCN activation error: {e}")
    
    def start_tcn_monitoring(self):
        """Start TCN-CNN-PPO monitoring with comprehensive analysis"""
        try:
            logger.info("🧠 Starting TCN-CNN-PPO comprehensive monitoring...")
            
            check_count = 0
            while self.tcn_system_active:
                try:
                    check_count += 1
                    logger.info(f"🧠 TCN Analysis #{check_count}")
                    
                    # Generate signal
                    signal_data = self.signal_generator.generate_signal()
                    
                    # Get market data
                    market_data = self.binance.get_market_data()
                    if market_data is not None:
                        current_price = market_data['close'].iloc[-1]
                        current_rsi = market_data['rsi'].iloc[-1]
                        current_vwap = market_data['vwap'].iloc[-1]
                        
                        # Market sentiment analysis
                        if current_rsi < 30:
                            sentiment = "🔴 OVERSOLD"
                        elif current_rsi > 70:
                            sentiment = "🟢 OVERBOUGHT"
                        elif current_price > current_vwap:
                            sentiment = "🔵 BULLISH"
                        else:
                            sentiment = "🟠 BEARISH"
                        
                        # Grid analysis
                        grid_level = self.calculate_grid_level(current_price)
                        grid_status = "✅ COMPLIANT" if signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL' else "⚠️ WAITING"
                        
                        # Confidence analysis
                        confidence = signal_data.get('confidence', 0)
                        confidence_status = "🟢 HIGH" if confidence > 0.75 else "🟡 MEDIUM" if confidence > 0.5 else "🔴 LOW"
                        
                        # Send comprehensive monitoring update every 3 checks
                        if check_count % 3 == 0 and self.telegram:
                            monitoring_message = f"""
🧠 **TCN-CNN-PPO COMPREHENSIVE MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {confidence:.2f} ({confidence_status})
📈 **Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
📈 **VWAP:** ${current_vwap:.2f}
{sentiment}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔲 **Grid Level:** {grid_level}
📍 **Grid Status:** {grid_status}
🔍 **Analysis:** #{check_count}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: TCN-CNN-PPO ACTIVE**
"""
                            self.telegram.send_message(monitoring_message)
                        
                        # Execute trade if conditions are met
                        if (signal_data['signal'] != 'HOLD' and 
                            confidence > 0.75 and
                            signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL'):
                            
                            logger.info(f"🧠 High confidence signal detected: {signal_data['signal']}")
                            # Here you would execute the TCN trade
                            # For now, just log it
                    
                    time.sleep(180)  # 3 minutes between checks
                    
                except KeyboardInterrupt:
                    logger.info("🛑 TCN monitoring stopped")
                    break
                except Exception as e:
                    logger.error(f"❌ TCN monitoring error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ TCN monitoring failed: {e}")
    
    def calculate_grid_level(self, current_price):
        """Calculate grid level"""
        try:
            base_price = 100000
            grid_spacing = 0.0025
            level = int((current_price - base_price) / (base_price * grid_spacing))
            return f"Level {level:+d}"
        except:
            return "Unknown"

def main():
    """Main execution"""
    print("⚡ CORRECTED IMMEDIATE TRADING - 0.1% SL OCO")
    print("=" * 60)
    
    trading = CorrectedImmediateTrading()
    
    if not trading.initialize_system():
        print("❌ System initialization failed")
        return
    
    print("⚡ Executing corrected immediate trade...")
    if trading.execute_corrected_trade():
        print("✅ Trade executed - monitoring for completion...")
        
        try:
            while trading.test_trade_active or trading.tcn_system_active:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 System stopped")
            trading.test_trade_active = False
            trading.tcn_system_active = False
    else:
        print("❌ Trade execution failed")

if __name__ == "__main__":
    main()
