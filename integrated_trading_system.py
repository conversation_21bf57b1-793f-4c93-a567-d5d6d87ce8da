#!/usr/bin/env python3
"""
Integrated Trading System
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Complete integration of all system components for real money trading
"""

import json
import time
import logging
import threading
from datetime import datetime
import sys
import os

# Import all system components
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from automated_trading_engine import AutomatedTradingEngine
from binance_real_money_connector import get_binance_connector
from protected_core_system import protection_system, mandatory_security_check
from change_authorization_system import authorization_system
from auto_compliance_startup import AutoComplianceStartup
from guardrails_compliance_check import GuardrailsComplianceCheck

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegratedTradingSystem:
    """Complete integrated trading system for real money Bitcoin trading"""
    
    def __init__(self):
        self.signal_generator = None
        self.trading_engine = None
        self.binance_connector = None
        self.compliance_system = None
        self.running = False
        self.system_status = 'INITIALIZING'
        self.performance_metrics = {
            'trades_today': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'system_uptime': 0
        }
        
    def initialize_system(self):
        """Initialize all system components"""
        try:
            logger.info("🚀 Initializing Integrated Trading System...")
            
            # Step 1: Run compliance startup
            startup_system = AutoComplianceStartup()
            if not startup_system.auto_startup():
                logger.error("❌ Compliance startup failed")
                return False
            
            # Step 2: Initialize Binance connector
            self.binance_connector = get_binance_connector()
            logger.info("✅ Binance connector initialized")
            
            # Step 3: Initialize signal generator
            self.signal_generator = GridAwareSignalGenerator()
            logger.info("✅ Signal generator initialized")
            
            # Step 4: Initialize trading engine
            self.trading_engine = AutomatedTradingEngine()
            logger.info("✅ Trading engine initialized")
            
            # Step 5: Initialize compliance monitoring
            self.compliance_system = GuardrailsComplianceCheck()
            self.compliance_system.start_continuous_monitoring()
            logger.info("✅ Compliance monitoring started")
            
            # Step 6: Start protection system monitoring
            protection_system.start_monitoring()
            logger.info("✅ Protection system monitoring started")
            
            self.system_status = 'READY'
            logger.info("🎯 Integrated Trading System initialized successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            self.system_status = 'FAILED'
            return False
    
    def start_trading(self):
        """Start the complete trading system"""
        try:
            if self.system_status != 'READY':
                logger.error("❌ System not ready for trading")
                return False
            
            # Final security check before starting
            if not mandatory_security_check():
                logger.error("🚨 Mandatory security check failed")
                return False
            
            self.running = True
            self.system_status = 'TRADING'
            
            logger.info("🚀 Starting integrated trading system...")
            
            # Start main trading loop
            self.main_trading_loop()
            
        except Exception as e:
            logger.error(f"❌ Failed to start trading: {e}")
            self.emergency_shutdown()
    
    def main_trading_loop(self):
        """Main trading loop integrating all components"""
        logger.info("🔄 Main trading loop started")
        
        while self.running:
            try:
                # Step 1: Pre-execution security scan
                if not mandatory_security_check():
                    logger.error("🚨 Security check failed - halting trading")
                    self.emergency_shutdown()
                    break
                
                # Step 2: Check compliance
                if not self.compliance_system.run_comprehensive_compliance_check():
                    logger.warning("⚠️ Compliance violations detected")
                    # Continue but log violations
                
                # Step 3: Generate signal
                signal_data = self.signal_generator.generate_signal()
                
                if signal_data['signal'] != 'HOLD':
                    logger.info(f"📡 Signal received: {signal_data['signal']} at ${signal_data['price']:.2f}")
                    
                    # Step 4: Execute trade if signal is valid
                    if signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL':
                        trade_result = self.trading_engine.execute_trade(signal_data)
                        
                        if trade_result:
                            self.performance_metrics['trades_today'] += 1
                            self.performance_metrics['successful_trades'] += 1
                            logger.info("✅ Trade executed successfully")
                        else:
                            self.performance_metrics['failed_trades'] += 1
                            logger.warning("⚠️ Trade execution failed")
                    else:
                        logger.info(f"📊 Signal held: {signal_data.get('reason')}")
                
                # Step 5: Update performance metrics
                self.update_performance_metrics()
                
                # Step 6: Monitor positions
                self.monitor_positions()
                
                # Wait before next iteration
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logger.info("🛑 Trading stopped by user")
                self.stop_trading()
                break
            except Exception as e:
                logger.error(f"❌ Error in trading loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def monitor_positions(self):
        """Monitor active positions and update status"""
        try:
            if self.trading_engine.current_position:
                # Check if position is still active
                open_orders = self.binance_connector.get_open_orders()
                
                if not open_orders:
                    # Position may have been closed
                    logger.info("📊 Checking position status...")
                    
                    # Get current balance to check if position closed
                    balance = self.binance_connector.get_account_balance()
                    if balance:
                        btc_position = balance['btc']['netAsset']
                        if abs(btc_position) < 0.000001:  # Position closed
                            logger.info("✅ Position closed automatically")
                            self.trading_engine.current_position = None
                            
        except Exception as e:
            logger.error(f"❌ Position monitoring error: {e}")
    
    def update_performance_metrics(self):
        """Update system performance metrics"""
        try:
            # Calculate win rate
            total_trades = self.performance_metrics['successful_trades'] + self.performance_metrics['failed_trades']
            if total_trades > 0:
                self.performance_metrics['win_rate'] = self.performance_metrics['successful_trades'] / total_trades
            
            # Get current P&L from Binance
            balance = self.binance_connector.get_account_balance()
            if balance:
                # This would calculate P&L based on starting balance
                # For now, use current balance as proxy
                self.performance_metrics['total_pnl'] = balance['total_usdt_value'] - 100.0  # Assuming $100 start
                
        except Exception as e:
            logger.error(f"❌ Failed to update performance metrics: {e}")
    
    def get_system_status(self):
        """Get comprehensive system status"""
        try:
            balance = self.binance_connector.get_account_balance()
            current_price = self.binance_connector.get_current_price()
            
            status = {
                'system_status': self.system_status,
                'running': self.running,
                'timestamp': datetime.now().isoformat(),
                'performance_metrics': self.performance_metrics,
                'account_balance': balance,
                'current_btc_price': current_price,
                'active_position': self.trading_engine.current_position if self.trading_engine else None,
                'compliance_status': self.compliance_system.get_compliance_report() if self.compliance_system else None,
                'protection_status': protection_system.is_system_secure()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Failed to get system status: {e}")
            return {'error': str(e)}
    
    def stop_trading(self):
        """Stop trading gracefully"""
        try:
            logger.info("🛑 Stopping trading system...")
            
            self.running = False
            self.system_status = 'STOPPING'
            
            # Stop compliance monitoring
            if self.compliance_system:
                self.compliance_system.stop_monitoring()
            
            # Stop protection monitoring
            protection_system.monitoring_active = False
            
            self.system_status = 'STOPPED'
            logger.info("✅ Trading system stopped gracefully")
            
        except Exception as e:
            logger.error(f"❌ Error stopping trading system: {e}")
    
    def emergency_shutdown(self):
        """Emergency shutdown of entire system"""
        try:
            logger.critical("🚨 EMERGENCY SHUTDOWN INITIATED")
            
            self.running = False
            self.system_status = 'EMERGENCY_SHUTDOWN'
            
            # Emergency shutdown Binance connector
            if self.binance_connector:
                self.binance_connector.emergency_shutdown()
            
            # Stop all monitoring
            if self.compliance_system:
                self.compliance_system.stop_monitoring()
            
            protection_system.emergency_shutdown("System emergency shutdown")
            
            logger.critical("🚨 Emergency shutdown completed")
            
        except Exception as e:
            logger.critical(f"❌ Emergency shutdown error: {e}")
    
    def run_system_diagnostics(self):
        """Run comprehensive system diagnostics"""
        try:
            logger.info("🔍 Running system diagnostics...")
            
            diagnostics = {
                'timestamp': datetime.now().isoformat(),
                'binance_connection': False,
                'signal_generator': False,
                'trading_engine': False,
                'compliance_system': False,
                'protection_system': False,
                'overall_health': False
            }
            
            # Test Binance connection
            try:
                balance = self.binance_connector.get_account_balance()
                diagnostics['binance_connection'] = balance is not None
            except:
                pass
            
            # Test signal generator
            try:
                if self.signal_generator:
                    diagnostics['signal_generator'] = True
            except:
                pass
            
            # Test trading engine
            try:
                if self.trading_engine:
                    diagnostics['trading_engine'] = True
            except:
                pass
            
            # Test compliance system
            try:
                if self.compliance_system:
                    diagnostics['compliance_system'] = True
            except:
                pass
            
            # Test protection system
            try:
                diagnostics['protection_system'] = protection_system.is_system_secure()
            except:
                pass
            
            # Overall health
            diagnostics['overall_health'] = all([
                diagnostics['binance_connection'],
                diagnostics['signal_generator'],
                diagnostics['trading_engine'],
                diagnostics['compliance_system'],
                diagnostics['protection_system']
            ])
            
            logger.info(f"📊 System diagnostics completed: {diagnostics['overall_health']}")
            return diagnostics
            
        except Exception as e:
            logger.error(f"❌ System diagnostics failed: {e}")
            return {'error': str(e)}

def main():
    """Main function to start the integrated trading system"""
    print("🚀 Integrated Trading System")
    print("=" * 50)
    print("100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md")
    print("Real money Bitcoin trading with full security")
    print("=" * 50)
    
    # Initialize system
    trading_system = IntegratedTradingSystem()
    
    # Initialize all components
    if not trading_system.initialize_system():
        print("❌ System initialization failed")
        return False
    
    print("✅ System initialized successfully")
    
    # Run diagnostics
    diagnostics = trading_system.run_system_diagnostics()
    if diagnostics.get('overall_health'):
        print("✅ All system diagnostics passed")
    else:
        print("⚠️ Some diagnostic checks failed")
        print(f"📊 Diagnostics: {diagnostics}")
    
    # Start trading
    try:
        print("🚀 Starting real money trading...")
        trading_system.start_trading()
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
        trading_system.stop_trading()
    except Exception as e:
        print(f"❌ System error: {e}")
        trading_system.emergency_shutdown()
    
    print("✅ System shutdown completed")
    return True

if __name__ == "__main__":
    main()
