#!/usr/bin/env python3
"""
Test Results Loading - Check if the model and results work correctly
"""

import torch
import json
import numpy as np
from datetime import datetime

def test_model_loading():
    """Test if the saved model loads correctly"""
    print("🧪 Testing model loading...")
    
    try:
        model_path = '02_signal_generator/models/final_tcn_cnn_ppo_ensemble.pth'
        checkpoint = torch.load(model_path, map_location='cpu')
        
        print("✅ Model loaded successfully!")
        print(f"   Architecture: {checkpoint['model_config']['architecture']}")
        print(f"   Training Date: {checkpoint['model_config']['training_date']}")
        print(f"   Best Validation Accuracy: {checkpoint['model_config']['best_val_accuracy']}")
        print(f"   Ensemble Components: {checkpoint['model_config']['ensemble_components']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False

def test_json_serialization():
    """Test JSON serialization with numpy type conversion"""
    print("\n🧪 Testing JSON serialization...")
    
    def convert_numpy_types(obj):
        """Convert numpy types to native Python types"""
        if isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    # Test data with numpy types
    test_data = {
        'win_rate': np.float64(39.5),
        'trades_per_day': np.float32(0.1),
        'total_trades': np.int64(38),
        'compliance_met': np.bool_(False),
        'hierarchy_correct': np.bool_(False),
        'array_data': np.array([1, 2, 3]),
        'nested': {
            'score': np.float64(0.734),
            'flag': np.bool_(True)
        }
    }
    
    try:
        # Convert numpy types
        converted_data = convert_numpy_types(test_data)
        
        # Test JSON serialization
        json_str = json.dumps(converted_data, indent=2)
        
        # Test JSON deserialization
        loaded_data = json.loads(json_str)
        
        print("✅ JSON serialization test passed!")
        print("   Sample converted data:")
        for key, value in converted_data.items():
            if key != 'array_data':
                print(f"     {key}: {value} ({type(value).__name__})")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON serialization test failed: {e}")
        return False

def create_sample_results():
    """Create sample results file to test the fix"""
    print("\n🧪 Creating sample results file...")
    
    def convert_numpy_types(obj):
        """Convert numpy types to native Python types"""
        if isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    # Sample results with the actual values from the training
    sample_results = {
        'results': {
            'training': {
                'phase': 'Training',
                'grid_signals': 1807,
                'actual_trades': 38,
                'total_trades': 38,
                'winning_trades': 15,
                'win_rate': np.float64(39.5),
                'trades_per_day': np.float32(0.1),
                'composite_score': np.float64(0.734),
                'net_profit': np.float64(128.18),
                'corrected_reward': np.float64(94.11),
                'compliance_score': np.float64(0.0),
                'compliance_details': {
                    'win_rate_target': np.bool_(False),
                    'trades_per_day_target': np.bool_(False),
                    'composite_score_target': np.bool_(False)
                },
                'final_balance': np.float64(1128.18)
            },
            'out_of_sample': {
                'phase': 'Out-of-Sample',
                'grid_signals': 991,
                'actual_trades': 31,
                'total_trades': 31,
                'winning_trades': 8,
                'win_rate': np.float64(25.8),
                'trades_per_day': np.float32(0.1),
                'composite_score': np.float64(0.705),
                'net_profit': np.float64(-107.27),
                'corrected_reward': np.float64(0.0),
                'compliance_score': np.float64(0.0),
                'compliance_details': {
                    'win_rate_target': np.bool_(False),
                    'trades_per_day_target': np.bool_(False),
                    'composite_score_target': np.bool_(False)
                },
                'final_balance': np.float64(892.73)
            },
            'backtest': {
                'phase': 'Backtest',
                'grid_signals': 2363,
                'actual_trades': 0,
                'total_trades': 0,
                'winning_trades': 0,
                'win_rate': np.float64(0.0),
                'trades_per_day': np.float32(0.0),
                'composite_score': np.float64(0.0),
                'net_profit': np.float64(0.0),
                'corrected_reward': np.float64(0.0),
                'compliance_score': np.float64(0.0),
                'compliance_details': {
                    'win_rate_target': np.bool_(False),
                    'trades_per_day_target': np.bool_(False),
                    'composite_score_target': np.bool_(False)
                },
                'final_balance': np.float64(1000.0)
            }
        },
        'hierarchy_correct': np.bool_(False),
        'overall_compliance': np.float64(0.0),
        'perfect_compliance': np.bool_(False),
        'master_document_compliance': {
            'data_split': {
                'training_years': [2021, 2022],
                'out_of_sample_years': [2023],
                'backtest_years': [2024]
            },
            'parameters': {
                'grid_spacing': 0.0025,
                'grid_tolerance': 0.001,
                'risk_reward_ratio': 2.5,
                'risk_per_trade': 0.01,
                'confidence_threshold': 0.75,
                'architecture': 'ensemble_tcn_cnn_ppo'
            },
            'targets': {
                'win_rate': 60.0,
                'trades_per_day': 8.0,
                'composite_score': 0.8
            },
            'architecture': 'TCN-CNN-PPO Ensemble',
            'features': 135
        },
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        # Convert numpy types
        converted_results = convert_numpy_types(sample_results)
        
        # Save to JSON
        with open('final_tcn_cnn_ppo_ensemble_results.json', 'w') as f:
            json.dump(converted_results, f, indent=2)
        
        print("✅ Sample results file created successfully!")
        print("   File: final_tcn_cnn_ppo_ensemble_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample results creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING RESULTS LOADING AND JSON SERIALIZATION")
    print("=" * 60)
    
    # Test 1: Model loading
    model_test = test_model_loading()
    
    # Test 2: JSON serialization
    json_test = test_json_serialization()
    
    # Test 3: Create sample results
    results_test = create_sample_results()
    
    print("\n" + "=" * 60)
    print("🧪 TEST SUMMARY:")
    print(f"   Model Loading: {'✅ PASSED' if model_test else '❌ FAILED'}")
    print(f"   JSON Serialization: {'✅ PASSED' if json_test else '❌ FAILED'}")
    print(f"   Results Creation: {'✅ PASSED' if results_test else '❌ FAILED'}")
    
    all_passed = model_test and json_test and results_test
    print(f"   Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎯 The JSON serialization fix is working correctly!")
        print("   The final_tcn_cnn_ppo_ensemble_trainer.py should now work without errors.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
