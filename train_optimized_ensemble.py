#!/usr/bin/env python3
"""
Optimized Ensemble TCN-CNN-PPO Training
Using Best Hyperparameters from Tuning
Optimized for: Composite Score × Net Profit
"""

import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from datetime import datetime
from torch.utils.data import DataLoader, TensorDataset
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedEnsembleModel(nn.Module):
    """Optimized Ensemble Model with Best Hyperparameters"""
    
    def __init__(self):
        super(OptimizedEnsembleModel, self).__init__()
        
        # Best hyperparameters from tuning
        self.config = {
            "tcn_channels": [128, 256],
            "cnn_channels": [16, 32],
            "ppo_hidden_dims": [128],
            "learning_rates": {
                "tcn": 0.001,
                "cnn": 0.01,
                "ppo": 0.0003,
                "ensemble": 0.1
            },
            "dropout_rate": 0.2,
            "batch_size": 32,
            "sequence_length": 60,
            "epochs": 100  # Increased for full training
        }
        
        # Optimized TCN Component
        self.tcn = nn.Sequential(
            nn.Conv1d(4, self.config['tcn_channels'][0], 3, padding=1),
            nn.ReLU(),
            nn.Dropout(self.config['dropout_rate']),
            nn.Conv1d(self.config['tcn_channels'][0], self.config['tcn_channels'][1], 3, padding=1),
            nn.ReLU(),
            nn.Dropout(self.config['dropout_rate']),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(self.config['tcn_channels'][1], 3)
        )
        
        # Optimized CNN Component
        self.cnn = nn.Sequential(
            nn.Conv1d(4, self.config['cnn_channels'][0], 5, padding=2),
            nn.ReLU(),
            nn.Dropout(self.config['dropout_rate']),
            nn.Conv1d(self.config['cnn_channels'][0], self.config['cnn_channels'][1], 3, padding=1),
            nn.ReLU(),
            nn.Dropout(self.config['dropout_rate']),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(self.config['cnn_channels'][1], 3)
        )
        
        # Optimized PPO Component
        self.ppo = nn.Sequential(
            nn.Linear(4, self.config['ppo_hidden_dims'][0]),
            nn.ReLU(),
            nn.Dropout(self.config['dropout_rate']),
            nn.Linear(self.config['ppo_hidden_dims'][0], 3)
        )
        
        # Learnable ensemble weights
        self.ensemble_weights = nn.Parameter(torch.tensor([0.333, 0.333, 0.334]))
        
        logger.info("🎯 Optimized Ensemble Model Initialized")
        logger.info(f"📊 TCN Channels: {self.config['tcn_channels']}")
        logger.info(f"📊 CNN Channels: {self.config['cnn_channels']}")
        logger.info(f"📊 PPO Hidden: {self.config['ppo_hidden_dims']}")
        logger.info(f"📊 Dropout Rate: {self.config['dropout_rate']}")
    
    def forward(self, x):
        """Forward pass through optimized ensemble"""
        # x shape: (batch, sequence, features)
        x_transposed = x.transpose(1, 2)  # (batch, features, sequence)
        
        # Individual component predictions
        tcn_pred = torch.softmax(self.tcn(x_transposed), dim=1)
        cnn_pred = torch.softmax(self.cnn(x_transposed), dim=1)
        ppo_pred = torch.softmax(self.ppo(x[:, -1, :]), dim=1)  # Last timestep for PPO
        
        # Ensemble combination with learned weights
        normalized_weights = torch.softmax(self.ensemble_weights, dim=0)
        ensemble_pred = (normalized_weights[0] * tcn_pred + 
                        normalized_weights[1] * cnn_pred + 
                        normalized_weights[2] * ppo_pred)
        
        return ensemble_pred

class OptimizedEnsembleTrainer:
    """Optimized trainer using best hyperparameters"""
    
    def __init__(self):
        self.model = OptimizedEnsembleModel()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # Optimized optimizers with best learning rates
        self.tcn_optimizer = optim.Adam(
            self.model.tcn.parameters(), 
            lr=self.model.config['learning_rates']['tcn']
        )
        self.cnn_optimizer = optim.Adam(
            self.model.cnn.parameters(), 
            lr=self.model.config['learning_rates']['cnn']
        )
        self.ppo_optimizer = optim.Adam(
            self.model.ppo.parameters(), 
            lr=self.model.config['learning_rates']['ppo']
        )
        self.ensemble_optimizer = optim.Adam(
            [self.model.ensemble_weights], 
            lr=self.model.config['learning_rates']['ensemble']
        )
        
        self.criterion = nn.CrossEntropyLoss()
        
        logger.info("🎯 Optimized Trainer Initialized")
        logger.info(f"📊 Learning Rates: {self.model.config['learning_rates']}")
    
    def load_data(self):
        """Load data with backward split"""
        try:
            df = pd.read_json('real_bitcoin_4year_data.json', orient='records')
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['year'] = df['datetime'].dt.year
            
            # Backward from today split
            train_data = df[df['year'].isin([2021, 2022])].copy()  # Historical learning
            val_data = df[df['year'].isin([2023])].copy()          # Recent validation
            test_data = df[df['year'].isin([2024])].copy()         # Most recent backtest
            
            logger.info(f"📊 Training: {len(train_data)} samples (2021-2022)")
            logger.info(f"📊 Validation: {len(val_data)} samples (2023)")
            logger.info(f"📊 Backtest: {len(test_data)} samples (2024)")
            
            return train_data, val_data, test_data
            
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            return None, None, None
    
    def prepare_data_loader(self, data, shuffle=True):
        """Prepare optimized data loader"""
        sequences = []
        targets = []
        
        seq_len = self.model.config['sequence_length']
        
        for i in range(seq_len, len(data)):
            # Market data sequence (OHLCV)
            sequence = data.iloc[i-seq_len:i][['open', 'high', 'low', 'close']].values
            
            # Grid-to-grid movement target
            if i < len(data) - 1:
                current_grid = data.iloc[i]['grid_level']
                next_grid = data.iloc[i+1]['grid_level']
                
                if next_grid > current_grid:
                    target = 0  # UP
                elif next_grid < current_grid:
                    target = 1  # DOWN
                else:
                    target = 2  # HOLD
            else:
                target = 2
            
            sequences.append(sequence)
            targets.append(target)
        
        # Convert to tensors
        X = torch.FloatTensor(np.array(sequences))
        y = torch.LongTensor(np.array(targets))
        
        dataset = TensorDataset(X, y)
        return DataLoader(
            dataset, 
            batch_size=self.model.config['batch_size'], 
            shuffle=shuffle
        )
    
    def train_epoch(self, train_loader):
        """Train one epoch with optimized parameters"""
        self.model.train()
        total_loss = 0.0
        correct_predictions = 0
        total_predictions = 0
        
        for batch_idx, (data, targets) in enumerate(train_loader):
            data = data.to(self.device)
            targets = targets.to(self.device)
            
            # Zero gradients
            self.tcn_optimizer.zero_grad()
            self.cnn_optimizer.zero_grad()
            self.ppo_optimizer.zero_grad()
            self.ensemble_optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(data)
            loss = self.criterion(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Update all components
            self.tcn_optimizer.step()
            self.cnn_optimizer.step()
            self.ppo_optimizer.step()
            self.ensemble_optimizer.step()
            
            # Clamp ensemble weights to stay positive
            with torch.no_grad():
                self.model.ensemble_weights.clamp_(min=0.01)
            
            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_predictions += targets.size(0)
            correct_predictions += (predicted == targets).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct_predictions / total_predictions
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader):
        """Validate one epoch"""
        self.model.eval()
        total_loss = 0.0
        correct_predictions = 0
        total_predictions = 0
        
        with torch.no_grad():
            for data, targets in val_loader:
                data = data.to(self.device)
                targets = targets.to(self.device)
                
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_predictions += targets.size(0)
                correct_predictions += (predicted == targets).sum().item()
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct_predictions / total_predictions
        
        return avg_loss, accuracy
    
    def calculate_composite_score_and_reward(self, accuracy, net_profit):
        """Calculate composite score and reward"""
        # Simplified composite score based on accuracy
        composite_score = min(accuracy / 0.60, 1.0)  # Normalize to 60% target
        
        # User preferred reward: composite_score × net_profit
        reward = composite_score * net_profit
        
        return composite_score, reward
    
    def train_optimized_ensemble(self):
        """Train optimized ensemble model"""
        logger.info("🚀 Starting Optimized Ensemble Training")
        logger.info("🎯 Using Best Hyperparameters from Tuning")
        logger.info("🎯 Optimization Target: Composite Score × Net Profit")
        
        # Load data
        train_data, val_data, test_data = self.load_data()
        if train_data is None:
            logger.error("❌ Training failed - data loading error")
            return False
        
        # Prepare data loaders
        train_loader = self.prepare_data_loader(train_data, shuffle=True)
        val_loader = self.prepare_data_loader(val_data, shuffle=False)
        test_loader = self.prepare_data_loader(test_data, shuffle=False)
        
        # Training loop
        best_reward = 0.0
        best_accuracy = 0.0
        training_history = []
        
        epochs = self.model.config['epochs']
        
        for epoch in range(epochs):
            # Training phase
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # Validation phase
            val_loss, val_acc = self.validate_epoch(val_loader)
            
            # Calculate reward (simplified)
            net_profit = val_acc * 100 - (1 - val_acc) * 40  # 2.5:1 risk-reward approximation
            composite_score, reward = self.calculate_composite_score_and_reward(val_acc, net_profit)
            
            # Record history
            training_history.append({
                'epoch': epoch,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc,
                'composite_score': composite_score,
                'net_profit': net_profit,
                'reward': reward
            })
            
            # Save best model based on reward
            if reward > best_reward:
                best_reward = reward
                best_accuracy = val_acc
                self.save_optimized_model(epoch, val_acc, reward)
            
            # Logging every 10 epochs
            if epoch % 10 == 0 or epoch == epochs - 1:
                logger.info(f"Epoch {epoch}/{epochs}")
                logger.info(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
                logger.info(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
                logger.info(f"Composite Score: {composite_score:.4f}")
                logger.info(f"Net Profit: {net_profit:.2f}")
                logger.info(f"Reward: {reward:.4f} (Best: {best_reward:.4f})")
                logger.info(f"Ensemble Weights: {self.model.ensemble_weights.data}")
                logger.info("-" * 80)
        
        # Final evaluation on test set
        test_loss, test_acc = self.validate_epoch(test_loader)
        test_net_profit = test_acc * 100 - (1 - test_acc) * 40
        test_composite_score, test_reward = self.calculate_composite_score_and_reward(test_acc, test_net_profit)
        
        # Final results
        logger.info("🎯 OPTIMIZED ENSEMBLE TRAINING COMPLETED")
        logger.info("=" * 80)
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"Best Validation Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")
        logger.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.1f}%)")
        logger.info(f"Best Reward: {best_reward:.4f}")
        logger.info(f"Test Reward: {test_reward:.4f}")
        logger.info(f"Test Composite Score: {test_composite_score:.4f}")
        logger.info(f"Test Net Profit: {test_net_profit:.2f}")
        
        # Master document compliance
        target_accuracy = 0.60
        compliance_passed = test_acc >= target_accuracy
        
        logger.info(f"\n📋 MASTER DOCUMENT COMPLIANCE:")
        logger.info(f"Target Accuracy: ≥{target_accuracy:.1%}")
        logger.info(f"Achieved Accuracy: {test_acc:.1%}")
        logger.info(f"Compliance Status: {'✅ PASSED' if compliance_passed else '❌ FAILED'}")
        
        # Final ensemble weights
        final_weights = self.model.ensemble_weights.data
        logger.info(f"\n🔧 FINAL OPTIMIZED ENSEMBLE WEIGHTS:")
        logger.info(f"TCN Weight: {final_weights[0]:.3f}")
        logger.info(f"CNN Weight: {final_weights[1]:.3f}")
        logger.info(f"PPO Weight: {final_weights[2]:.3f}")
        
        logger.info("=" * 80)
        
        return compliance_passed
    
    def save_optimized_model(self, epoch, accuracy, reward):
        """Save optimized model"""
        model_path = f"02_signal_generator/models/optimized_ensemble_epoch_{epoch}_acc_{accuracy:.4f}_reward_{reward:.4f}.pth"
        
        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'tcn_optimizer_state_dict': self.tcn_optimizer.state_dict(),
            'cnn_optimizer_state_dict': self.cnn_optimizer.state_dict(),
            'ppo_optimizer_state_dict': self.ppo_optimizer.state_dict(),
            'ensemble_optimizer_state_dict': self.ensemble_optimizer.state_dict(),
            'accuracy': accuracy,
            'reward': reward,
            'config': self.model.config,
            'ensemble_weights': self.model.ensemble_weights.data,
            'optimization_target': 'composite_score_x_net_profit'
        }, model_path)
        
        logger.info(f"💾 Optimized model saved: {model_path}")

def main():
    """Main optimized training execution"""
    print("🎯 OPTIMIZED ENSEMBLE TCN-CNN-PPO TRAINING")
    print("🎯 Using Best Hyperparameters from Tuning")
    print("🎯 Optimization Target: Composite Score × Net Profit")
    print("🎯 Aim for Highest Reward")
    print("=" * 80)
    
    # Initialize optimized trainer
    trainer = OptimizedEnsembleTrainer()
    
    # Execute optimized training
    success = trainer.train_optimized_ensemble()
    
    if success:
        print("🎉 OPTIMIZED TRAINING COMPLETED SUCCESSFULLY")
        print("✅ MASTER DOCUMENT COMPLIANCE: PASSED")
        print("🚀 MODEL READY FOR LIVE TRADING")
    else:
        print("❌ OPTIMIZED TRAINING COMPLETED")
        print("❌ MASTER DOCUMENT COMPLIANCE: NEEDS IMPROVEMENT")
        print("🔧 CONSIDER FURTHER HYPERPARAMETER TUNING")

if __name__ == "__main__":
    main()
