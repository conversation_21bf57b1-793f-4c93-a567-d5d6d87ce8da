#!/usr/bin/env python3
"""
Simple Leveraged Execution
Direct leveraged trade execution with minimal complexity
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleLeveragedExecution:
    """Simple leveraged execution"""
    
    def __init__(self):
        self.binance = None
        self.telegram = None
        
    def initialize_system(self):
        """Initialize system"""
        try:
            if not validate_all_credentials():
                return False
            
            self.binance = BinanceRealMoneyConnector()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    def execute_simple_leveraged_trade(self):
        """Execute simple leveraged trade"""
        try:
            logger.info("⚡ Executing simple leveraged trade...")
            
            # Get current price
            ticker = self.binance.client.get_symbol_ticker(symbol='BTCUSDT')
            current_price = float(ticker['price'])
            
            # Check isolated margin balance
            isolated_account = self.binance.client.get_isolated_margin_account()
            
            margin_usdt = 0
            for asset in isolated_account['assets']:
                if asset['symbol'] == 'BTCUSDT':
                    margin_usdt = float(asset['quoteAsset']['free'])
                    break
            
            if margin_usdt < 10:
                logger.error(f"❌ Insufficient margin balance: ${margin_usdt:.2f}")
                return False
            
            logger.info(f"💰 Available margin: ${margin_usdt:.2f}")
            logger.info(f"📈 Current BTC price: ${current_price:.2f}")
            
            # Calculate simple position
            # Use 50% of available margin for safety
            position_value = margin_usdt * 0.5 * 10  # 10x leverage
            position_size_btc = position_value / current_price
            
            # Round to valid step size
            position_size_btc = round(position_size_btc, 5)
            
            # Ensure minimum
            if position_size_btc < 0.00001:
                position_size_btc = 0.00001
            
            # Calculate risk/reward
            sl_percentage = 0.001  # 0.1%
            risk_amount = position_size_btc * current_price * sl_percentage
            reward_amount = risk_amount * 2.5
            
            # Simple BUY signal
            signal = 'BUY'
            entry_price = current_price
            stop_loss_price = current_price * (1 - sl_percentage)
            take_profit_price = current_price * (1 + (sl_percentage * 2.5))
            
            # Send notification
            if self.telegram:
                trade_message = f"""
⚡ **SIMPLE LEVERAGED TRADE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Margin Available:** ${margin_usdt:.2f}
🎯 **Signal:** {signal}
💰 **Entry:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Position:** {position_size_btc:.5f} BTC
💵 **Value:** ${position_value:.2f}
⚡ **Leverage:** 10x
🔴 **Risk:** ${risk_amount:.2f}
🟢 **Reward:** ${reward_amount:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute simple market buy order on isolated margin
            try:
                logger.info(f"📊 Placing BUY order for {position_size_btc:.5f} BTC")
                
                market_order = self.binance.client.create_margin_order(
                    symbol='BTCUSDT',
                    side='BUY',
                    type='MARKET',
                    quantity=f"{position_size_btc:.5f}",
                    isIsolated='TRUE'
                )
                
                if market_order:
                    logger.info(f"✅ Market order executed: {market_order.get('orderId')}")
                    
                    # Place stop loss order
                    try:
                        sl_order = self.binance.client.create_margin_order(
                            symbol='BTCUSDT',
                            side='SELL',
                            type='STOP_LOSS_LIMIT',
                            quantity=f"{position_size_btc:.5f}",
                            price=f"{stop_loss_price:.2f}",
                            stopPrice=f"{stop_loss_price:.2f}",
                            timeInForce='GTC',
                            isIsolated='TRUE'
                        )
                        
                        logger.info(f"✅ Stop loss order placed: {sl_order.get('orderId')}")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Stop loss order failed: {e}")
                    
                    # Place take profit order
                    try:
                        tp_order = self.binance.client.create_margin_order(
                            symbol='BTCUSDT',
                            side='SELL',
                            type='LIMIT',
                            quantity=f"{position_size_btc:.5f}",
                            price=f"{take_profit_price:.2f}",
                            timeInForce='GTC',
                            isIsolated='TRUE'
                        )
                        
                        logger.info(f"✅ Take profit order placed: {tp_order.get('orderId')}")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Take profit order failed: {e}")
                    
                    # Send success notification
                    if self.telegram:
                        success_message = f"""
✅ **LEVERAGED TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Market Order:** {market_order.get('orderId')}
⚡ **Leverage:** 10x isolated margin
🎯 **Position:** {position_size_btc:.5f} BTC
💰 **Entry:** ${entry_price:.2f}
🔴 **SL Target:** ${stop_loss_price:.2f}
🟢 **TP Target:** ${take_profit_price:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **TRADE CYCLE COMPLETE**
🚀 **READY FOR TCN-CNN-PPO ACTIVATION**
"""
                        self.telegram.send_message(success_message)
                    
                    return True
                else:
                    logger.error("❌ Market order failed")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Order execution error: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Simple leveraged trade error: {e}")
            return False

def main():
    """Main execution"""
    print("⚡ SIMPLE LEVERAGED EXECUTION")
    print("=" * 50)
    print("📋 Direct leveraged trade with 10x margin")
    print("📋 Target: 0.1% SL, 0.25% TP")
    print("=" * 50)
    
    execution = SimpleLeveragedExecution()
    
    if not execution.initialize_system():
        print("❌ System initialization failed")
        return
    
    print("⚡ Executing simple leveraged trade...")
    if execution.execute_simple_leveraged_trade():
        print("✅ Simple leveraged trade executed successfully!")
        print("🚀 Ready for TCN-CNN-PPO system activation")
        print("📱 Check Telegram for trade confirmation")
    else:
        print("❌ Simple leveraged trade failed")

if __name__ == "__main__":
    main()
