#!/usr/bin/env python3
"""
EXECUTE COMPOUNDING SYSTEM
Direct execution of compounding real money trading system
"""

import sys
import time
from datetime import datetime

# Add paths
sys.path.append('01_binance_connector')
sys.path.append('06_telegram_system')

def main():
    print("🚀 COMPOUNDING REAL MONEY TRADING SYSTEM")
    print("Starting Balance Equivalent: $100")
    print("Risk: 1% ($1) | Reward: 2.5% ($2.5) | RR: 2.5:1")
    print("Compounding: ENABLED | Real Money: LIVE")
    print("="*80)
    
    try:
        # Step 1: Initialize Binance and get account status
        print("Step 1: Initializing system components...")
        
        from binance_real_money_connector import BinanceRealMoneyConnector
        binance = BinanceRealMoneyConnector()
        
        # Get balance
        balance_info = binance.get_isolated_margin_balance()
        current_balance = balance_info['total_usdt_value']
        usdt_balance = balance_info['usdt_balance']
        btc_balance = balance_info['btc_balance']
        
        print(f"✅ Account Status:")
        print(f"  Total Balance: ${current_balance:.2f}")
        print(f"  USDT Balance: ${usdt_balance:.2f}")
        print(f"  BTC Balance: {btc_balance:.8f}")
        
        # Step 2: Setup compounding money management
        print(f"\nStep 2: Setting up compounding money management...")
        
        starting_balance_equivalent = 100.0
        risk_percent = 0.01  # 1%
        reward_percent = 0.025  # 2.5%
        
        # Calculate compounding parameters
        if current_balance >= starting_balance_equivalent:
            # Use full $100 equivalent
            risk_amount = starting_balance_equivalent * risk_percent  # $1
            reward_amount = starting_balance_equivalent * reward_percent  # $2.5
            compounding_factor = 1.0
        else:
            # Scale to current balance
            compounding_factor = current_balance / starting_balance_equivalent
            risk_amount = current_balance * risk_percent
            reward_amount = current_balance * reward_percent
        
        print(f"✅ Money Management Setup:")
        print(f"  Starting Balance Equivalent: ${starting_balance_equivalent:.2f}")
        print(f"  Current Balance: ${current_balance:.2f}")
        print(f"  Compounding Factor: {compounding_factor:.3f}x")
        print(f"  Risk per Trade: ${risk_amount:.2f} ({risk_percent:.1%})")
        print(f"  Reward per Trade: ${reward_amount:.2f} ({reward_percent:.1%})")
        print(f"  Risk-Reward Ratio: {reward_amount/risk_amount:.1f}:1")
        
        # Step 3: Calculate position size for test trade
        print(f"\nStep 3: Calculating position size for test trade...")
        
        # Get current BTC price
        ticker = binance.client.get_symbol_ticker(symbol='BTCUSDT')
        current_price = float(ticker['price'])
        
        # Calculate position size to achieve risk amount
        position_size = risk_amount / risk_percent  # This gives us the position size needed
        
        # Calculate BTC quantity
        btc_quantity = position_size / current_price
        btc_quantity = round(btc_quantity, 8)
        
        # Ensure minimum quantity (0.00001 BTC)
        if btc_quantity < 0.00001:
            btc_quantity = 0.00001
            position_size = btc_quantity * current_price
            # Recalculate risk/reward based on actual position
            risk_amount = position_size * risk_percent
            reward_amount = position_size * reward_percent
        
        # Calculate SL and TP prices
        stop_loss_price = round(current_price * (1 - risk_percent), 2)
        take_profit_price = round(current_price * (1 + reward_percent), 2)
        
        print(f"✅ Position Calculation:")
        print(f"  Current BTC Price: ${current_price:.2f}")
        print(f"  Position Size: ${position_size:.2f}")
        print(f"  BTC Quantity: {btc_quantity:.8f}")
        print(f"  Stop Loss: ${stop_loss_price:.2f} (${risk_amount:.2f})")
        print(f"  Take Profit: ${take_profit_price:.2f} (${reward_amount:.2f})")
        print(f"  Risk-Reward: {reward_amount/risk_amount:.1f}:1")
        
        # Step 4: Validate sufficient balance
        if position_size > usdt_balance:
            print(f"❌ Insufficient balance: Need ${position_size:.2f}, Have ${usdt_balance:.2f}")
            return
        
        print(f"✅ Balance Check: PASSED")
        
        # Step 5: Initialize Telegram bot
        print(f"\nStep 5: Initializing Telegram monitoring...")
        
        try:
            from telegram_trading_bot import ComprehensiveTelegramTradingBot
            telegram_bot = ComprehensiveTelegramTradingBot()
            print(f"✅ Telegram bot: SUCCESS")
            
            # Send system startup notification
            startup_message = f"""
🚀 COMPOUNDING REAL MONEY SYSTEM STARTUP

💰 MONEY MANAGEMENT STATUS:
- Starting Balance Equivalent: ${starting_balance_equivalent:.2f}
- Current Balance: ${current_balance:.2f}
- Compounding Factor: {compounding_factor:.3f}x
- Risk per Trade: ${risk_amount:.2f} (1%)
- Reward per Trade: ${reward_amount:.2f} (2.5%)
- Risk-Reward Ratio: {reward_amount/risk_amount:.1f}:1

📊 SYSTEM STATUS:
- Compounding: ENABLED
- Real Money Trading: ACTIVE
- Telegram Monitoring: OPERATIONAL
- Safety Checks: ENABLED

🎯 EXECUTING TEST TRADE CYCLE
Position: ${position_size:.2f} ({btc_quantity:.8f} BTC)
SL: ${stop_loss_price:.2f} | TP: ${take_profit_price:.2f}
"""
            telegram_bot.send_message(startup_message)
            
        except Exception as e:
            print(f"⚠️ Telegram bot failed: {e}")
            telegram_bot = None
        
        # Step 6: Confirm execution
        print(f"\n⚠️  READY TO EXECUTE REAL MONEY COMPOUNDING TEST TRADE!")
        print(f"This will use ${position_size:.2f} of your ${usdt_balance:.2f} available USDT")
        print(f"Risk: ${risk_amount:.2f} | Reward: ${reward_amount:.2f}")
        
        confirm = input("Type 'EXECUTE' to proceed or anything else to cancel: ")
        if confirm != 'EXECUTE':
            print("❌ Trade cancelled by user")
            return
        
        # Step 7: Execute entry order
        print(f"\n🚀 EXECUTING ENTRY ORDER...")
        
        quantity_str = f"{btc_quantity:.8f}".rstrip('0').rstrip('.')
        
        buy_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='BUY',
            type='MARKET',
            quantity=quantity_str
        )
        
        entry_order_id = buy_order['orderId']
        print(f"🎉 ENTRY ORDER EXECUTED: {entry_order_id}")
        
        # Wait and get execution details
        time.sleep(2)
        
        order_details = binance.client.get_order(symbol='BTCUSDT', orderId=entry_order_id)
        actual_quantity = float(order_details['executedQty'])
        
        # Calculate actual fill price
        if 'fills' in buy_order and buy_order['fills']:
            total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in buy_order['fills'])
            actual_entry_price = total_cost / actual_quantity
        else:
            actual_entry_price = current_price
        
        actual_cost = actual_quantity * actual_entry_price
        
        # Recalculate based on actual execution
        actual_risk_amount = actual_cost * risk_percent
        actual_reward_amount = actual_cost * reward_percent
        actual_stop_loss = round(actual_entry_price * (1 - risk_percent), 2)
        actual_take_profit = round(actual_entry_price * (1 + reward_percent), 2)
        
        print(f"\n📊 ACTUAL EXECUTION:")
        print(f"  Entry Price: ${actual_entry_price:.2f}")
        print(f"  Quantity: {actual_quantity:.8f} BTC")
        print(f"  Cost: ${actual_cost:.2f}")
        print(f"  SL: ${actual_stop_loss:.2f} (${actual_risk_amount:.2f})")
        print(f"  TP: ${actual_take_profit:.2f} (${actual_reward_amount:.2f})")
        
        # Step 8: Place exit orders
        print(f"\n🎯 PLACING EXIT ORDERS...")
        
        quantity_str = f"{actual_quantity:.8f}".rstrip('0').rstrip('.')
        tp_price_str = f"{actual_take_profit:.2f}"
        sl_price_str = f"{actual_stop_loss:.2f}"
        sl_limit_str = f"{actual_stop_loss * 0.999:.2f}"
        
        # Place Take Profit order
        tp_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='SELL',
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity_str,
            price=tp_price_str
        )
        
        # Place Stop Loss order
        sl_order = binance.client.create_order(
            symbol='BTCUSDT',
            side='SELL',
            type='STOP_LOSS_LIMIT',
            timeInForce='GTC',
            quantity=quantity_str,
            price=sl_limit_str,
            stopPrice=sl_price_str
        )
        
        tp_order_id = tp_order['orderId']
        sl_order_id = sl_order['orderId']
        
        print(f"🎉 EXIT ORDERS PLACED!")
        print(f"📋 Take Profit Order: {tp_order_id}")
        print(f"📋 Stop Loss Order: {sl_order_id}")
        
        # Step 9: Send Telegram notification
        if telegram_bot:
            trade_message = f"""
🎉 COMPOUNDING TEST TRADE EXECUTED!

📋 ALL ORDER NUMBERS FOR BINANCE APP:
Entry: {entry_order_id}
Take Profit: {tp_order_id}
Stop Loss: {sl_order_id}

📊 TRADE DETAILS:
- Entry: ${actual_entry_price:.2f}
- TP: ${actual_take_profit:.2f} (${actual_reward_amount:.2f})
- SL: ${actual_stop_loss:.2f} (${actual_risk_amount:.2f})
- Risk-Reward: {actual_reward_amount/actual_risk_amount:.1f}:1

💰 COMPOUNDING STATUS:
- Position Size: ${actual_cost:.2f}
- Balance Utilization: {(actual_cost/current_balance)*100:.1f}%

🔄 MONITORING UNTIL COMPLETION
Real money compounding test trade active! 💰
"""
            telegram_bot.send_message(trade_message)
        
        # Step 10: Display final status
        print(f"\n🔄 COMPOUNDING TEST TRADE IS NOW ACTIVE")
        print(f"="*80)
        print(f"📋 ALL ORDER NUMBERS FOR BINANCE APP:")
        print(f"  Entry Order: {entry_order_id}")
        print(f"  Take Profit: {tp_order_id}")
        print(f"  Stop Loss: {sl_order_id}")
        print(f"")
        print(f"📊 COMPOUNDING TRADE SUMMARY:")
        print(f"  Entry Price: ${actual_entry_price:.2f}")
        print(f"  Position Size: ${actual_cost:.2f}")
        print(f"  Risk: ${actual_risk_amount:.2f} (1%)")
        print(f"  Reward: ${actual_reward_amount:.2f} (2.5%)")
        print(f"  Risk-Reward: {actual_reward_amount/actual_risk_amount:.1f}:1")
        print(f"  Balance Utilization: {(actual_cost/current_balance)*100:.1f}%")
        print(f"")
        print(f"✅ COMPOUNDING MONEY MANAGEMENT ENGINE: SETUP CORRECTLY")
        print(f"✅ Real money test trade: EXECUTED")
        print(f"✅ Telegram monitoring: ACTIVE")
        print(f"✅ Risk management: OPERATIONAL")
        print(f"")
        print(f"🚀 SYSTEM IS READY FOR LIVE SIGNALS AFTER TEST COMPLETION!")
        print(f"")
        print(f"The trade will automatically complete when either:")
        print(f"  - Take Profit is hit: ${actual_reward_amount:.2f} profit")
        print(f"  - Stop Loss is hit: ${actual_risk_amount:.2f} loss")
        print(f"")
        print(f"Monitor progress in Binance app or wait for Telegram notifications.")
        
        # Save trade record
        import json
        
        trade_record = {
            'system_type': 'Compounding Real Money System',
            'test_trade_status': 'ACTIVE',
            'timestamp': datetime.now().isoformat(),
            'money_management': {
                'starting_balance_equivalent': starting_balance_equivalent,
                'current_balance': current_balance,
                'compounding_factor': compounding_factor,
                'risk_percent': risk_percent,
                'reward_percent': reward_percent,
                'compounding_enabled': True
            },
            'order_numbers': {
                'entry_order_id': entry_order_id,
                'take_profit_order_id': tp_order_id,
                'stop_loss_order_id': sl_order_id
            },
            'trade_details': {
                'entry_price': actual_entry_price,
                'quantity': actual_quantity,
                'position_size': actual_cost,
                'stop_loss_price': actual_stop_loss,
                'take_profit_price': actual_take_profit,
                'risk_amount': actual_risk_amount,
                'reward_amount': actual_reward_amount,
                'risk_reward_ratio': actual_reward_amount / actual_risk_amount
            },
            'system_status': {
                'money_management_engine': 'SETUP CORRECTLY',
                'real_money_execution': 'CONFIRMED',
                'telegram_monitoring': 'ACTIVE',
                'ready_for_live_signals': 'AFTER TEST COMPLETION'
            }
        }
        
        filename = f'compounding_system_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(filename, 'w') as f:
            json.dump(trade_record, f, indent=2, default=str)
        
        print(f"📄 Trade record saved to: {filename}")
        
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
