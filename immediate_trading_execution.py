#!/usr/bin/env python3
"""
Immediate Trading Execution with 0.1% SL OCO Orders
Then activate full TCN-CNN-PPO system with comprehensive monitoring
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
import threading

# Add module paths
sys.path.append('shared_config')
sys.path.append('01_binance_connector')
sys.path.append('02_signal_generator')
sys.path.append('06_telegram_system')

from secure_credentials import validate_all_credentials
from binance_real_money_connector import BinanceRealMoneyConnector
from enhanced_grid_aware_signal_generator import GridAwareSignalGenerator
from telegram_trading_bot import ComprehensiveTelegramTradingBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)')
logger = logging.getLogger(__name__)

class ImmediateTradingSystem:
    """Immediate execution with OCO orders and TCN-CNN-PPO monitoring"""
    
    def __init__(self):
        self.binance = None
        self.signal_generator = None
        self.telegram = None
        self.test_trade_active = False
        self.test_trade_result = None
        self.tcn_system_active = False
        
        # Trading parameters
        self.account_balance = 600.46
        self.sl_percentage = 0.001  # 0.1% SL
        self.tp_percentage = 0.0025  # 0.25% TP (2.5:1 ratio)
        
    def initialize_system(self):
        """Initialize all system components"""
        try:
            logger.info("🚀 Initializing immediate trading system...")
            
            # Validate credentials
            if not validate_all_credentials():
                return False
            
            # Initialize components
            self.binance = BinanceRealMoneyConnector()
            self.signal_generator = GridAwareSignalGenerator()
            self.telegram = ComprehensiveTelegramTradingBot()
            
            # Get current account balance
            balance_info = self.binance.get_account_balance()
            if balance_info:
                self.account_balance = balance_info['total_usdt_value']
                logger.info(f"✅ Account balance: ${self.account_balance:.2f}")
            
            logger.info("✅ System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    def send_startup_notification(self):
        """Send immediate execution startup notification"""
        try:
            if self.telegram:
                balance_info = self.binance.get_account_balance()
                sl_amount = self.account_balance * self.sl_percentage
                tp_amount = self.account_balance * self.tp_percentage
                
                message = f"""
🚀 **IMMEDIATE TRADING EXECUTION**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 **Account Balance:** ${self.account_balance:.2f}
📊 **BTC Price:** ${balance_info['current_btc_price']:.2f}
🔴 **Stop Loss:** 0.1% = ${sl_amount:.2f}
🟢 **Take Profit:** 0.25% = ${tp_amount:.2f}
⚖️ **Risk-Reward:** 2.5:1
📋 **Order Type:** OCO (One-Cancels-Other)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚡ **EXECUTING IMMEDIATE TRADE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(message)
                
        except Exception as e:
            logger.error(f"❌ Failed to send startup notification: {e}")
    
    def execute_immediate_trade(self):
        """Execute immediate trade with OCO orders"""
        try:
            logger.info("⚡ Executing immediate trade...")
            
            # Get current market data
            market_data = self.binance.get_market_data()
            if market_data is None:
                logger.error("❌ Failed to get market data")
                return False
            
            current_price = market_data['close'].iloc[-1]
            current_rsi = market_data['rsi'].iloc[-1]
            current_vwap = market_data['vwap'].iloc[-1]
            
            # Determine signal based on current market conditions
            if current_rsi < 50:
                signal = 'BUY'
                entry_price = current_price
                stop_loss_price = current_price * (1 - self.sl_percentage)
                take_profit_price = current_price * (1 + self.tp_percentage)
            else:
                signal = 'SELL'
                entry_price = current_price
                stop_loss_price = current_price * (1 + self.sl_percentage)
                take_profit_price = current_price * (1 - self.tp_percentage)
            
            # Calculate position size
            risk_amount = self.account_balance * self.sl_percentage
            price_difference = abs(entry_price - stop_loss_price)
            position_size_usdt = risk_amount / (price_difference / entry_price)
            position_size_btc = position_size_usdt / entry_price
            
            # Send trade execution notification
            if self.telegram:
                trade_message = f"""
⚡ **IMMEDIATE TRADE EXECUTION**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **Signal:** {signal}
💰 **Entry Price:** ${entry_price:.2f}
🔴 **Stop Loss:** ${stop_loss_price:.2f}
🟢 **Take Profit:** ${take_profit_price:.2f}
📊 **Position Size:** {position_size_btc:.6f} BTC
💵 **Position Value:** ${position_size_usdt:.2f}
📈 **RSI:** {current_rsi:.1f}
📊 **VWAP:** ${current_vwap:.2f}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Placing OCO order...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute the trade with OCO order
            trade_result = self.binance.execute_full_trade(
                signal,
                entry_price,
                0.9  # High confidence for immediate execution
            )
            
            if trade_result:
                logger.info("✅ Immediate trade executed successfully")
                self.test_trade_active = True
                
                # Send success notification
                if self.telegram:
                    success_message = f"""
✅ **TRADE EXECUTED SUCCESSFULLY**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Order ID:** {trade_result.get('orderId', 'N/A')}
📋 **Status:** OCO order placed
🎯 **Monitoring:** Active until TP/SL hit
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Waiting for trade completion...**
"""
                    self.telegram.send_message(success_message)
                
                # Start monitoring the trade
                self.monitor_trade_completion(trade_result, signal, entry_price, stop_loss_price, take_profit_price)
                return True
            else:
                logger.error("❌ Immediate trade execution failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Immediate trade execution error: {e}")
            return False
    
    def monitor_trade_completion(self, trade_result, signal, entry_price, sl_price, tp_price):
        """Monitor trade until TP or SL is hit"""
        try:
            logger.info("👁️ Monitoring trade completion...")
            
            # Start monitoring in separate thread
            monitor_thread = threading.Thread(
                target=self._trade_monitoring_loop,
                args=(trade_result, signal, entry_price, sl_price, tp_price)
            )
            monitor_thread.daemon = True
            monitor_thread.start()
            
        except Exception as e:
            logger.error(f"❌ Trade monitoring setup error: {e}")
    
    def _trade_monitoring_loop(self, trade_result, signal, entry_price, sl_price, tp_price):
        """Trade monitoring loop"""
        try:
            start_time = datetime.now()
            max_wait_minutes = 60  # Maximum 1 hour wait
            check_interval = 30  # Check every 30 seconds
            
            while self.test_trade_active:
                try:
                    # Get current price
                    current_price = self.binance.get_current_price()
                    
                    # Check if TP or SL hit
                    trade_completed = False
                    result_type = None
                    pnl = 0
                    
                    if signal == 'BUY':
                        if current_price <= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -(self.account_balance * self.sl_percentage)
                        elif current_price >= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = self.account_balance * self.tp_percentage
                    else:  # SELL
                        if current_price >= sl_price:
                            trade_completed = True
                            result_type = 'STOP_LOSS'
                            pnl = -(self.account_balance * self.sl_percentage)
                        elif current_price <= tp_price:
                            trade_completed = True
                            result_type = 'TAKE_PROFIT'
                            pnl = self.account_balance * self.tp_percentage
                    
                    if trade_completed:
                        self.complete_test_trade(result_type, pnl, current_price)
                        break
                    
                    # Check timeout
                    elapsed_time = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed_time > max_wait_minutes:
                        logger.warning("⏰ Trade monitoring timeout - assuming completion")
                        self.complete_test_trade('TIMEOUT', 0, current_price)
                        break
                    
                    # Send periodic updates
                    if int(elapsed_time) % 5 == 0:  # Every 5 minutes
                        if self.telegram:
                            update_message = f"""
👁️ **TRADE MONITORING UPDATE**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 **Current Price:** ${current_price:.2f}
🎯 **Entry:** ${entry_price:.2f}
🔴 **Stop Loss:** ${sl_price:.2f}
🟢 **Take Profit:** ${tp_price:.2f}
⏰ **Elapsed:** {elapsed_time:.1f} minutes
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: MONITORING ACTIVE**
"""
                            self.telegram.send_message(update_message)
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Monitoring loop error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ Trade monitoring error: {e}")
            self.complete_test_trade('ERROR', 0, 0)
    
    def complete_test_trade(self, result_type, pnl, final_price):
        """Complete test trade and activate TCN-CNN-PPO system"""
        try:
            self.test_trade_active = False
            self.test_result = result_type
            
            # Determine success
            success = result_type in ['TAKE_PROFIT', 'TIMEOUT']
            
            # Send completion notification
            if self.telegram:
                if success:
                    emoji = "✅"
                    status = "SUCCESS"
                    next_action = "🚀 **ACTIVATING TCN-CNN-PPO SYSTEM**"
                else:
                    emoji = "🔴"
                    status = "STOPPED"
                    next_action = "⚠️ **SYSTEM REVIEW REQUIRED**"
                
                completion_message = f"""
{emoji} **TEST TRADE COMPLETED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Result:** {result_type}
💰 **P&L:** ${pnl:+.2f}
📈 **Final Price:** ${final_price:.2f}
📊 **New Balance:** ${self.account_balance + pnl:.2f}
⏰ **Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{next_action}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(completion_message)
            
            if success:
                logger.info("✅ Test trade successful - Activating TCN-CNN-PPO system")
                self.activate_tcn_system()
            else:
                logger.error("❌ Test trade failed - System halted")
                
        except Exception as e:
            logger.error(f"❌ Test completion error: {e}")
    
    def activate_tcn_system(self):
        """Activate full TCN-CNN-PPO system with comprehensive monitoring"""
        try:
            logger.info("🚀 Activating TCN-CNN-PPO trading system...")
            self.tcn_system_active = True
            
            if self.telegram:
                activation_message = f"""
🚀 **TCN-CNN-PPO SYSTEM ACTIVATED**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **Test Trade:** Completed successfully
🧠 **AI Model:** TCN-CNN-PPO Ensemble
📊 **Features:** 135-feature state vector
🎯 **Grid Aware:** 0.25% spacing compliance
📈 **Indicators:** RSI, VWAP, Real-time data
⚖️ **Risk Management:** 0.1% SL, 0.25% TP
🔄 **Compounding:** Enabled
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **LIVE TRADING ACTIVE**
⏰ **Started:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
                self.telegram.send_message(activation_message)
            
            # Start TCN-CNN-PPO monitoring loop
            self.start_tcn_monitoring()
            
        except Exception as e:
            logger.error(f"❌ TCN system activation error: {e}")
    
    def start_tcn_monitoring(self):
        """Start comprehensive TCN-CNN-PPO monitoring"""
        try:
            logger.info("🧠 Starting TCN-CNN-PPO monitoring with grid/sentiment analysis...")
            
            check_count = 0
            while self.tcn_system_active:
                try:
                    check_count += 1
                    logger.info(f"🧠 TCN-CNN-PPO Analysis #{check_count}")
                    
                    # Generate comprehensive signal
                    signal_data = self.signal_generator.generate_signal()
                    
                    # Get market data for additional analysis
                    market_data = self.binance.get_market_data()
                    if market_data is not None:
                        current_price = market_data['close'].iloc[-1]
                        current_rsi = market_data['rsi'].iloc[-1]
                        current_vwap = market_data['vwap'].iloc[-1]
                        
                        # Determine market sentiment
                        if current_rsi < 30:
                            sentiment = "🔴 OVERSOLD"
                        elif current_rsi > 70:
                            sentiment = "🟢 OVERBOUGHT"
                        elif current_price > current_vwap:
                            sentiment = "🔵 BULLISH"
                        else:
                            sentiment = "🟠 BEARISH"
                        
                        # Grid analysis
                        grid_level = self.calculate_grid_level(current_price)
                        grid_status = self.get_grid_status(current_price, signal_data)
                        
                        # Send comprehensive monitoring update
                        if check_count % 5 == 0 and self.telegram:  # Every 5th check
                            monitoring_message = f"""
🧠 **TCN-CNN-PPO SYSTEM MONITORING**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {signal_data.get('confidence', 0):.2f}
📈 **Price:** ${current_price:.2f}
📊 **RSI:** {current_rsi:.1f}
📈 **VWAP:** ${current_vwap:.2f}
{sentiment}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔲 **Grid Level:** {grid_level}
📍 **Grid Status:** {grid_status}
🔍 **Analysis:** #{check_count}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ **STATUS: TCN-CNN-PPO ACTIVE**
"""
                            self.telegram.send_message(monitoring_message)
                        
                        # Execute trade if signal is strong
                        if (signal_data['signal'] != 'HOLD' and 
                            signal_data.get('confidence', 0) > 0.75 and
                            signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL'):
                            
                            self.execute_tcn_trade(signal_data, current_price, sentiment, grid_status)
                    
                    # Wait before next analysis
                    time.sleep(300)  # 5 minutes between checks
                    
                except KeyboardInterrupt:
                    logger.info("🛑 TCN monitoring stopped by user")
                    break
                except Exception as e:
                    logger.error(f"❌ TCN monitoring error: {e}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ TCN monitoring failed: {e}")
    
    def calculate_grid_level(self, current_price):
        """Calculate current grid level"""
        try:
            # Grid spacing of 0.25%
            grid_spacing = 0.0025
            base_price = 100000  # Base reference price
            
            level = int((current_price - base_price) / (base_price * grid_spacing))
            return f"Level {level:+d}"
            
        except Exception as e:
            return "Unknown"
    
    def get_grid_status(self, current_price, signal_data):
        """Get grid compliance status"""
        try:
            if signal_data.get('reason') == 'GRID_COMPLIANT_SIGNAL':
                return "✅ COMPLIANT"
            elif 'GRID' in str(signal_data.get('reason', '')):
                return "⚠️ PARTIAL"
            else:
                return "❌ NON-COMPLIANT"
                
        except Exception as e:
            return "Unknown"
    
    def execute_tcn_trade(self, signal_data, current_price, sentiment, grid_status):
        """Execute trade based on TCN-CNN-PPO signal"""
        try:
            logger.info(f"🧠 Executing TCN-CNN-PPO trade: {signal_data['signal']}")
            
            if self.telegram:
                trade_message = f"""
🧠 **TCN-CNN-PPO TRADE SIGNAL**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 **Signal:** {signal_data['signal']}
🎯 **Confidence:** {signal_data.get('confidence', 0):.2f}
📈 **Price:** ${current_price:.2f}
{sentiment}
📍 **Grid:** {grid_status}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⏳ **Executing trade...**
"""
                self.telegram.send_message(trade_message)
            
            # Execute the trade
            trade_result = self.binance.execute_full_trade(
                signal_data['signal'],
                current_price,
                signal_data.get('confidence', 0.8)
            )
            
            if trade_result:
                logger.info("✅ TCN-CNN-PPO trade executed successfully")
            else:
                logger.error("❌ TCN-CNN-PPO trade execution failed")
                
        except Exception as e:
            logger.error(f"❌ TCN trade execution error: {e}")

def main():
    """Main execution function"""
    print("⚡ IMMEDIATE TRADING EXECUTION - 0.1% SL OCO")
    print("=" * 60)
    print("📋 Phase 1: Immediate trade with OCO orders")
    print("📋 Phase 2: TCN-CNN-PPO system with monitoring")
    print("=" * 60)
    
    # Initialize system
    trading_system = ImmediateTradingSystem()
    
    if not trading_system.initialize_system():
        print("❌ System initialization failed")
        return
    
    # Send startup notification
    trading_system.send_startup_notification()
    
    # Execute immediate trade
    print("⚡ Executing immediate trade...")
    if trading_system.execute_immediate_trade():
        print("✅ Immediate trade executed - monitoring for completion...")
        
        # Keep main thread alive while monitoring
        try:
            while trading_system.test_trade_active or trading_system.tcn_system_active:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 System stopped by user")
            trading_system.test_trade_active = False
            trading_system.tcn_system_active = False
    else:
        print("❌ Immediate trade execution failed")

if __name__ == "__main__":
    main()
