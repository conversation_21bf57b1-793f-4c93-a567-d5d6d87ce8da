#!/usr/bin/env python3
"""
System Logger
Centralized logging system for all modules
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
import json

class SystemLogger:
    """Centralized logging system"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        self.ensure_log_directory()
        self.loggers = {}
        
    def ensure_log_directory(self):
        """Ensure log directory exists"""
        os.makedirs(self.log_dir, exist_ok=True)
    
    def get_logger(self, module_name, log_level="INFO"):
        """Get or create logger for module"""
        if module_name in self.loggers:
            return self.loggers[module_name]
        
        logger = logging.getLogger(module_name)
        logger.setLevel(getattr(logging, log_level))
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f"{module_name}.log"),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        
        # Formatter
        formatter = logging.Formatter(
            f'%(asctime)s - {module_name} - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        self.loggers[module_name] = logger
        return logger
    
    def log_trade(self, trade_data):
        """Log trade information"""
        trade_logger = self.get_logger('trades')
        trade_logger.info(f"TRADE: {json.dumps(trade_data)}")
    
    def log_error(self, module_name, error_message):
        """Log error message"""
        error_logger = self.get_logger('errors')
        error_logger.error(f"{module_name}: {error_message}")
    
    def log_system_event(self, event_type, event_data):
        """Log system event"""
        system_logger = self.get_logger('system')
        system_logger.info(f"{event_type}: {json.dumps(event_data)}")

# Global logger instance
system_logger = SystemLogger()

def get_module_logger(module_name):
    """Get logger for specific module"""
    return system_logger.get_logger(module_name)
