{"system_config": {"name": "Enhanced Grid-Aware TCN-CNN-PPO Trading System", "version": "1.0.0", "deployment_date": "2025-07-14", "compliance_document": "COMPREHENSIVE_DEPLOYMENT_PLAN.md"}, "trading_parameters": {"starting_balance": 100.0, "risk_per_trade": 0.01, "risk_reward_ratio": 2.5, "max_daily_trades": 8, "max_daily_risk": 0.08, "max_drawdown_limit": 0.1, "position_sizing_method": "fixed_percentage", "leverage": 10, "margin_type": "isolated"}, "grid_system": {"grid_spacing": 0.0025, "grid_tolerance": 1e-05, "grid_compliance_mandatory": true, "grid_only_trading": true, "price_precision": 2, "quantity_precision": 6}, "performance_targets": {"win_rate_target": 0.6, "trades_per_day_target": 8.0, "composite_score_target": 0.8, "daily_return_target": 0.012, "sharpe_ratio_target": 1.5, "max_consecutive_losses": 5}, "model_config": {"model_file": "best_real_3year_trained_model.pth", "architecture": "TCN-CNN-PPO", "total_features": 135, "tcn_features": 64, "cnn_features": 64, "grid_features": 7, "confidence_threshold": 0.7, "signal_validation": true}, "binance_config": {"symbol": "BTCUSDT", "order_type": "MARKET", "time_in_force": "GTC", "test_mode": false, "api_rate_limit": 1200, "reconnect_attempts": 5, "connection_timeout": 30}, "risk_management": {"stop_loss_mandatory": true, "take_profit_mandatory": true, "position_monitoring": true, "emergency_shutdown_enabled": true, "drawdown_monitoring": true, "correlation_limits": true, "volatility_adjustment": true}, "monitoring": {"real_time_tracking": true, "performance_logging": true, "trade_logging": true, "error_logging": true, "health_monitoring": true, "alert_system": true, "backup_frequency": "daily"}, "telegram_integration": {"enabled": true, "notifications": {"trade_execution": true, "performance_updates": true, "alerts": true, "system_status": true, "daily_summary": true, "emergency_alerts": true}, "update_frequency": {"status_updates": 3600, "performance_updates": 1800, "health_checks": 300}}, "security": {"pre_execution_scanning": true, "compliance_monitoring": true, "anomaly_detection": true, "access_control": true, "audit_logging": true, "encryption_enabled": true, "backup_encryption": true}, "deployment_stages": {"micro": {"balance_range": [100, 500], "duration_days": 7, "monitoring_level": "basic", "risk_multiplier": 1.0}, "scaled": {"balance_range": [500, 1000], "duration_days": 14, "monitoring_level": "enhanced", "risk_multiplier": 1.0}, "full": {"balance_range": [1000, 10000], "duration_days": 999, "monitoring_level": "complete", "risk_multiplier": 1.0}}, "emergency_protocols": {"auto_shutdown_triggers": {"drawdown_threshold": 0.1, "consecutive_losses": 8, "api_errors": 5, "system_errors": 3, "margin_call": true}, "manual_controls": {"emergency_stop": true, "position_closure": true, "system_pause": true, "risk_reduction": true}, "recovery_procedures": {"auto_restart": false, "manual_approval": true, "system_validation": true, "performance_review": true}}, "logging": {"log_level": "INFO", "log_file": "live_trading.log", "max_log_size": "100MB", "log_rotation": true, "remote_logging": false, "structured_logging": true}, "data_sources": {"primary": "Binance WebSocket", "backup": "Binance REST API", "data_validation": true, "real_time_only": true, "synthetic_data_blocked": true, "data_integrity_checks": true}, "performance_optimization": {"execution_speed": "high", "memory_management": true, "cpu_optimization": true, "network_optimization": true, "caching_enabled": true, "parallel_processing": false}}