{"binance_config": {"api_key": "sZU8MFl0EsmffpDQM4zuUIiicAD9yQ7vvrJvR9wkYblgtAJDDailcsZwkvK90UQY", "api_secret": "7kWjxOFTIHrDUju4VoLDCAP93c3zWFhG3799SEBxUTg88NcBESY9WHjob7xChRXz"}, "trading_config": {"symbol": "BTCUSDT", "margin_type": "ISOLATED", "leverage": 10, "max_leverage": 10, "risk_per_trade": 0.01, "reward_per_trade": 0.025, "grid_spacing": 0.0025, "grid_tolerance": 1e-05, "confidence_threshold": 0.75, "min_trade_interval": 300}, "master_document_compliance": {"starting_balance": 100.0, "risk_reward_ratio": 2.5, "grid_compliance": true, "real_indicators": true, "tcn_cnn_ppo_model": true, "trades_per_day_target": 8.0, "win_rate_target": 0.6, "composite_score": 0.8, "signal_persistence": 300, "grid_only_trading": true, "pre_execution_scanning": true, "telegram_integration": true}}